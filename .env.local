# Firebase production environment variables
VITE_APP_ENVIRONMENT=production
SESSION_SECRET=N92K47NDHeGn/Rjzj8pagZFnlTpxFj31/Ap31tKvRa7HqHlHh4ffAmXwAnQCy0Hnpum9BNSdVGL71oBzAgCKfw==
SUPABASE_URL=https://udkkdwvgobazxnowbtjk.supabase.co
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.W5v47sYl5xWQaJe6tAzB-qICQd4HJaJbE06SBTc4F-k
GOOGLE_APPLICATION_CREDENTIALS=./firebase-service-account.json

# Database connection
# Substitua pela URL correta do banco de dados Neon
DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require

# Usar o pool de conexões do Supabase em vez do Neon direto
USE_SUPABASE_POOL=false


