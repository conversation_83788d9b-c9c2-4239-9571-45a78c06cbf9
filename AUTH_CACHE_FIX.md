# 🔧 Correção do Problema de Cache após Login

## 🚨 **Problema Identificado**

Após fazer login com uma nova conta, o dashboard estava mostrando dados de outra conta, sendo necessário fazer refresh manual para carregar os dados corretos.

### **Causa Raiz:**
O React Query não estava invalidando o cache automaticamente após mudanças de autenticação, mantendo dados antigos em memória.

## ✅ **Solução Implementada**

### **1. Hook de Refresh de Dados (`useDataRefresh.ts`)**

Criado um hook especializado para gerenciar invalidação de cache:

```typescript
export function useDataRefresh() {
  const queryClient = useQueryClient();

  const refreshAllUserData = useCallback(async () => {
    // Invalidar todas as queries principais
    const queriesToInvalidate = [
      ['/api/stores/me'],
      ['/api/dashboard'],
      ['/api/analytics/dashboard'],
      ['/api/subscriptions/usage'],
      ['/api/orders'],
      ['/api/products'],
      ['/api/customers'],
      // ... outras queries
    ];

    // Invalidar queries específicas
    await Promise.all(
      queriesToInvalidate.map(queryKey => 
        queryClient.invalidateQueries({ queryKey })
      )
    );

    // Invalidar queries com padrões específicos
    await queryClient.invalidateQueries({
      predicate: (query) => {
        const key = query.queryKey[0] as string;
        return key.startsWith('/api/orders/') || 
               key.startsWith('/api/products/') ||
               // ... outros padrões
      }
    });

    // Forçar refetch das queries críticas
    await Promise.all(
      criticalQueries.map(queryKey => 
        queryClient.refetchQueries({ queryKey, type: 'active' })
      )
    );
  }, [queryClient]);

  return { refreshAllUserData, clearAllData, refreshEntityData };
}
```

### **2. Integração com Firebase Auth (`useFirebaseAuth.ts`)**

Atualizado o hook de autenticação para usar o novo sistema de refresh:

```typescript
export function useFirebaseAuth() {
  const { refreshAllUserData, clearAllData } = useDataRefresh();

  // Invalidação automática após login
  const signInWithGoogle = async () => {
    // ... código de login
    await apiRequest("POST", "/api/auth/sync-user", userData);
    
    // 🔄 INVALIDAR CACHE APÓS SINCRONIZAÇÃO
    await refreshAllUserData();
    
    setUser(formatUser(firebaseUser));
    // ... resto do código
  };

  // Mesmo para signInWithEmail e signUpWithEmail
  
  // Limpeza de cache no logout
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      if (firebaseUser) {
        // Login detectado
        setUser(formatUser(firebaseUser));
        await apiRequest("POST", "/api/auth/sync-user", userData);
        await refreshAllUserData(); // 🔄 INVALIDAR CACHE
      } else {
        // Logout detectado
        setUser(null);
        clearAllData(); // 🗑️ LIMPAR TODO O CACHE
      }
    });
    return () => unsubscribe();
  }, [refreshAllUserData, clearAllData]);
}
```

### **3. Componente de Debug (`AuthDataDebug.tsx`)**

Criado componente temporário para monitorar a sincronização de dados:

```typescript
export function AuthDataDebug() {
  const { user, isAuthenticated } = useAuth();
  const { store, isLoading: storeLoading } = useStore();
  const { subscription, usageInfo, isLoading: subscriptionLoading } = useSubscription();
  const { refreshAllUserData } = useDataRefresh();

  return (
    <Card>
      <CardContent>
        {/* Exibe dados do usuário, loja e assinatura */}
        {/* Botão para forçar refresh manual */}
        <Button onClick={refreshAllUserData}>
          Forçar Refresh
        </Button>
      </CardContent>
    </Card>
  );
}
```

## 🔄 **Fluxo de Invalidação**

### **Quando o Cache é Invalidado:**

1. **Login com Google:**
   - `signInWithGoogle()` → sync user → `refreshAllUserData()`

2. **Login com Email:**
   - `signInWithEmail()` → sync user → `refreshAllUserData()`

3. **Registro:**
   - `signUpWithEmail()` → sync user → `refreshAllUserData()`

4. **Mudança de Estado de Auth:**
   - `onAuthStateChanged()` → sync user → `refreshAllUserData()`

5. **Logout:**
   - `onAuthStateChanged()` → `clearAllData()`

### **Queries Invalidadas:**

- **Dados do Usuário:** `/api/stores/me`
- **Dashboard:** `/api/dashboard`, `/api/analytics/dashboard`
- **Assinatura:** `/api/subscriptions/usage`
- **Entidades:** `/api/orders`, `/api/products`, `/api/customers`
- **Padrões:** Todas as queries que começam com `/api/orders/`, `/api/products/`, etc.

## 🧪 **Como Testar**

### **1. Teste Manual:**
1. Faça login com uma conta
2. Observe os dados no dashboard
3. Faça logout
4. Faça login com outra conta
5. ✅ Os dados devem ser atualizados automaticamente

### **2. Componente de Debug:**
- Temporariamente adicionado ao dashboard
- Mostra dados do usuário, loja e assinatura em tempo real
- Botão para forçar refresh manual
- Indicadores de loading

### **3. Console do Navegador:**
```
🔄 Forçando refresh completo dos dados do usuário...
✅ Refresh completo dos dados concluído
```

## 📋 **Arquivos Modificados**

- ✅ `client/src/hooks/useDataRefresh.ts` - **NOVO** Hook de refresh
- ✅ `client/src/hooks/useFirebaseAuth.ts` - Integração com refresh
- ✅ `client/src/components/debug/AuthDataDebug.tsx` - **NOVO** Debug component
- ✅ `client/src/components/admin/dashboard/DashboardMVP.tsx` - Debug temporário

## 🎯 **Resultado**

### **Antes:**
- ❌ Login com nova conta mostrava dados antigos
- ❌ Necessário refresh manual da página
- ❌ Cache não era invalidado automaticamente

### **Depois:**
- ✅ Login com nova conta carrega dados corretos automaticamente
- ✅ Cache é invalidado em todos os pontos de autenticação
- ✅ Logout limpa completamente o cache
- ✅ Fallback manual disponível via botão de debug

## 🔮 **Próximos Passos**

1. **Remover componente de debug** após confirmação de funcionamento
2. **Monitorar performance** da invalidação de cache
3. **Otimizar queries** se necessário (invalidação seletiva)
4. **Adicionar logs** para auditoria de invalidação

A correção garante que os dados sejam sempre consistentes com o usuário autenticado, eliminando a necessidade de refresh manual.
