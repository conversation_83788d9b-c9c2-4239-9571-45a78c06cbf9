# 🧪 Guia de Teste - Sincronização de Dados após Login

## 🎯 **Objetivo**
Testar se o problema de sincronização de dados após login foi corrigido, garantindo que os dados do dashboard sejam atualizados automaticamente quando um usuário diferente faz login.

## 🔧 **Preparação para Teste**

### **1. Contas de Teste Necessárias:**
- **Conta A**: Uma conta Google ou email já existente
- **Conta B**: Uma conta Google ou email diferente da Conta A

### **2. Dados de Teste:**
- Cada conta deve ter dados diferentes (nome da loja, produtos, pedidos)
- Se não tiver dados, criar alguns produtos/pedidos para diferenciar

## 📋 **Cenários de Teste**

### **Teste 1: Login → Logout → Login com Conta Diferente**

1. **Acesse:** `http://localhost:3000/login`
2. **Faça login** com a Conta A
3. **Observe o dashboard** - anote os dados exibidos (nome da loja, produtos, etc.)
4. **Faça logout**
5. **Faça login** com a Conta B
6. **✅ VERIFICAR:** Os dados devem ser da Conta B automaticamente

### **Teste 2: Troca Direta de Contas (se aplicável)**

1. **Estando logado** com a Conta A
2. **Abra nova aba** e faça login com Conta B
3. **Volte para a aba original**
4. **✅ VERIFICAR:** Os dados devem ser atualizados para a Conta B

### **Teste 3: Refresh Manual vs Automático**

1. **Faça login** com a Conta A
2. **Anote os dados** do dashboard
3. **Faça logout e login** com a Conta B
4. **✅ VERIFICAR:** Dados atualizados automaticamente (sem refresh manual)
5. **Teste refresh manual** (F5) - dados devem permanecer da Conta B

### **Teste 4: Diferentes Tipos de Dados**

Verificar se TODOS os tipos de dados são atualizados:
- ✅ **Nome da loja** no header/sidebar
- ✅ **Dados do dashboard** (estatísticas, gráficos)
- ✅ **Lista de produtos**
- ✅ **Lista de pedidos**
- ✅ **Lista de clientes**
- ✅ **Informações de assinatura**

## 🔍 **Monitoramento via Debug**

### **Componente de Debug Temporário:**
O dashboard agora inclui um componente de debug que mostra:

```
Debug Info:
- Refresh manual: 0x
- Último: --:--:--
- Usuário mudou: ✅ Sim / ❌ Não
- Usuário atual: [user-id]
- Usuário anterior: [previous-user-id]
```

### **Console do Navegador:**
Abra o DevTools (F12) e monitore as mensagens:

```
🔄 Auth state changed: User [user-id]
👤 Usuário logado detectado - limpando cache antigo...
🗑️ Cache antigo removido
🔄 Sincronizando usuário com banco de dados...
✅ Usuário sincronizado, invalidando cache...
🔄 Forçando refresh completo dos dados do usuário...
✅ Refresh completo dos dados concluído
```

## ✅ **Critérios de Sucesso**

### **ANTES (Problema):**
- ❌ Dashboard mostrava dados da conta anterior
- ❌ Necessário refresh manual (F5) para ver dados corretos
- ❌ Cache não era invalidado automaticamente

### **DEPOIS (Corrigido):**
- ✅ Dashboard mostra dados da nova conta automaticamente
- ✅ Não é necessário refresh manual
- ✅ Cache é invalidado e recarregado automaticamente
- ✅ Logs no console confirmam o processo de limpeza/reload

## 🚨 **Problemas a Reportar**

Se algum dos testes falhar, reportar:

1. **Cenário específico** que falhou
2. **Dados esperados** vs **dados exibidos**
3. **Logs do console** (copiar mensagens relevantes)
4. **Informações do debug component**
5. **Passos para reproduzir**

## 🔧 **Ferramentas de Debug**

### **Botão "Forçar Refresh":**
- Use apenas se os dados não atualizarem automaticamente
- Deve resolver o problema temporariamente
- Se necessário usar, indica que a correção automática falhou

### **Logs Detalhados:**
```javascript
// No console, você pode forçar um refresh manual:
window.location.reload();

// Ou limpar o cache do React Query:
// (disponível apenas em desenvolvimento)
```

## 📊 **Métricas de Performance**

- **Tempo de sincronização:** Deve ser < 2 segundos
- **Número de requests:** Mínimo necessário (não deve fazer requests desnecessários)
- **UX:** Transição suave sem "flicker" de dados antigos

## 🎯 **Resultado Esperado**

Após a correção, a troca de usuários deve ser:
- ✅ **Automática** - sem intervenção manual
- ✅ **Rápida** - dados atualizados em segundos
- ✅ **Confiável** - funciona 100% das vezes
- ✅ **Completa** - todos os dados são atualizados
- ✅ **Transparente** - usuário não percebe o processo técnico
