# Guia Técnico - Doce Menu

## Visão Geral da Arquitetura

O Doce Menu é uma aplicação web full-stack desenvolvida com uma arquitetura moderna baseada em JavaScript/TypeScript. O projeto segue uma estrutura monorepo, onde o frontend e o backend compartilham o mesmo repositório, mas são claramente separados em diretórios distintos.

### Estrutura do Projeto

```
/
├── client/               # Aplicação frontend React
│   ├── src/              # Código fonte do frontend
│   │   ├── components/   # Componentes React reutilizáveis
│   │   ├── context/      # Contextos React para gerenciamento de estado
│   │   ├── hooks/        # Hooks personalizados
│   │   ├── lib/          # Utilitários e configurações
│   │   ├── pages/        # Componentes de página
│   │   └── main.tsx      # Ponto de entrada do frontend
│   └── index.html        # Template HTML principal
├── server/               # Aplicação backend Express
│   ├── routes.ts         # Definição de rotas da API
│   ├── storage.ts        # Interface de acesso ao banco de dados
│   ├── storage.supabase.ts # Implementação Supabase do storage
│   └── index.ts          # Ponto de entrada do servidor
├── shared/               # Código compartilhado entre frontend e backend
│   └── schema.ts         # Definições de schema do banco de dados
├── migrations/           # Migrações de banco de dados
└── public/               # Arquivos estáticos
```

## Stack Tecnológico

### Frontend

- **Framework**: React 18+
- **Linguagem**: TypeScript
- **Roteamento**: Wouter (alternativa leve ao React Router)
- **Gerenciamento de Estado**:
  - Context API do React para estado global
  - React Query para gerenciamento de estado do servidor e cache
- **UI/Componentes**:
  - Tailwind CSS para estilização
  - shadcn/ui como biblioteca de componentes (baseada em Radix UI)
- **Internacionalização**: react-i18next
- **Formulários**: react-hook-form com Zod para validação
- **Bundler**: Vite

### Backend

- **Framework**: Express.js
- **Linguagem**: TypeScript
- **ORM**: Drizzle ORM
- **Banco de Dados**: PostgreSQL via Supabase
- **Autenticação**: Firebase Authentication
- **Armazenamento de Arquivos**: Supabase Storage

### Infraestrutura

- **Hospedagem**: Firebase Hosting
- **Ambiente de Desenvolvimento**: Replit
- **CI/CD**: Scripts de deploy automatizados

### Ambiente de Desenvolvimento

O projeto está configurado para desenvolvimento no Replit, uma plataforma de desenvolvimento colaborativa baseada em navegador:

- **Configuração Replit**: Definida no arquivo `.replit`
- **Módulos**: Node.js 20, PostgreSQL 16, Web
- **Comando de Execução**: `npm run dev`
- **Porta de Desenvolvimento**: 3000 (mapeada para 5000 em produção)

### Implantação

O processo de implantação utiliza Firebase Hosting e é automatizado através de scripts:

- **Script de Implantação**: `deploy.sh`
- **Processo**:
  1. Copia variáveis de ambiente de `.env.firebase` para `.env.production`
  2. Compila o frontend com Vite
  3. Compila o backend com esbuild
  4. Otimiza os arquivos para implantação
  5. Implanta no Firebase Hosting

### Variáveis de Ambiente

O projeto utiliza as seguintes variáveis de ambiente:

- `DATABASE_URL`: String de conexão PostgreSQL
- `SUPABASE_URL`: URL do projeto Supabase
- `VITE_SUPABASE_ANON_KEY`: Chave anônima do Supabase (frontend)
- `SUPABASE_SERVICE_ROLE_KEY`: Chave de serviço do Supabase (apenas backend)
- `VITE_APP_BASE_URL`: URL base da aplicação
- `SESSION_SECRET`: Segredo para sessões
- `GOOGLE_APPLICATION_CREDENTIALS`: Caminho para credenciais do Firebase

## Padrões de Desenvolvimento

### Gerenciamento de Estado

O aplicativo utiliza uma combinação de abordagens para gerenciamento de estado:

1. **Context API do React**: Para estado global da aplicação
   - `StoreContext`: Gerencia dados da loja atual
   - `CartContext`: Gerencia o carrinho de compras
   - `FirebaseAuthContext`: Gerencia autenticação
   - `AdminOrderContext`: Gerencia criação de pedidos no painel admin

2. **React Query**: Para estado do servidor e cache
   - Configurado com `QueryClient` personalizado
   - Implementa tratamento de erros e autenticação
   - Gerencia cache de dados com invalidação inteligente

3. **Estado Local**: Para componentes individuais
   - Utiliza `useState` e `useReducer` do React

### Autenticação

O sistema utiliza Firebase Authentication para gerenciamento de usuários:

- Suporte para login com email/senha e Google
- Integração com backend para sincronização de dados de usuário
- Middleware de autenticação para proteger rotas da API
- Hook personalizado `useFirebaseAuth` para acesso ao estado de autenticação

### Acesso a Dados

O acesso a dados segue um padrão de abstração:

1. **Frontend**:
   - Utiliza React Query para buscar dados da API
   - Função `apiRequest` centralizada para todas as chamadas HTTP
   - Hooks personalizados para operações específicas

2. **Backend**:
   - Interface `IStorage` define operações de acesso a dados
   - Implementação concreta `SupabaseStorage` utiliza Supabase
   - Rotas Express mapeiam requisições HTTP para operações de storage

### Modelo de Dados

O sistema utiliza um modelo relacional implementado no PostgreSQL via Supabase:

#### Principais Entidades

- **Users**: Usuários do sistema (administradores de lojas)
  - Integrado com Firebase Auth (campo `firebaseUid`)

- **Stores**: Lojas virtuais
  - Configurações visuais (cores, logo)
  - Configurações de pagamento e entrega
  - Informações de contato e endereço

- **Products**: Produtos disponíveis nas lojas
  - Imagens, preço, descrição
  - Associação com categorias

- **ProductVariations**: Tipos de variação de produtos
  - Ex: Tamanho, Cor, Sabor

- **VariationOptions**: Opções específicas para cada variação
  - Ex: P, M, G para Tamanho
  - Preços adicionais para cada opção

- **Customers**: Clientes que fazem pedidos
  - Informações de contato e endereço

- **Orders**: Pedidos realizados
  - Status, valores, método de pagamento
  - Informações de entrega/retirada

- **OrderItems**: Itens individuais de cada pedido
  - Produto, quantidade, preço
  - Variações selecionadas

- **OrderRevisions**: Revisões de pedidos
  - Histórico de alterações em pedidos
  - Mantém integridade dos dados originais

- **Coupons**: Cupons de desconto
  - Tipo (valor fixo ou percentual)
  - Regras de aplicação e validade

#### Relacionamentos

- Uma loja pertence a um usuário
- Uma loja tem muitos produtos
- Um produto pertence a uma categoria
- Um produto pode ter múltiplas variações
- Uma variação tem múltiplas opções
- Um pedido pertence a uma loja e a um cliente
- Um pedido tem múltiplos itens
- Um pedido pode ter múltiplas revisões

### Componentes UI

A interface de usuário segue princípios de design system:

- Componentes base de shadcn/ui (Button, Input, Card, etc.)
- Componentes compostos específicos da aplicação
- Estilização consistente com Tailwind CSS
- Suporte a temas e personalização de cores

## Principais Funcionalidades e Implementação

### Sistema Multi-loja

Cada loja possui:
- URL personalizada (slug)
- Configurações visuais (cores, logo)
- Catálogo de produtos próprio
- Configurações de pagamento e entrega

Implementação:
- Tabela `stores` no banco de dados
- Contexto `StoreContext` para acesso aos dados da loja atual
- Rotas parametrizadas por slug da loja

### Internacionalização

O sistema suporta múltiplos idiomas:

- Português (padrão) e Inglês
- Traduções armazenadas em arquivos JSON
- Hook personalizado `useTranslation` para acesso às traduções
- Persistência da preferência de idioma no localStorage

### Gerenciamento de Pedidos

Sistema completo de pedidos com:

- Criação de pedidos pelo cliente ou admin
- Sistema de revisão de pedidos
- Cálculo automático de valores
- Geração de PDF para impressão

Implementação:
- Tabelas `orders`, `order_items`, `order_revisions`
- Contexto `AdminOrderContext` para criação de pedidos
- Biblioteca jsPDF para geração de documentos

### Produtos e Variações

Sistema flexível de produtos com:

- Categorias para organização
- Variações de produtos (tamanho, cor, etc.)
- Opções personalizáveis com preços adicionais
- Produtos personalizados para pedidos específicos

Implementação:
- Tabelas `products`, `product_variations`, `variation_options`
- Componentes específicos para seleção de variações
- Lógica de cálculo de preço baseada em variações selecionadas

## Padrões de Código

### Convenções de Nomenclatura

- **Componentes React**: PascalCase (ex: `ProductCard.tsx`)
- **Hooks**: camelCase com prefixo "use" (ex: `useCart.ts`)
- **Contextos**: PascalCase com sufixo "Context" (ex: `StoreContext.tsx`)
- **Utilitários**: camelCase (ex: `formatCurrency.ts`)
- **Tipos/Interfaces**: PascalCase (ex: `interface OrderItem {}`)

### Estrutura de Componentes

Os componentes seguem uma estrutura consistente:

```tsx
// Imports
import React from 'react';
import { useTranslation } from '@/hooks/useTranslation';

// Types
interface ComponentProps {
  // props...
}

// Component
export function Component({ prop1, prop2 }: ComponentProps) {
  // Hooks
  const { t } = useTranslation();

  // State & Effects

  // Handlers

  // Render
  return (
    // JSX
  );
}
```

### Estilização

O projeto utiliza Tailwind CSS com algumas convenções:

- Classes utilitárias diretamente nos elementos
- Função `cn()` para combinar classes condicionalmente
- Variáveis CSS para temas e cores personalizadas
- Componentes shadcn/ui como base para UI consistente

## Estrutura de Rotas e APIs

### Rotas do Frontend

O frontend utiliza o roteador Wouter para navegação:

- **Rotas Públicas**:
  - `/`: Página inicial (landing page)
  - `/login`: Página de login
  - `/register`: Página de registro
  - `/store/:slug`: Página da loja (catálogo de produtos)
  - `/store/product/:id`: Detalhes do produto
  - `/store/cart`: Carrinho de compras

- **Rotas Administrativas**:
  - `/admin`: Dashboard administrativo
  - `/admin/products`: Gerenciamento de produtos
  - `/admin/orders`: Gerenciamento de pedidos
  - `/admin/customers`: Gerenciamento de clientes
  - `/admin/settings`: Configurações da loja
  - `/admin/categories`: Gerenciamento de categorias
  - `/admin/coupons`: Gerenciamento de cupons

### Endpoints da API

O backend expõe os seguintes endpoints principais:

- **Autenticação**:
  - `POST /api/auth/sync-user`: Sincroniza dados do usuário Firebase com o banco de dados
  - `GET /api/auth/user`: Obtém dados do usuário autenticado

- **Lojas**:
  - `GET /api/stores/me`: Obtém a loja do usuário autenticado
  - `POST /api/stores`: Cria uma nova loja
  - `PATCH /api/stores/:id`: Atualiza uma loja existente
  - `GET /api/public/stores/:slug`: Obtém uma loja pelo slug (acesso público)

- **Produtos**:
  - `GET /api/products`: Lista produtos da loja
  - `POST /api/products`: Cria um novo produto
  - `PATCH /api/products/:id`: Atualiza um produto existente
  - `DELETE /api/products/:id`: Remove um produto

- **Pedidos**:
  - `GET /api/orders`: Lista pedidos da loja
  - `POST /api/admin/orders`: Cria um novo pedido (admin)
  - `GET /api/orders/:id`: Obtém detalhes de um pedido
  - `PATCH /api/orders/:id/status`: Atualiza o status de um pedido
  - `POST /api/orders/:id/revisions`: Cria uma revisão de pedido

- **Clientes**:
  - `GET /api/customers`: Lista clientes da loja
  - `POST /api/customers`: Cria um novo cliente
  - `GET /api/customers/:id`: Obtém detalhes de um cliente
  - `PATCH /api/customers/:id`: Atualiza um cliente existente

- **Cupons**:
  - `GET /api/admin/cupons`: Lista cupons da loja
  - `POST /api/admin/cupons`: Cria um novo cupom
  - `GET /api/public/stores/:slug/validate-coupon`: Valida um cupom (acesso público)

## Boas Práticas e Recomendações

1. **Supabase**: Use o cliente Supabase exclusivamente para operações de banco de dados em vez de conexões PostgreSQL diretas.

2. **Internacionalização**: Adicione traduções para todas as novas chaves de texto no sistema, tanto em português quanto em inglês.

3. **Commits**: Não faça commits automaticamente - sempre pergunte ao usuário antes.

4. **Valores Monetários**: Use o símbolo de moeda da configuração da loja em todos os valores.

5. **Interface Mobile**: Considere a experiência em dispositivos iOS ao implementar ou modificar componentes.

6. **Identidade Visual**: Adicione logo e cores da loja ao implementar elementos visuais.

7. **Testes**: Implemente testes para novas funcionalidades, especialmente para lógica de negócios crítica.

8. **Segurança**: Valide todas as entradas de usuário tanto no frontend quanto no backend.

9. **Performance**: Otimize imagens e minimize requisições de rede desnecessárias.

10. **Acessibilidade**: Mantenha os componentes acessíveis seguindo as diretrizes do shadcn/ui.
