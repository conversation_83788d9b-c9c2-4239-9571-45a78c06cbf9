# Guia do Usuário - Doce Menu

## Visão Geral

O Doce Menu é uma plataforma multilíngue de e-commerce projetada para criar e gerenciar catálogos de produtos online com recursos avançados. Cada loja possui uma URL personalizada (app.docemenu.com.br/[nome-da-loja]) e um painel administrativo para gerenciar produtos, pedidos e clientes.

Este guia contém instruções detalhadas sobre como utilizar todas as funcionalidades do sistema, desde a configuração inicial da loja até o gerenciamento diário de pedidos, produtos e clientes.

## Principais Funcionalidades

- **Suporte Multilíngue**: Disponível em português e inglês
- **Gerenciamento de Loja**: Crie e personalize sua loja online
- **Catálogo de Produtos**: Gerencie produtos e categorias
- **Processamento de Pedidos**: Acompanhe e gerencie pedidos de clientes
- **Banco de Dados de Clientes**: Mantenha um registro de seus clientes
- **Painel Analítico**: Visualize dados mensais de visitas e vendas
- **URLs Personalizadas**: Cada loja recebe uma URL única (app.docemenu.com.br/[nome-da-loja])
- **Personalização de Tema**: Personalize cores e identidade visual

## Configurações da Loja

### Informações Básicas

- **Nome da Loja**: Define o nome principal exibido para os clientes
- **Descrição**: Breve descrição da loja exibida na página inicial
- **URL Personalizada**: Define o endereço único da loja (app.docemenu.com.br/[slug])
- **Logo**: Imagem da marca exibida no cabeçalho e documentos
- **Imagem de Cabeçalho**: Banner exibido na página inicial da loja

### Personalização Visual

- **Cor Primária**: Cor principal usada em botões e elementos de destaque
- **Cor Secundária**: Cor complementar usada em elementos secundários
- **Cor de Destaque**: Cor usada para chamar atenção para elementos específicos
- **Moeda**: Símbolo de moeda usado em todos os valores monetários (ex: R$)

### Informações de Contato

- **Código do País**: Prefixo internacional para números de telefone (ex: +55)
- **WhatsApp**: Número para contato via WhatsApp
- **Instagram**: Nome de usuário do Instagram da loja
- **Email de Contato**: Email para contato com a loja
- **Endereço Completo**: Rua, número, complemento, bairro, cidade e estado

### Métodos de Pagamento

Configure quais métodos de pagamento sua loja aceita através da seção "Pagamentos" nas configurações:

#### Métodos Padrão
- **Dinheiro**: Pagamento em espécie
- **Cartão de Crédito**: Pagamento com cartão de crédito
- **Cartão de Débito**: Pagamento com cartão de débito
- **PIX**: Sistema de pagamento instantâneo brasileiro
- **Transferência Bancária**: Transferência entre contas bancárias

#### Métodos Personalizados
- Adicione métodos específicos da sua loja (ex: Boleto, Cheque, Fiado)
- Validação automática para evitar métodos duplicados
- Interface traduzida em português e inglês

#### Configuração
- Marque/desmarque os métodos que sua loja aceita
- Adicione métodos personalizados usando o campo de texto
- Todos os métodos configurados aparecerão nas opções de pagamento dos pedidos

### Configurações de Entrega

- **Permitir Entrega**: Habilita a opção de entrega
- **Permitir Retirada**: Habilita a opção de retirada no local
- **Taxa de Entrega**: Valor cobrado pela entrega
- **Dias e Horários**: Configure dias e horários disponíveis para entrega e retirada
- **Dias Mínimos de Antecedência**: Tempo mínimo para fazer um pedido
- **Períodos Indisponíveis**: Configure períodos de férias ou feriados

## Gerenciamento de Produtos

### Lista de Produtos

- **Visualização**: Produtos organizados por categorias em cards ou lista
- **Pesquisa**: Busque produtos pelo nome
- **Ordenação**: Organize produtos por nome, preço ou data de criação
- **Filtragem**: Filtre produtos por categoria ou disponibilidade

### Adicionar/Editar Produtos

- **Informações Básicas**: Nome, descrição, preço e categoria
- **Imagens**: Até 3 imagens por produto
- **Disponibilidade**: Marque produtos como disponíveis ou indisponíveis
- **Variações**: Adicione opções como tamanho, cor, sabor, etc.

### Variações de Produtos

- **Tipos de Variação**: Crie tipos como "Tamanho", "Sabor", "Cor", etc.
- **Opções**: Adicione opções para cada tipo (ex: P, M, G para Tamanho)
- **Preços Adicionais**: Defina valores extras para cada opção
- **Opção "Outros"**: Permite adicionar texto, valor e quantidade personalizados

### Categorias

- **Organização**: Agrupe produtos em categorias para facilitar a navegação
- **Visibilidade**: Controle quais categorias são visíveis na loja
- **Ordenação**: Arraste e solte para reordenar categorias

## Gerenciamento de Pedidos

### Lista de Pedidos

- **Visualização**: Pedidos exibidos em cards ou lista com informações principais
- **Pesquisa**: Busque pedidos por ID, cliente, status ou período
- **Filtragem**: Filtre por status, método de pagamento ou data
- **Status**: Acompanhe o status de cada pedido (pendente, confirmado, em preparo, etc.)

### Detalhes do Pedido

- **Informações do Cliente**: Nome, telefone, email e endereço
- **Itens do Pedido**: Lista de produtos, quantidades, variações e observações
- **Valores**: Subtotal, taxa de entrega, desconto e total
- **Método de Pagamento**: Forma de pagamento escolhida
- **Método de Recebimento**: Entrega ou retirada
- **Data e Hora**: Data e horário de recebimento
- **Observações**: Notas adicionais sobre o pedido

### Revisão de Pedidos

- **Criar Revisão**: Modifique pedidos existentes sem perder o histórico
- **Editar Informações**: Altere datas, métodos de pagamento, status, etc.
- **Gerenciar Itens**:
  - Adicione novos produtos ao pedido com o botão "Adicionar Produto"
  - Edite produtos existentes com o ícone de edição ao lado de cada item
  - Remova produtos individualmente com o ícone de lixeira
  - Modifique quantidades e preços base de cada produto
  - Adicione ou remova variações de produtos
- **Ajustar Valores**:
  - Altere preços, taxa de entrega e descontos
  - Recálculo automático do total com base nas alterações
  - Para descontos percentuais, o valor monetário é recalculado mantendo o percentual original
- **Alterar Cliente**: Funcionalidade para trocar o cliente associado ao pedido
- **Histórico**: Mantenha um registro de todas as alterações feitas
- **Visualização**: Informações de revisão exibidas no rodapé do PDF em vez do cabeçalho

### Impressão de Pedidos

- **Visualização Prévia**: Veja como o pedido ficará antes de imprimir
- **Geração de PDF**: Crie PDFs para impressão ou envio digital
- **Personalização**: PDFs incluem logo e cores da loja
- **Formatos**: Suporte para impressão em A4 e impressoras térmicas (80mm)
- **Layout de Impressão**:
  - Design compacto com separação clara de seções
  - Tipografia hierárquica para melhor legibilidade
  - Cores sutis com indicadores de status
  - Fontes sans-serif para melhor legibilidade
  - Variações de produtos com recuo
  - Colunas alinhadas para fácil leitura
  - Totais destacados
  - Informações de revisão no rodapé
  - Contatos da loja (Instagram e WhatsApp) no rodapé

## Gerenciamento de Clientes

### Lista de Clientes

- **Visualização**: Clientes exibidos em cards ou lista
- **Pesquisa**: Busque clientes por nome, email ou telefone
- **Ordenação**: Organize por nome, data de cadastro ou número de pedidos
- **Integração WhatsApp**: Botão para contato direto via WhatsApp

### Adicionar/Editar Clientes

- **Informações Básicas**: Nome, email e telefone
- **Código do País**: Armazena o código telefônico internacional (ex: +55)
- **Endereço**: Rua, número, complemento, bairro, cidade e estado
- **Observações**: Notas adicionais sobre o cliente

### Histórico de Pedidos

- **Visualização**: Veja todos os pedidos feitos pelo cliente
- **Detalhes**: Acesse rapidamente os detalhes de cada pedido

## Gerenciamento de Cupons

### Lista de Cupons

- **Visualização**: Cupons exibidos com código, tipo e valor
- **Pesquisa**: Busque cupons por código (insensível a maiúsculas/minúsculas)
- **Status**: Veja quais cupons estão ativos ou inativos

### Adicionar/Editar Cupons

- **Código**: Identificador único do cupom
- **Tipo**: Valor fixo ou percentual
- **Valor**: Quantia de desconto (valor ou percentual)
- **Valor Mínimo**: Valor mínimo de compra para aplicar o cupom
- **Validade**: Data de início e término da validade
- **Limite de Uso**: Número máximo de utilizações
- **Status**: Ativo ou inativo

### Aplicação de Descontos

- **Busca de Cupons**: Pesquisa insensível a maiúsculas/minúsculas
- **Validação**: Verificação automática de validade, valor mínimo e limite de uso
- **Exibição**:
  - Descontos fixos mostrados como "Desconto (Fixo)"
  - Descontos percentuais mostrados como "Desconto (X%)"
- **Recálculo em Revisões**:
  - Descontos percentuais são recalculados automaticamente com base no novo subtotal
  - O percentual original é mantido, apenas o valor monetário é atualizado
- **Exibição em Detalhes**: Valor monetário do desconto exibido com o símbolo da moeda

## Boas Práticas

### Interface do Usuário

- **Layout Responsivo**: A interface se adapta a diferentes tamanhos de tela, especialmente dispositivos móveis
- **Navegação em Cards**: Clique em cards para acessar detalhes em vez de tabelas tradicionais
- **Telas Não-Modais**: Edição e visualização em páginas completas, não em modais
- **Cabeçalhos**: Evite cabeçalhos duplicados nas páginas de pedidos e clientes
- **Ícones Intuitivos**:
  - Use ícone de impressora para o botão "Ver Pedido" em vez do ícone de olho/visualização
  - Adicione ícone de edição ao lado de cada produto na tela de revisão de pedido
  - Adicione ícone de lixeira para permitir remoção individual de produtos
- **Layout de Detalhes do Pedido**:
  - Mova o subtotal e taxa de entrega da aba de checkout para a aba de detalhes do pedido
  - Posicione a taxa de entrega acima do subtotal
  - Separe visualmente o card de informações financeiras da lista de itens do pedido
- **Layout de Produtos**:
  - Posicione o campo de quantidade abaixo das fotos do produto na página de detalhes

### Pedidos

- **Verificação de Clientes**: Ao criar pedidos, verifique se o cliente já existe por email ou telefone
- **Produtos Personalizados**: Crie produtos específicos para um pedido sem adicionar ao catálogo
- **Revisões**: Use revisões para modificar pedidos em vez de editar diretamente
- **Impressão**: Gere PDFs com layout compacto, separação clara de seções e tipografia hierárquica

### Clientes

- **Validação de Telefone**:
  - Para números brasileiros (+55), valide DDD + 8 ou 9 dígitos
  - Exiba mensagem de erro apropriada para formatos inválidos
- **Códigos de País**:
  - Armazene o código telefônico internacional (ex: +55) para qualquer país selecionado
  - Ao processar códigos que contêm texto entre parênteses (como '+1 (EUA)'), remova o texto e os parênteses
  - Armazene apenas o código numérico (como '+1')
- **Persistência Local**: Dados do cliente e endereço são salvos no localStorage durante checkout
- **Formatação Internacional**: Exiba números de telefone com o código do país
- **Verificação de Existência**: Ao criar pedidos, verifique se o cliente já existe por email primeiro, depois por telefone

### Produtos

- **Imagens Otimizadas**: Use imagens de tamanho adequado para melhor desempenho
- **Variações Claras**: Apresente opções de variação de forma organizada
- **Quantidade**: Posicione o campo de quantidade abaixo das fotos do produto

### Internacionalização

- **Traduções Completas**: Verifique se todos os textos estão traduzidos para português e inglês
- **Moeda**: Use o símbolo de moeda configurado em todos os valores monetários
- **Traduções Recentes Implementadas**:
  - Métodos de pagamento na tela de configurações da loja
  - Mensagens de sucesso e erro para edição de categorias
  - Textos de carregamento ("Salvando...")
  - Labels de categorias e botão "Mais informações" na interface da loja
  - Todas as chaves de tradução para métodos de pagamento padrão e personalizados

## Resolução de Problemas

### Traduções Não Exibidas

Se algum texto não estiver sendo traduzido corretamente:

1. **Verifique a Chave de Tradução**: Confirme se a chave está sendo usada corretamente no código
2. **Arquivo i18n.ts**: Verifique se a tradução existe tanto em português quanto em inglês
3. **Seção Correta**: Certifique-se de que a tradução está na seção correta (ex: `storefront`, `admin`, `settings`)
4. **Hot Reload**: O sistema detecta mudanças automaticamente, mas pode ser necessário recarregar a página

### Problemas Comuns Resolvidos

- **Métodos de Pagamento**: Traduções faltantes na tela de configurações foram adicionadas
- **Edição de Categorias**: Mensagens de sucesso e erro agora são exibidas corretamente
- **Interface da Loja**: Labels de categorias e botão "Mais informações" traduzidos
- **Textos de Carregamento**: Indicadores de "Salvando..." implementados

## Dicas de Uso

1. **Teste em Localhost**: Use localhost:3000 para testar alterações antes de publicar
2. **Verifique Traduções**: Certifique-se de que novos textos estão traduzidos em todos os idiomas
3. **Considere Dispositivos Móveis**: Teste a interface em dispositivos iOS para garantir boa experiência
4. **Backup de Dados**: Faça backups regulares do banco de dados
5. **Personalize a Identidade Visual**: Use as cores e logo da loja em todos os elementos visuais
6. **Traduções Consistentes**: Mantenha consistência entre as traduções em português e inglês
7. **Validação de Dados**: Sempre valide dados de entrada tanto no frontend quanto no backend

## Tecnologias e Desenvolvimento

### Stack Tecnológico

- **Frontend**: React com Tailwind CSS e componentes shadcn/ui
- **Backend**: Express (Node.js)
- **Banco de Dados**: PostgreSQL via Supabase
- **Autenticação**: Supabase Auth
- **Implantação**: Firebase Hosting
- **Internacionalização**: React-i18next

### Boas Práticas de Desenvolvimento

- **Supabase**: Use o cliente Supabase exclusivamente para operações de banco de dados em vez de conexões PostgreSQL diretas
- **Internacionalização**:
  - Adicione traduções para todas as novas chaves de texto no sistema
  - Verifique se as traduções estão na seção correta do arquivo i18n.ts
  - Mantenha consistência entre português e inglês
  - Use chaves descritivas e organizadas por funcionalidade
- **Commits**: Não faça commits automaticamente - sempre pergunte ao usuário antes
- **Valores Monetários**: Use o símbolo de moeda da configuração da loja em todos os valores
- **Interface**:
  - Considere a experiência em dispositivos iOS ao implementar ou modificar componentes
  - Teste em diferentes tamanhos de tela, especialmente mobile
  - Use hot reload para verificar mudanças em tempo real
- **Identidade Visual**: Adicione logo e cores da loja ao implementar elementos visuais
- **Geração de PDF**: O sistema usa as bibliotecas jspdf e html2canvas para geração de PDF
- **Validação**:
  - Implemente validação tanto no frontend quanto no backend
  - Use mensagens de erro claras e traduzidas
  - Valide dados de entrada antes de processar
- **Debugging**:
  - Use o terminal para verificar logs de requisições e erros
  - Verifique o hot reload do Vite para confirmar que mudanças foram aplicadas
  - Teste funcionalidades em localhost:5000 antes de publicar

## Atualizações Recentes

### Melhorias de Tradução (Última Atualização)

#### Métodos de Pagamento
- **Configurações da Loja**: Todas as traduções para métodos de pagamento foram implementadas
- **Chaves Adicionadas**:
  - `settings.standardPaymentMethods`: "Métodos de pagamento padrão" / "Standard payment methods"
  - `settings.customPaymentMethods`: "Métodos de pagamento personalizados" / "Custom payment methods"
  - `settings.cashPayment`: "Dinheiro" / "Cash"
  - `settings.creditCardPayment`: "Cartão de crédito" / "Credit card"
  - `settings.debitCardPayment`: "Cartão de débito" / "Debit card"
  - `settings.pixPayment`: "PIX" / "PIX"
  - `settings.bankTransferPayment`: "Transferência bancária" / "Bank transfer"
  - `settings.paymentMethodsDesc`: Descrição dos métodos de pagamento
  - `settings.paymentMethodExists`: Validação para métodos duplicados

#### Interface da Loja
- **Storefront**: Traduções para elementos da interface do cliente
- **Chaves Adicionadas**:
  - `storefront.categories`: "Categorias" / "Categories"
  - `storefront.moreInfo`: "Mais informações" / "More information"

#### Edição de Categorias
- **Admin**: Mensagens de sucesso e erro para operações com categorias
- **Chaves Movidas**: Traduções movidas da seção `categories` para `admin` para consistência
- **Chaves Adicionadas**:
  - `admin.categoryUpdated`: Mensagem de sucesso
  - `admin.categoryUpdatedDesc`: Descrição detalhada
  - `common.saving`: "Salvando..." / "Saving..."

### Status das Traduções

✅ **Completo**: Métodos de pagamento nas configurações da loja
✅ **Completo**: Edição e gerenciamento de categorias
✅ **Completo**: Interface da loja (storefront)
✅ **Completo**: Mensagens de carregamento e feedback

### Próximas Melhorias Sugeridas

- Verificar traduções em outras seções do sistema
- Implementar validações adicionais com mensagens traduzidas
- Melhorar feedback visual para operações de longa duração
- Expandir suporte a outros idiomas se necessário
