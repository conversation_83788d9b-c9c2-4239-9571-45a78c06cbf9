# Deploy de Monorepo no Vercel - Configuração Otimizada

## Visão Geral

Este documento descreve as configurações implementadas para otimizar o deploy do monorepo no Vercel, baseado nas melhores práticas para projetos que combinam frontend e backend em um único repositório.

## Estrutura do Projeto

```
replitDoceMenu/
├── client/          # Frontend React + Vite
├── server/          # Backend Express.js
├── shared/          # Código compartilhado
├── dist/            # Output do build
│   ├── public/      # Frontend buildado
│   └── index.js     # Backend buildado
├── package.json     # Dependências e scripts
├── vercel.json      # Configuração do Vercel
└── vite.config.ts   # Configuração do Vite
```

## Configurações Implementadas

### 1. Arquivo `vercel.json` Otimizado

O arquivo `vercel.json` foi atualizado com as seguintes melhorias:

#### **Builds**
- `@vercel/node` para o backend (`dist/index.js`)
- `@vercel/static` para arquivos estáticos do frontend (`dist/public/**/*`)

#### **Roteamento**
- Rotas `/api/*` direcionadas para o backend
- Fallback para `index.html` para SPA routing
- Suporte a todos os métodos HTTP (GET, POST, PUT, PATCH, DELETE, OPTIONS)

#### **Headers CORS**
- Configuração automática de CORS para APIs
- Headers de controle de acesso apropriados

#### **Cache Otimizado**
- Cache longo (1 ano) para assets estáticos
- Cache curto para `index.html` (sempre revalidar)

#### **Configurações de Função**
- Timeout de 30 segundos para funções serverless
- Variável de ambiente `NODE_ENV=production`

### 2. Script de Build

O script de build no `package.json` combina:
```bash
vite build && esbuild server/index.ts --platform=node --packages=external --bundle --format=cjs --outfile=dist/index.js
```

- **Vite**: Builda o frontend para `dist/public/`
- **esbuild**: Builda o backend para `dist/index.js` em formato CommonJS

### 3. Configuração do Vite

O `vite.config.ts` está configurado para:
- Output em `dist/public/`
- Aliases para `@` (client/src), `@shared` (shared), `@assets` (attached_assets)
- Code splitting otimizado com chunks manuais
- Suporte a plugins do Replit em desenvolvimento

## Instruções de Deploy

### Pré-requisitos

1. **Conta no Vercel**: Certifique-se de ter uma conta ativa
2. **Repositório Git**: O projeto deve estar em um repositório Git (GitHub, GitLab, Bitbucket)
3. **Variáveis de Ambiente**: Configure todas as variáveis necessárias

### Deploy via Dashboard do Vercel

1. **Conectar Repositório**:
   - Acesse [vercel.com](https://vercel.com)
   - Clique em "New Project"
   - Conecte seu repositório Git

2. **Configurações do Projeto**:
   - **Framework Preset**: Other
   - **Root Directory**: `.` (raiz do projeto)
   - **Build Command**: `npm run build`
   - **Output Directory**: `dist`

3. **Variáveis de Ambiente**:
   Configure as seguintes variáveis no dashboard do Vercel:
   ```
   NODE_ENV=production
   VITE_SUPABASE_URL=sua_url_supabase
   VITE_SUPABASE_ANON_KEY=sua_chave_anonima
   SUPABASE_SERVICE_ROLE_KEY=sua_chave_service_role
   VITE_FIREBASE_API_KEY=sua_api_key_firebase
   VITE_FIREBASE_AUTH_DOMAIN=seu_auth_domain
   VITE_FIREBASE_PROJECT_ID=seu_project_id
   STRIPE_SECRET_KEY=sua_chave_secreta_stripe
   VITE_STRIPE_PUBLISHABLE_KEY=sua_chave_publica_stripe
   ```

4. **Deploy**:
   - Clique em "Deploy"
   - Aguarde o build completar

### Deploy via CLI do Vercel

1. **Instalar CLI**:
   ```bash
   npm i -g vercel
   ```

2. **Login**:
   ```bash
   vercel login
   ```

3. **Deploy**:
   ```bash
   vercel --prod
   ```

### Verificação do Deploy

Após o deploy, verifique:

1. **Frontend**: Acesse a URL principal do projeto
2. **Backend**: Teste as rotas da API em `/api/*`
3. **Logs**: Monitore os logs no dashboard do Vercel

## Troubleshooting

### Problemas Comuns

1. **Build Falha**:
   - Verifique se todas as dependências estão instaladas
   - Confirme que o script `npm run build` funciona localmente

2. **API Routes Não Funcionam**:
   - Verifique se as rotas começam com `/api/`
   - Confirme que o backend está sendo buildado corretamente

3. **Variáveis de Ambiente**:
   - Certifique-se de que todas as variáveis estão configuradas
   - Variáveis que começam com `VITE_` são expostas no frontend

4. **Timeout de Função**:
   - Otimize consultas de banco de dados
   - Considere implementar cache
   - Verifique se o timeout de 30s é suficiente

### Logs e Monitoramento

- **Dashboard do Vercel**: Monitore builds e deployments
- **Function Logs**: Verifique logs das funções serverless
- **Analytics**: Use Vercel Analytics para monitorar performance

## Otimizações Futuras

1. **Edge Functions**: Considere migrar algumas funções para Edge Runtime
2. **ISR**: Implemente Incremental Static Regeneration para páginas dinâmicas
3. **CDN**: Otimize assets estáticos com CDN
4. **Monitoring**: Implemente monitoramento de performance e erros

## Conclusão

A configuração implementada otimiza o deploy do monorepo no Vercel, garantindo:
- Build eficiente do frontend e backend
- Roteamento correto entre SPA e API
- Cache otimizado para performance
- Configurações de segurança apropriadas

Para dúvidas ou problemas, consulte a [documentação oficial do Vercel](https://vercel.com/docs) ou os logs de deploy no dashboard.
