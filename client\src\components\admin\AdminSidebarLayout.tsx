import { ReactNode, useState, useEffect, useMemo } from 'react';
import { Link, useLocation } from 'wouter';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarProvider,
  SidebarTrigger,
  useSidebar
} from '@/components/ui/sidebar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useAuth } from '@/context/FirebaseAuthContext';
import { useStore } from '@/context/StoreContext';
import { useTranslation } from '@/hooks/useTranslation';
import { useIsGlobalAdmin } from '@/hooks/useGlobalAdmin';
import { useDashboardData } from '@/hooks/useDashboardData';
import {
  LayoutDashboard,
  ShoppingBag,
  ShoppingCart,
  Users,
  Settings,
  Store,
  User,
  LogOut,
  FolderTree,
  Ticket,
  Crown,
  CreditCard,
  ExternalLink,
  Menu,
  Bell
} from 'lucide-react';

interface AdminSidebarLayoutProps {
  children: ReactNode;
  title?: string;
  description?: string;
}

interface NavItem {
  path: string;
  label: string;
  icon: ReactNode;
  badge?: number;
  external?: boolean;
}

interface NavGroup {
  label: string;
  items: NavItem[];
}

export default function AdminSidebarLayout({ children, title, description }: AdminSidebarLayoutProps) {
  const [location] = useLocation();
  const { user, signOut } = useAuth();
  const { store } = useStore();
  const { t } = useTranslation();
  const isGlobalAdmin = useIsGlobalAdmin();
  const { data: dashboardData } = useDashboardData();

  // Obter número de pedidos pendentes para badge
  const pendingOrdersCount = dashboardData?.summary?.pendingOrders || 0;

  // Verificar se a loja está completamente configurada
  const isStoreFullyConfigured = useMemo(() => {
    if (!store) return false;

    // Verificar se tem pelo menos nome, descrição e configurações básicas
    const hasBasicInfo = !!(store.name && store.description && store.slug);
    const hasPaymentMethods = !!(store.paymentMethods && (
      store.paymentMethods.cash ||
      store.paymentMethods.creditCard ||
      store.paymentMethods.debitCard ||
      store.paymentMethods.pix ||
      store.paymentMethods.bankTransfer ||
      (store.paymentMethods.customMethods && store.paymentMethods.customMethods.length > 0)
    ));

    return hasBasicInfo && hasPaymentMethods;
  }, [store]);

  const isActive = (path: string) => {
    if (path === '/admin') {
      return location === '/admin' || location === '/admin/dashboard';
    }
    return location.startsWith(path);
  };

  // Organizar itens de menu em grupos com navegação condicional
  const navGroups: NavGroup[] = useMemo(() => {
    // Se a loja não está configurada, mostrar apenas configurações
    if (!isStoreFullyConfigured) {
      return [
        {
          label: t('common.configuration'),
          items: [
            {
              path: '/admin/settings',
              label: t('common.settings'),
              icon: <Settings className="h-4 w-4" />
            }
          ]
        }
      ];
    }

    // Se a loja está configurada, mostrar todos os itens
    return [
      {
        label: t('common.overview'),
        items: [
          {
            path: '/admin',
            label: t('common.dashboard'),
            icon: <LayoutDashboard className="h-4 w-4" />
          }
        ]
      },
      {
        label: t('common.sales'),
        items: [
          {
            path: '/admin/orders',
            label: t('common.orders'),
            icon: <ShoppingCart className="h-4 w-4" />,
            badge: pendingOrdersCount > 0 ? pendingOrdersCount : undefined
          },
          {
            path: '/admin/customers',
            label: t('common.customers'),
            icon: <Users className="h-4 w-4" />
          },
          {
            path: '/admin/coupons',
            label: t('coupons.title'),
            icon: <Ticket className="h-4 w-4" />
          }
        ]
      },
      {
        label: t('common.catalog'),
        items: [
          {
            path: '/admin/products',
            label: t('common.products'),
            icon: <ShoppingBag className="h-4 w-4" />
          },
          {
            path: '/admin/categories',
            label: t('common.categories'),
            icon: <FolderTree className="h-4 w-4" />
          }
        ]
      },
      {
        label: t('common.configuration'),
        items: [
          {
            path: '/admin/settings',
            label: t('common.settings'),
            icon: <Settings className="h-4 w-4" />
          }
        ]
      }
    ];
  }, [isStoreFullyConfigured, t, pendingOrdersCount]);

  // Adicionar itens especiais se aplicável
  if (isGlobalAdmin) {
    navGroups.push({
      label: 'Global Admin',
      items: [
        {
          path: '/admin/global',
          label: 'Dashboard Global',
          icon: <Crown className="h-4 w-4" />
        }
      ]
    });
  }

  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  return (
    <SidebarProvider>
      <div className="flex min-h-screen w-full bg-gray-50">
        <AppSidebar
          navGroups={navGroups}
          isActive={isActive}
          store={store}
          user={user}
          onSignOut={handleSignOut}
          t={t}
        />
        
        {/* Main content */}
        <main className="flex-1 flex flex-col overflow-hidden">
          {/* Top bar for mobile */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-white shadow-sm lg:hidden">
            <div className="flex items-center space-x-3">
              <SidebarTrigger className="p-2 text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors" />
              {store?.logo ? (
                <img
                  src={store.logo}
                  alt={store.name}
                  className="h-8 w-8 rounded-lg object-cover shadow-sm"
                />
              ) : (
                <div className="h-8 w-8 rounded-lg bg-gradient-to-br from-pink-500 to-yellow-500 flex items-center justify-center shadow-sm">
                  <Store className="h-4 w-4 text-white" />
                </div>
              )}
            </div>
            <h1 className="text-lg font-semibold text-gray-900 truncate flex-1 text-center mx-4">
              {title || 'Admin'}
            </h1>
            <div className="w-12" /> {/* Spacer for centering */}
          </div>

          {/* Page header for desktop */}
          {(title || description) && (
            <div className="hidden lg:block border-b bg-white px-6 py-4">
              <div className="max-w-7xl mx-auto">
                {title && <h1 className="text-2xl font-bold text-gray-900">{title}</h1>}
                {description && <p className="mt-1 text-sm text-gray-600">{description}</p>}
              </div>
            </div>
          )}

          {/* Page content */}
          <div className="flex-1 overflow-auto bg-gray-50">
            <div className="max-w-7xl mx-auto p-3 sm:p-4 lg:p-6 min-h-full">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6 min-h-[calc(100vh-8rem)]">
                {children}
              </div>
            </div>
          </div>
        </main>
      </div>
    </SidebarProvider>
  );
}

// Componente do Sidebar separado para melhor organização
interface AppSidebarProps {
  navGroups: NavGroup[];
  isActive: (path: string) => boolean;
  store: any;
  user: any;
  onSignOut: () => void;
  t: (key: string) => string;
}

function AppSidebar({ navGroups, isActive, store, user, onSignOut, t }: AppSidebarProps) {
  return (
    <Sidebar className="border-r bg-white shadow-xl lg:shadow-none lg:border-gray-200">
      <SidebarHeader className="border-b border-gray-200 p-3 bg-white">
        {/* Logo e informações da loja */}
        <div className="flex items-center space-x-3 mb-3">
          {store?.logo ? (
            <img
              src={store.logo}
              alt={store.name}
              className="h-10 w-10 rounded-xl object-cover shadow-sm"
            />
          ) : (
            <div className="h-10 w-10 rounded-xl bg-gradient-to-br from-pink-500 to-yellow-500 flex items-center justify-center shadow-sm">
              <Store className="h-5 w-5 text-white" />
            </div>
          )}
          <div className="flex-1 min-w-0">
            <h2 className="text-sm font-semibold text-gray-900 truncate">
              {store?.name || 'Doce Menu'}
            </h2>
            <p className="text-xs text-gray-500 truncate">
              {t('common.admin')}
            </p>
          </div>
        </div>

        {/* Botões de ação reorganizados */}
        <div className="space-y-1.5">
          {/* Link para ver loja */}
          {store && (
            <Button
              variant="outline"
              size="sm"
              asChild
              className="w-full justify-start h-8 text-gray-700 border-gray-200 hover:bg-gray-50 hover:text-gray-900 hover:border-gray-300"
            >
              <Link href={`/${store.slug}`} className="flex items-center space-x-2">
                <ExternalLink className="h-4 w-4 flex-shrink-0" />
                <span className="text-sm font-medium">{t('common.viewStore')}</span>
              </Link>
            </Button>
          )}

          {/* Menu do usuário */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                className="w-full justify-start h-8 text-gray-700 border-gray-200 hover:bg-gray-50 hover:text-gray-900 hover:border-gray-300"
              >
                <Avatar className="h-6 w-6 flex-shrink-0 mr-2">
                  <AvatarImage src={user?.photoURL} />
                  <AvatarFallback className="text-xs bg-gradient-to-br from-pink-500 to-yellow-500 text-white">
                    {user?.displayName?.charAt(0) || user?.email?.charAt(0) || 'U'}
                  </AvatarFallback>
                </Avatar>
                <span className="text-sm font-medium truncate flex-1 text-left">
                  {user?.displayName || user?.email?.split('@')[0] || 'User'}
                </span>
                <User className="h-4 w-4 flex-shrink-0 ml-2 text-gray-400" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56 bg-white border border-gray-200 shadow-lg">
              <DropdownMenuItem disabled className="text-gray-500">
                <User className="mr-2 h-4 w-4" />
                <span className="truncate">{user?.email}</span>
              </DropdownMenuItem>
              <DropdownMenuSeparator className="bg-gray-200" />
              <DropdownMenuItem
                onClick={onSignOut}
                className="text-red-600 hover:text-red-700 hover:bg-red-50 focus:bg-red-50 focus:text-red-700"
              >
                <LogOut className="mr-2 h-4 w-4" />
                <span>{t('common.logout')}</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </SidebarHeader>

      <SidebarContent className="px-3 py-3 bg-white overflow-y-auto">
        {navGroups.map((group, index) => (
          <SidebarGroup key={index} className="mb-4">
            <SidebarGroupLabel className="text-xs font-semibold text-gray-500 uppercase tracking-wider px-3 py-1 mb-1">
              {group.label}
            </SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu className="space-y-0.5">
                {group.items.map((item) => (
                  <SidebarMenuItem key={item.path}>
                    <SidebarMenuButton
                      asChild
                      isActive={isActive(item.path)}
                      className={`
                        w-full justify-start h-9 px-3 rounded-lg transition-all duration-200 ease-in-out
                        ${isActive(item.path)
                          ? 'bg-gradient-to-r from-pink-50 to-yellow-50 text-gray-900 border border-pink-200 shadow-sm'
                          : 'text-gray-700 hover:text-gray-900 hover:bg-gray-50 border border-transparent hover:border-gray-200'
                        }
                      `}
                    >
                      <Link href={item.path} className="flex items-center space-x-3 w-full">
                        <div className={`
                          flex-shrink-0
                          ${isActive(item.path) ? 'text-pink-600' : 'text-gray-500'}
                        `}>
                          {item.icon}
                        </div>
                        <span className="flex-1 font-medium text-sm">{item.label}</span>
                        {item.badge && (
                          <Badge
                            variant="destructive"
                            className="ml-auto h-5 min-w-5 text-xs bg-red-500 hover:bg-red-600 text-white"
                          >
                            {item.badge}
                          </Badge>
                        )}
                        {item.external && (
                          <ExternalLink className="h-3 w-3 text-gray-400 flex-shrink-0" />
                        )}
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        ))}
      </SidebarContent>


    </Sidebar>
  );
}
