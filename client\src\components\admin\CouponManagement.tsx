import { useState, useMemo, useCallback } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { Plus, Check, X, Edit, Trash2, Search, Ticket, Crown } from "lucide-react";
import { formatCurrency, formatStoreCurrency, formatDate } from "@/lib/utils";
import { useTranslation } from "@/hooks/useTranslation";
import { apiRequest } from "@/lib/queryClient";
import { useStore } from "@/context/StoreContext";
import { useLocation } from "wouter";
import { useSubscription } from "@/context/SubscriptionContext";
import { UpgradePrompt } from "@/components/subscription/UpgradePrompt";
import { StartTrialButton } from "@/components/subscription/StartTrialButton";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";



export default function CouponManagement() {
  const { t } = useTranslation();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { store } = useStore();
  const [, navigate] = useLocation();

  // Estados locais
  const [searchQuery, setSearchQuery] = useState("");
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedCoupon, setSelectedCoupon] = useState<any>(null);
  const [showUpgradePrompt, setShowUpgradePrompt] = useState(false);

  // Hooks de assinatura
  const {
    canUseCoupons,
    usageInfo,
    planConfig
  } = useSubscription();

  // Verificações de plano
  const isCouponsAvailable = useMemo(() => canUseCoupons(), [canUseCoupons]);
  const isFreePlan = useMemo(() => planConfig?.id === 'free', [planConfig]);

  // Buscar cupons
  const { data: coupons = [], isLoading } = useQuery({
    queryKey: ['/admin/cupons'],
    enabled: !!store,
  });

  // Filtrar cupons com base na busca
  const filteredCoupons = coupons
    ? coupons.filter((coupon: any) =>
        coupon.code.toLowerCase().includes(searchQuery.toLowerCase()))
    : [];

  // Atualizar status do cupom
  const toggleStatusMutation = useMutation({
    mutationFn: ({ id, active }: { id: number, active: boolean }) =>
      apiRequest('PATCH', `/admin/cupons/${id}/status`, { active }),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['/admin/cupons'] });
      toast({
        title: variables.active ? t('coupons.couponActivated') : t('coupons.couponDeactivated'),
        description: t('common.success'),
      });
    },
    onError: (error) => {
      toast({
        title: t('common.error'),
        description: error instanceof Error ? error.message : String(error),
        variant: "destructive",
      });
    }
  });

  // Excluir cupom
  const deleteMutation = useMutation({
    mutationFn: (id: number) =>
      apiRequest('DELETE', `/admin/cupons/${id}`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/admin/cupons'] });
      setIsDeleteDialogOpen(false);
      toast({
        title: t('coupons.couponDeleted'),
        description: t('common.success'),
      });
    },
    onError: (error) => {
      toast({
        title: t('common.error'),
        description: error instanceof Error ? error.message : String(error),
        variant: "destructive",
      });
    }
  });

  // Handler para tentar adicionar cupom
  const handleAddCoupon = useCallback(() => {
    if (!isCouponsAvailable) {
      if (isFreePlan) {
        setShowUpgradePrompt(true);
      } else {
        toast({
          title: t('subscription.feature_blocked.allowCoupons.title'),
          description: t('subscription.feature_blocked.allowCoupons.description'),
          variant: 'destructive',
        });
      }
      return;
    }
    navigate('/admin/coupons/new');
  }, [isCouponsAvailable, isFreePlan, toast, t, navigate]);

  // Navegar para a página de edição
  const handleEdit = (coupon: any) => {
    if (!isCouponsAvailable) {
      if (isFreePlan) {
        setShowUpgradePrompt(true);
      } else {
        toast({
          title: t('subscription.feature_blocked.allowCoupons.title'),
          description: t('subscription.feature_blocked.allowCoupons.description'),
          variant: 'destructive',
        });
      }
      return;
    }
    navigate(`/admin/coupons/${coupon.id}/edit`);
  };

  // Abrir diálogo de exclusão
  const handleDelete = (coupon: any) => {
    if (!isCouponsAvailable) {
      if (isFreePlan) {
        setShowUpgradePrompt(true);
      } else {
        toast({
          title: t('subscription.feature_blocked.allowCoupons.title'),
          description: t('subscription.feature_blocked.allowCoupons.description'),
          variant: 'destructive',
        });
      }
      return;
    }
    setSelectedCoupon(coupon);
    setIsDeleteDialogOpen(true);
  };

  // Alternar status do cupom
  const handleToggleStatus = (coupon: any) => {
    if (!isCouponsAvailable) {
      if (isFreePlan) {
        setShowUpgradePrompt(true);
      } else {
        toast({
          title: t('subscription.feature_blocked.allowCoupons.title'),
          description: t('subscription.feature_blocked.allowCoupons.description'),
          variant: 'destructive',
        });
      }
      return;
    }
    toggleStatusMutation.mutate({
      id: coupon.id,
      active: !coupon.ativo,
    });
  };

  return (
    <div>
      {/* Prompt de upgrade para plano gratuito */}
      {showUpgradePrompt && isFreePlan && (
        <div className="mb-6">
          <UpgradePrompt
            title={t('subscription.feature_blocked.allowCoupons.title')}
            description={t('subscription.feature_blocked.allowCoupons.description')}
            feature="allowCoupons"
            variant="alert"
            onClose={() => setShowUpgradePrompt(false)}
          />
        </div>
      )}

      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
        {/* Barra de busca */}
        <div className="relative w-full sm:w-80">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder={`${t('common.search')} ${t('coupons.title').toLowerCase()}...`}
            className="pl-10"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        <div className="flex items-center gap-2">
          {/* Botão de teste grátis para usuários do plano gratuito */}
          {!isCouponsAvailable && isFreePlan && (
            <StartTrialButton
              variant="outline"
              className="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white border-0"
            />
          )}

          <Button
            onClick={handleAddCoupon}
            className="flex items-center"
            disabled={!isCouponsAvailable}
            variant={!isCouponsAvailable ? "outline" : "default"}
          >
            {!isCouponsAvailable && isFreePlan ? (
              <Crown className="mr-1 h-4 w-4" />
            ) : (
              <Plus className="mr-1 h-4 w-4" />
            )}
            {!isCouponsAvailable && isFreePlan
              ? t('subscription.upgrade_required')
              : t('coupons.addCoupon')
            }
          </Button>
        </div>
      </div>

      {/* Lista de cupons */}
      {isLoading ? (
        <div className="space-y-4">
          {[...Array(3)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-6 bg-neutral-200 rounded w-1/4 mb-4"></div>
                <div className="h-4 bg-neutral-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : filteredCoupons.length === 0 ? (
        <Card>
          <CardContent className="p-6 text-center">
            <Ticket className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <p className="text-muted-foreground">{t('coupons.noCoupons')}</p>
            {isCouponsAvailable && (
              <Button
                onClick={handleAddCoupon}
                className="mt-4"
              >
                {t('coupons.addCoupon')}
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {filteredCoupons.map((coupon: any) => (
            <Card key={coupon.id} className="overflow-hidden">
              <CardContent className="p-6">
                <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h3 className="text-lg font-semibold">{coupon.code}</h3>
                      <Badge variant={coupon.ativo ? "default" : "outline"}>
                        {coupon.ativo ? t('coupons.active') : t('coupons.inactive')}
                      </Badge>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-muted-foreground">
                      <div>
                        <span className="font-medium">{t('coupons.couponType')}: </span>
                        {coupon.tipo === 'valor_fixo' ? t('coupons.fixedValue') : t('coupons.percentage')}
                      </div>
                      <div>
                        <span className="font-medium">{t('coupons.couponValue')}: </span>
                        {coupon.tipo === 'valor_fixo'
                          ? formatStoreCurrency(coupon.valor, store?.currency)
                          : `${coupon.valor}%`}
                      </div>
                      <div>
                        <span className="font-medium">{t('coupons.expirationDate')}: </span>
                        {formatDate(new Date(coupon.dataValidade))}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => handleToggleStatus(coupon)}
                      title={coupon.ativo ? t('coupons.inactive') : t('coupons.active')}
                      disabled={!isCouponsAvailable}
                    >
                      {coupon.ativo ? <X className="h-4 w-4" /> : <Check className="h-4 w-4" />}
                    </Button>
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => handleEdit(coupon)}
                      title={!isCouponsAvailable ? t('subscription.upgrade_required') : t('common.edit')}
                      disabled={!isCouponsAvailable}
                    >
                      {!isCouponsAvailable && isFreePlan ? (
                        <Crown className="h-4 w-4" />
                      ) : (
                        <Edit className="h-4 w-4" />
                      )}
                    </Button>
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => handleDelete(coupon)}
                      title={!isCouponsAvailable ? t('subscription.upgrade_required') : t('common.delete')}
                      disabled={!isCouponsAvailable}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Upgrade prompt quando funcionalidade não disponível */}
      {!isCouponsAvailable && isFreePlan && (
        <div className="mt-6">
          <UpgradePrompt
            title="Sistema de cupons disponível no plano Premium"
            description="Crie e gerencie cupons de desconto para aumentar suas vendas. Faça upgrade para o plano Premium e tenha acesso a esta funcionalidade."
            variant="card"
          />
        </div>
      )}

      {/* Diálogo de confirmação para excluir cupom */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t('coupons.deleteCoupon')}</AlertDialogTitle>
            <AlertDialogDescription>
              {t('coupons.confirmDelete')}
              <br />
              {t('coupons.confirmDeleteDesc')}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t('common.cancel')}</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => selectedCoupon && deleteMutation.mutate(selectedCoupon.id)}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {deleteMutation.isPending && (
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
              )}
              {t('common.delete')}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
