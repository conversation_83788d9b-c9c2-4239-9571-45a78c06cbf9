import React, { useState, useEffect, useRef, useMemo, useCallback } from "react";
import { useMutation } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { countryCodes } from "@/lib/countryCodes";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from "@/components/ui/accordion";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { useTranslation } from "@/hooks/useTranslation";
import { useStore } from "@/context/StoreContext";
import { apiRequest } from "@/lib/queryClient";
import { Store, SquareCheck, CreditCard, PaintBucket, Globe, Upload, Image, Plus, X, Instagram, Phone, DollarSign, LayoutGrid, Eye, Truck, Package, Calendar, CalendarX, PencilIcon, Trash, AlertTriangle, Mail, MapPin, Crown } from "lucide-react";
import { SubscriptionSettings } from "@/components/subscription/SubscriptionSettings";
import { UpgradePrompt } from "@/components/subscription/UpgradePrompt";
import { StartTrialButton } from "@/components/subscription/StartTrialButton";
import { useSubscription } from "@/context/SubscriptionContext";
import { format, parse } from "date-fns";
import StoreLayoutSelector from "./StoreLayoutSelector";

// Store form schema
const storeSchema = z.object({
  name: z.string().min(1, { message: "Store name is required" }),
  slug: z.string().min(3, { message: "URL must be at least 3 characters" })
    .regex(/^[a-z0-9-]+$/, { message: "URL can only contain lowercase letters, numbers, and hyphens" }),
  description: z.string().optional(),
  logo: z.string().optional(),
  headerImage: z.string().optional(),
  countryCode: z.string().default("+55"),
  whatsapp: z.string().optional(),
  instagram: z.string().optional(),
  currency: z.string().default("R$"),
  colors: z.object({
    primary: z.string(),
    secondary: z.string(),
    accent: z.string()
  }),
  paymentMethods: z.object({
    cash: z.boolean().default(true),
    creditCard: z.boolean().default(false),
    debitCard: z.boolean().default(false),
    pix: z.boolean().default(false),
    bankTransfer: z.boolean().default(false),
    customMethods: z.array(z.string()).default([])
  }),
  deliverySettings: z.object({
    allowDelivery: z.boolean().default(false),
    allowPickup: z.boolean().default(false),
    pickupDays: z.array(z.string()).default([]),
    pickupTimeSlots: z.array(z.string()).default([]),
    deliveryDays: z.array(z.string()).default([]),
    deliveryTimeSlots: z.array(z.string()).default([]),
    deliveryFee: z.number().min(0).default(0),
    minAdvanceDays: z.number().min(0).default(0),
    customMessage: z.string().optional().default(""),
    unavailablePeriods: z.array(
      z.object({
        id: z.string(),
        startDate: z.string(),
        endDate: z.string(),
        reason: z.string().optional()
      })
    ).default([])
  }).default({
    allowDelivery: false,
    allowPickup: false,
    pickupDays: [],
    pickupTimeSlots: [],
    deliveryDays: [],
    deliveryTimeSlots: [],
    deliveryFee: 0,
    minAdvanceDays: 0,
    customMessage: ""
  }),
  // Campos de endereço da loja
  addressStreet: z.string().optional(),
  addressNumber: z.string().optional(),
  addressComplement: z.string().optional(),
  addressNeighborhood: z.string().optional(),
  addressCity: z.string().optional(),
  addressState: z.string().optional(),
  contactEmail: z.string().email({ message: "Email inválido" }).optional(),
  layout: z.number().int().min(1).max(2).default(1),
  layoutSettings: z.record(z.any()).default({})
});

type StoreFormValues = z.infer<typeof storeSchema>;

// Predefined theme options
const themeOptions = [
  {
    value: "accent-red",
    label: "Red",
    color: "#FF6B6B",
    secondary: "#4ECDC4",
    accent: "#FFD166"
  },
  {
    value: "accent-blue",
    label: "Blue",
    color: "#4361EE",
    secondary: "#72EFDD",
    accent: "#FFC857"
  },
  {
    value: "accent-green",
    label: "Green",
    color: "#2ECC71",
    secondary: "#577590",
    accent: "#F4A261"
  },
  {
    value: "accent-purple",
    label: "Purple",
    color: "#9B5DE5",
    secondary: "#00F5D4",
    accent: "#FEE440"
  },
  {
    value: "accent-orange",
    label: "Orange",
    color: "#F39C12",
    secondary: "#3A86FF",
    accent: "#8338EC"
  },
  {
    value: "accent-teal",
    label: "Teal",
    color: "#06D6A0",
    secondary: "#118AB2",
    accent: "#FFD166"
  },
  {
    value: "accent-pink",
    label: "Pink",
    color: "#FF4D6D",
    secondary: "#073B4C",
    accent: "#FFB5A7"
  },
  {
    value: "accent-coral",
    label: "Coral",
    color: "#FF8C61",
    secondary: "#645DD7",
    accent: "#F7EC59"
  },
  {
    value: "accent-brown",
    label: "Chocolate",
    color: "#80471C",
    secondary: "#AD8E70",
    accent: "#FBF0AC"
  },
  {
    value: "accent-midnight",
    label: "Midnight",
    color: "#22223B",
    secondary: "#4A4E69",
    accent: "#F2E9E4"
  }
];

export function StoreSettings() {
  const { t, changeLanguage, currentLanguage } = useTranslation();
  const { toast } = useToast();
  const { store, createStore, updateStore, uploadLogo, uploadHeaderImage } = useStore();
  const [accentColor, setAccentColor] = useState("accent-red");
  const [isUploadingLogo, setIsUploadingLogo] = useState(false);
  const [isUploadingHeaderImage, setIsUploadingHeaderImage] = useState(false);
  const [newCustomMethod, setNewCustomMethod] = useState("");
  const [showUnavailablePeriodForm, setShowUnavailablePeriodForm] = useState(false);
  const [editingPeriodId, setEditingPeriodId] = useState<string | null>(null);
  const [newPeriodStartDate, setNewPeriodStartDate] = useState("");
  const [newPeriodEndDate, setNewPeriodEndDate] = useState("");
  const [newPeriodReason, setNewPeriodReason] = useState("");
  const [showUpgradePrompt, setShowUpgradePrompt] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const headerImageFileInputRef = useRef<HTMLInputElement>(null);

  // Hooks de assinatura
  const {
    canUseCustomization,
    usageInfo,
    planConfig
  } = useSubscription();

  // Verificações de plano
  const isCustomizationAvailable = useMemo(() => canUseCustomization(), [canUseCustomization]);
  const isFreePlan = useMemo(() => planConfig?.id === 'free', [planConfig]);

  // Verificar se a loja está configurada com informações básicas
  const isStoreConfigured = useMemo(() => {
    if (!store) return false;

    // Verificar se tem pelo menos nome, descrição e configurações básicas
    const hasBasicInfo = !!(store.name && store.description && store.slug);
    const hasPaymentMethods = !!(store.paymentMethods && (
      store.paymentMethods.cash ||
      store.paymentMethods.creditCard ||
      store.paymentMethods.debitCard ||
      store.paymentMethods.pix ||
      store.paymentMethods.bankTransfer ||
      (store.paymentMethods.customMethods && store.paymentMethods.customMethods.length > 0)
    ));

    return hasBasicInfo && hasPaymentMethods;
  }, [store]);

  // Form setup
  const form = useForm<StoreFormValues>({
    resolver: zodResolver(storeSchema),
    defaultValues: {
      name: "",
      slug: "",
      description: "",
      logo: "",
      headerImage: "",
      countryCode: "+55",
      whatsapp: "",
      instagram: "",
      currency: "R$",
      colors: {
        primary: "#FF6B6B",
        secondary: "#4ECDC4",
        accent: "#FFD166"
      },
      paymentMethods: {
        cash: true,
        creditCard: false,
        debitCard: false,
        pix: false,
        bankTransfer: false,
        customMethods: []
      },
      deliverySettings: {
        allowDelivery: false,
        allowPickup: false,
        pickupDays: [],
        pickupTimeSlots: [],
        deliveryDays: [],
        deliveryTimeSlots: [],
        deliveryFee: 0,
        minAdvanceDays: 0,
        customMessage: "",
        unavailablePeriods: []
      },
      // Campos de endereço da loja
      addressStreet: "",
      addressNumber: "",
      addressComplement: "",
      addressNeighborhood: "",
      addressCity: "",
      addressState: "",
      contactEmail: "",
      layout: 1,
      layoutSettings: {}
    }
  });

  // Load store data into form when available
  useEffect(() => {
    if (store) {
      // Handle potential snake_case fields from Supabase
      let paymentMethodsData;

      // Check if the store has payment_methods (snake_case from Supabase)
      if (store.paymentMethods) {
        paymentMethodsData = {
          ...store.paymentMethods,
          // Ensure customMethods exists
          customMethods: store.paymentMethods.customMethods || []
        };
      } else {
        // Fallback to default if neither exists
        paymentMethodsData = {
          cash: true,
          creditCard: false,
          debitCard: false,
          pix: false,
          bankTransfer: false,
          customMethods: []
        };
      }

      // Handle potential snake_case fields for colors
      const colorsData = store.colors || {
        primary: "#FF6B6B",
        secondary: "#4ECDC4",
        accent: "#FFD166"
      };

      // Handle delivery settings data
      const deliverySettingsData = store.deliverySettings ? {
        ...store.deliverySettings,
        // Garantir que os novos campos existam mesmo se não estiverem presentes no banco
        pickupDays: store.deliverySettings.pickupDays || [],
        pickupTimeSlots: store.deliverySettings.pickupTimeSlots || [],
        deliveryDays: store.deliverySettings.deliveryDays || [],
        deliveryTimeSlots: store.deliverySettings.deliveryTimeSlots || [],
        deliveryFee: store.deliverySettings.deliveryFee ?? 0,
        minAdvanceDays: store.deliverySettings.minAdvanceDays ?? 0,
        customMessage: store.deliverySettings.customMessage || "",
        unavailablePeriods: store.deliverySettings.unavailablePeriods || []
      } : {
        allowDelivery: false,
        allowPickup: false,
        pickupDays: [],
        pickupTimeSlots: [],
        deliveryDays: [],
        deliveryTimeSlots: [],
        deliveryFee: 0,
        minAdvanceDays: 0,
        customMessage: "",
        unavailablePeriods: []
      };

      // Reset the form with the processed data
      form.reset({
        name: store.name,
        slug: store.slug,
        description: store.description || "",
        logo: store.logo || "",
        headerImage: store.headerImage || "",
        countryCode: store.countryCode || "+55",
        whatsapp: store.whatsapp || "",
        instagram: store.instagram || "",
        currency: store.currency || "R$",
        colors: colorsData,
        paymentMethods: paymentMethodsData,
        deliverySettings: deliverySettingsData,
        // Campos de endereço da loja
        addressStreet: store.addressStreet || "",
        addressNumber: store.addressNumber || "",
        addressComplement: store.addressComplement || "",
        addressNeighborhood: store.addressNeighborhood || "",
        addressCity: store.addressCity || "",
        addressState: store.addressState || "",
        contactEmail: store.contactEmail || "",
        layout: store.layout || 1,
        layoutSettings: store.layoutSettings || {}
      });

      console.log("Loaded store data:", store);
      console.log("Payment methods:", paymentMethodsData);

      // Find the closest theme option
      const closestTheme = themeOptions.find(option =>
        option.color.toLowerCase() === colorsData.primary.toLowerCase()
      ) || themeOptions[0];

      setAccentColor(closestTheme.value);
    }
  }, [store, form]);

  // Store mutation
  const storeMutation = useMutation({
    mutationFn: (data: StoreFormValues) => {
      if (store) {
        return updateStore(data);
      } else {
        return createStore(data);
      }
    },
    onSuccess: () => {
      toast({
        title: t('settings.changesSuccessfullySaved'),
        variant: "default",
      });
    },
    onError: (error) => {
      toast({
        title: t('common.error'),
        description: error instanceof Error ? error.message : String(error),
        variant: "destructive",
      });
    }
  });

  // Handler para tentar alterar tema
  const handleThemeChange = useCallback((value: string) => {
    if (!isCustomizationAvailable) {
      if (isFreePlan) {
        setShowUpgradePrompt(true);
      } else {
        toast({
          title: t('subscription.feature_blocked.allowCustomization.title'),
          description: t('subscription.feature_blocked.allowCustomization.description'),
          variant: 'destructive',
        });
      }
      return;
    }

    setAccentColor(value);

    // Update form with selected theme colors
    const theme = themeOptions.find(option => option.value === value);
    if (theme) {
      form.setValue("colors.primary", theme.color);
      form.setValue("colors.secondary", theme.secondary);
      form.setValue("colors.accent", theme.accent);
    }
  }, [isCustomizationAvailable, isFreePlan, toast, t, form]);

  // Handler para tentar alterar layout
  const handleLayoutChange = useCallback((layoutType: number) => {
    if (!isCustomizationAvailable) {
      if (isFreePlan) {
        setShowUpgradePrompt(true);
      } else {
        toast({
          title: t('subscription.feature_blocked.allowCustomization.title'),
          description: t('subscription.feature_blocked.allowCustomization.description'),
          variant: 'destructive',
        });
      }
      return;
    }

    // Se a customização está disponível, permite a mudança
    form.setValue("layout", layoutType);
  }, [isCustomizationAvailable, isFreePlan, toast, t, form]);

  // Handle form submission
  const onSubmit = (values: StoreFormValues) => {
    // Validar número de telefone brasileiro
    if (values.countryCode === "+55" && values.whatsapp) {
      const numericOnly = values.whatsapp.replace(/\D/g, '');
      if (numericOnly.length < 10 || numericOnly.length > 11) {
        toast({
          title: "Erro de validação",
          description: "Número brasileiro deve conter DDD (2 dígitos) + número (8 ou 9 dígitos)",
          variant: "destructive",
        });
        return;
      }
    }

    storeMutation.mutate(values);
  };

  // Handle logo upload
  const handleLogoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Verificar se a loja está configurada antes de permitir upload
    if (!isStoreConfigured) {
      toast({
        title: t('settings.uploadDisabled'),
        description: t('settings.configureStoreFirst'),
        variant: "destructive",
      });

      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
      return;
    }

    try {
      setIsUploadingLogo(true);

      const result = await uploadLogo(file);

      // Update form with new logo URL
      form.setValue("logo", result.logo);

      toast({
        title: t('settings.logoUploaded'),
        description: t('settings.logoUploadSuccess'),
        variant: "default",
      });
    } catch (error) {
      toast({
        title: t('common.error'),
        description: error instanceof Error ? error.message : String(error),
        variant: "destructive",
      });
    } finally {
      setIsUploadingLogo(false);

      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  // Handle header image upload
  const handleHeaderImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Verificar se a loja está configurada antes de permitir upload
    if (!isStoreConfigured) {
      toast({
        title: t('settings.uploadDisabled'),
        description: t('settings.configureStoreFirst'),
        variant: "destructive",
      });

      // Reset file input
      if (headerImageFileInputRef.current) {
        headerImageFileInputRef.current.value = '';
      }
      return;
    }

    try {
      setIsUploadingHeaderImage(true);

      const result = await uploadHeaderImage(file);

      // Update form with new header image URL
      form.setValue("headerImage", result.headerImage);

      toast({
        title: t('settings.headerImageUploaded') || "Header image uploaded",
        description: t('settings.headerImageUploadSuccess') || "Your store header image has been updated successfully",
        variant: "default",
      });
    } catch (error) {
      toast({
        title: t('common.error'),
        description: error instanceof Error ? error.message : String(error),
        variant: "destructive",
      });
    } finally {
      setIsUploadingHeaderImage(false);

      // Reset file input
      if (headerImageFileInputRef.current) {
        headerImageFileInputRef.current.value = '';
      }
    }
  };

  // Trigger file input click for logo
  const triggerFileInput = () => {
    if (!isStoreConfigured) {
      toast({
        title: t('settings.storeNotConfigured'),
        description: t('settings.completeBasicInfo'),
        variant: "destructive",
      });
      return;
    }

    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // Trigger file input click for header image
  const triggerHeaderImageFileInput = () => {
    if (!isStoreConfigured) {
      toast({
        title: t('settings.storeNotConfigured'),
        description: t('settings.completeBasicInfo'),
        variant: "destructive",
      });
      return;
    }

    if (headerImageFileInputRef.current) {
      headerImageFileInputRef.current.click();
    }
  };

  // Handle add custom payment method
  const handleAddCustomMethod = () => {
    if (!newCustomMethod.trim()) return;

    const currentMethods = form.getValues("paymentMethods.customMethods") || [];

    // Check if method already exists
    if (currentMethods.includes(newCustomMethod.trim())) {
      toast({
        title: t('settings.paymentMethodExists'),
        description: t('settings.paymentMethodExistsDesc'),
        variant: "destructive",
      });
      return;
    }

    // Add new method
    form.setValue("paymentMethods.customMethods", [
      ...currentMethods,
      newCustomMethod.trim()
    ]);

    // Clear input
    setNewCustomMethod("");
  };

  // Handle remove custom payment method
  const handleRemoveCustomMethod = (method: string) => {
    const currentMethods = form.getValues("paymentMethods.customMethods") || [];
    form.setValue(
      "paymentMethods.customMethods",
      currentMethods.filter(m => m !== method)
    );
  };

  // Preview store
  const handlePreviewStore = () => {
    if (store) {
      window.open(`/${store.slug}`, '_blank');
    }
  };

  // Funções para gerenciar períodos de indisponibilidade
  const handleAddUnavailablePeriod = () => {
    if (!newPeriodStartDate || !newPeriodEndDate) {
      toast({
        title: "Datas obrigatórias",
        description: "Preencha as datas inicial e final para o período de indisponibilidade",
        variant: "destructive",
      });
      return;
    }

    const currentPeriods = form.getValues("deliverySettings.unavailablePeriods") || [];

    const newPeriod = {
      id: editingPeriodId || `period-${Date.now()}`,
      startDate: newPeriodStartDate,
      endDate: newPeriodEndDate,
      reason: newPeriodReason
    };

    if (editingPeriodId) {
      // Atualizar período existente
      form.setValue(
        "deliverySettings.unavailablePeriods",
        currentPeriods.map(period => period.id === editingPeriodId ? newPeriod : period)
      );
      toast({
        title: "Período atualizado",
        description: "O período de indisponibilidade foi atualizado com sucesso",
      });
    } else {
      // Adicionar novo período
      form.setValue(
        "deliverySettings.unavailablePeriods",
        [...currentPeriods, newPeriod]
      );
      toast({
        title: "Período adicionado",
        description: "O período de indisponibilidade foi adicionado com sucesso",
      });
    }

    // Limpar formulário
    resetUnavailablePeriodForm();
  };

  const handleEditUnavailablePeriod = (period: any) => {
    setEditingPeriodId(period.id);
    setNewPeriodStartDate(period.startDate);
    setNewPeriodEndDate(period.endDate);
    setNewPeriodReason(period.reason || "");
    setShowUnavailablePeriodForm(true);
  };

  const handleRemoveUnavailablePeriod = (periodId: string) => {
    const currentPeriods = form.getValues("deliverySettings.unavailablePeriods") || [];
    form.setValue(
      "deliverySettings.unavailablePeriods",
      currentPeriods.filter(period => period.id !== periodId)
    );
    toast({
      title: "Período removido",
      description: "O período de indisponibilidade foi removido com sucesso",
    });
  };

  const resetUnavailablePeriodForm = () => {
    setEditingPeriodId(null);
    setNewPeriodStartDate("");
    setNewPeriodEndDate("");
    setNewPeriodReason("");
    setShowUnavailablePeriodForm(false);
  };

  return (
    <div className="space-y-6">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <Accordion type="multiple" defaultValue={["info"]} className="w-full space-y-4">
            {/* INFORMAÇÕES DA LOJA */}
            <AccordionItem value="info" className="border rounded-lg overflow-hidden">
              <AccordionTrigger className="px-4 py-3 bg-neutral-50 hover:bg-neutral-100">
                <div className="flex items-center">
                  <Store className="mr-2 h-4 w-4" />
                  <span>{t('settings.storeInfo')}</span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="p-4">
                <div className="space-y-4">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('settings.storeName')}</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="slug"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('settings.storeUrl')}</FormLabel>
                        <div className="flex">
                          <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-input bg-muted text-muted-foreground text-sm">
                            {t('settings.storeUrlPrefix')}
                          </span>
                          <FormControl>
                            <Input
                              {...field}
                              className="rounded-l-none"
                            />
                          </FormControl>
                        </div>
                        <FormDescription>
                          {t('settings.urlSlugInUse')}
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('settings.storeDescription')}</FormLabel>
                        <FormControl>
                          <Textarea
                            {...field}
                            placeholder={t('settings.storeDescription')}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="logo"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('settings.storeLogo')}</FormLabel>
                        <div className="space-y-2">
                          {/* Campo oculto para armazenar a URL do logo */}
                          <div className="hidden">
                            <FormControl>
                              <Input
                                {...field}
                                type="hidden"
                              />
                            </FormControl>
                          </div>

                          {/* Hidden file input */}
                          <input
                            type="file"
                            ref={fileInputRef}
                            className="hidden"
                            accept="image/*"
                            onChange={handleLogoUpload}
                          />

                          {/* Logo preview and upload button */}
                          <div className="flex items-center gap-4 mt-2">
                            {field.value && (
                              <div className="relative w-16 h-16 border rounded overflow-hidden">
                                <img
                                  src={field.value}
                                  alt="Store logo"
                                  className="object-cover w-full h-full"
                                  onError={(e) => {
                                    // Handle image loading error
                                    (e.target as HTMLImageElement).src = 'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="18" height="18" x="3" y="3" rx="2" ry="2"/><circle cx="9" cy="9" r="2"/><path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/></svg>';
                                  }}
                                />
                              </div>
                            )}

                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={triggerFileInput}
                              disabled={isUploadingLogo || !isStoreConfigured}
                              className={`flex items-center gap-2 ${!isStoreConfigured ? 'opacity-50 cursor-not-allowed' : ''}`}
                              title={!isStoreConfigured ? t('settings.completeBasicInfo') : ''}
                            >
                              {isUploadingLogo ? (
                                <span className="animate-spin">⟳</span>
                              ) : !isStoreConfigured ? (
                                <AlertTriangle className="w-4 h-4" />
                              ) : (
                                <Upload className="w-4 h-4" />
                              )}
                              {t('settings.uploadLogo')}
                            </Button>
                          </div>

                          <FormDescription>
                            {t('settings.logoUpload')} ({t('settings.maxSize')})
                          </FormDescription>
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <FormField
                      control={form.control}
                      name="countryCode"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Código do País</FormLabel>
                          <Select
                            value={field.value.startsWith('+')
                              ? countryCodes.find(c => c.code.startsWith(field.value.split(' ')[0]))?.id || field.value
                              : field.value}
                            onValueChange={(value) => {
                              // Para qualquer país selecionado, armazena o código telefônico internacional
                              const country = countryCodes.find(c => c.id === value);
                              if (country) {
                                // Remove qualquer texto entre parênteses, incluindo os próprios parênteses
                                const cleanCode = country.code.replace(/\s*\([^)]*\)\s*/g, '');
                                field.onChange(cleanCode);

                                // Verificar se o número de telefone atual está no formato correto para o novo país
                                const currentWhatsapp = form.getValues("whatsapp");
                                if (currentWhatsapp) {
                                  // Se o novo país for Brasil, validar o formato brasileiro
                                  if (value === "br" || cleanCode === "+55") {
                                    const numericOnly = currentWhatsapp.replace(/\D/g, '');
                                    if (numericOnly.length < 10 || numericOnly.length > 11) {
                                      toast({
                                        title: "Atenção",
                                        description: "O número de WhatsApp atual não está no formato brasileiro correto (DDD + número). Por favor, verifique.",
                                        variant: "warning",
                                      });
                                    }
                                  }
                                }
                              } else {
                                field.onChange(value);
                              }
                            }}
                          >
                            <FormControl>
                              <SelectTrigger className="w-full">
                                <SelectValue placeholder="Selecione o código do país" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {countryCodes.map((country) => (
                                <SelectItem key={country.id} value={country.id}>
                                  <div className="flex items-center">
                                    <span className="mr-2">{country.flag}</span>
                                    <span>{country.name}</span>
                                    <span className="ml-1 text-muted-foreground">{country.code}</span>
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="md:col-span-2">
                      <FormField
                        control={form.control}
                        name="whatsapp"
                        render={({ field }) => {
                          // Verificar se o país selecionado é o Brasil
                          const isBrazil = form.watch("countryCode") === "+55";
                          const [error, setError] = useState<string | null>(null);

                          // Função para validar o número de telefone brasileiro
                          const validateBrazilianPhone = (value: string) => {
                            if (!value) return true; // Campo opcional

                            // Remover todos os caracteres não numéricos
                            const numericOnly = value.replace(/\D/g, '');

                            // Verificar se tem entre 10 e 11 dígitos (DDD + número)
                            if (numericOnly.length < 10 || numericOnly.length > 11) {
                              setError("Número brasileiro deve conter DDD (2 dígitos) + número (8 ou 9 dígitos)");
                              return false;
                            }

                            setError(null);
                            return true;
                          };

                          return (
                            <FormItem>
                              <FormLabel>WhatsApp</FormLabel>
                              <div className="flex items-center space-x-2">
                                <Phone className="w-4 h-4 text-muted-foreground" />
                                <FormControl>
                                  <Input
                                    {...field}
                                    placeholder={isBrazil ? "(DDD) 00000-0000" : "Número de telefone"}
                                    onChange={(e) => {
                                      field.onChange(e);
                                      if (isBrazil) {
                                        validateBrazilianPhone(e.target.value);
                                      } else {
                                        setError(null);
                                      }
                                    }}
                                    onBlur={(e) => {
                                      field.onBlur();
                                      if (isBrazil) {
                                        validateBrazilianPhone(e.target.value);
                                      }
                                    }}
                                  />
                                </FormControl>
                              </div>
                              <FormDescription>
                                {isBrazil
                                  ? "Número de WhatsApp brasileiro com DDD (2 dígitos) + número (8 ou 9 dígitos), sem o código do país"
                                  : "Número de WhatsApp para contato com a loja, sem o código do país"
                                }
                              </FormDescription>
                              {error && (
                                <div className="text-sm font-medium text-destructive mt-1">
                                  {error}
                                </div>
                              )}
                              <FormMessage />
                            </FormItem>
                          );
                        }}
                      />
                    </div>
                  </div>

                  <FormField
                    control={form.control}
                    name="instagram"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Instagram</FormLabel>
                        <div className="flex items-center space-x-2">
                          <Instagram className="w-4 h-4 text-muted-foreground" />
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="@nomeusuario"
                            />
                          </FormControl>
                        </div>
                        <FormDescription>
                          Usuário do Instagram da loja (sem o @)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="currency"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Moeda</FormLabel>
                        <div className="flex items-center space-x-2">
                          <DollarSign className="w-4 h-4 text-muted-foreground" />
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="R$"
                            />
                          </FormControl>
                        </div>
                        <FormDescription>
                          Símbolo da moeda utilizada na loja (R$, $, €, etc.)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="contactEmail"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email de Contato</FormLabel>
                        <div className="flex items-center space-x-2">
                          <Mail className="w-4 h-4 text-muted-foreground" />
                          <FormControl>
                            <Input
                              {...field}
                              type="email"
                              placeholder="<EMAIL>"
                            />
                          </FormControl>
                        </div>
                        <FormDescription>
                          Email de contato da loja para clientes
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* ENDEREÇO DA LOJA */}
            <AccordionItem value="address" className="border rounded-lg overflow-hidden">
              <AccordionTrigger className="px-4 py-3 bg-neutral-50 hover:bg-neutral-100">
                <div className="flex items-center">
                  <MapPin className="mr-2 h-4 w-4" />
                  <span>Endereço da Loja</span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="p-4">
                <div className="space-y-4">
                  <p className="text-sm text-muted-foreground">
                    Informe o endereço completo da sua loja. Estas informações poderão ser exibidas para seus clientes.
                  </p>

                  <FormField
                    control={form.control}
                    name="addressStreet"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Rua/Logradouro</FormLabel>
                        <FormControl>
                          <Input {...field} placeholder="Av. Brasil" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="addressNumber"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Número</FormLabel>
                          <FormControl>
                            <Input {...field} placeholder="123" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="addressComplement"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Complemento</FormLabel>
                          <FormControl>
                            <Input {...field} placeholder="Sala 101" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="addressNeighborhood"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Bairro</FormLabel>
                        <FormControl>
                          <Input {...field} placeholder="Centro" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="addressCity"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Cidade</FormLabel>
                          <FormControl>
                            <Input {...field} placeholder="São Paulo" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="addressState"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Estado</FormLabel>
                          <FormControl>
                            <Input {...field} placeholder="SP" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* VISUAL */}
            <AccordionItem value="visual" className="border rounded-lg overflow-hidden">
              <AccordionTrigger className="px-4 py-3 bg-neutral-50 hover:bg-neutral-100">
                <div className="flex items-center">
                  <PaintBucket className="mr-2 h-4 w-4" />
                  <span>{t('settings.visual')}</span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="p-4">
                {/* Prompt de upgrade para plano gratuito */}
                {showUpgradePrompt && isFreePlan && (
                  <div className="mb-6">
                    <UpgradePrompt
                      title={t('subscription.feature_blocked.allowCustomization.title')}
                      description={t('subscription.feature_blocked.allowCustomization.description')}
                      feature="allowCustomization"
                      variant="alert"
                      onClose={() => setShowUpgradePrompt(false)}
                    />
                  </div>
                )}

                <div className="space-y-4">
                  <FormField
                    control={form.control}
                    name="headerImage"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('settings.storeHeaderImage') || "Imagem de Cabeçalho da Loja"}</FormLabel>
                        <div className="space-y-2">
                          {/* Campo oculto para armazenar a URL da imagem */}
                          <div className="hidden">
                            <FormControl>
                              <Input
                                {...field}
                                type="hidden"
                              />
                            </FormControl>
                          </div>

                          {/* Hidden file input */}
                          <input
                            type="file"
                            ref={headerImageFileInputRef}
                            className="hidden"
                            accept="image/*"
                            onChange={handleHeaderImageUpload}
                          />

                          {/* Header image preview and upload button */}
                          <div className="mt-2">
                            {field.value && (
                              <div className="relative w-full h-32 border rounded overflow-hidden mb-2">
                                <img
                                  src={field.value}
                                  alt="Store header"
                                  className="object-cover w-full h-full"
                                  onError={(e) => {
                                    // Handle image loading error
                                    (e.target as HTMLImageElement).src = 'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="18" height="18" x="3" y="3" rx="2" ry="2"/><circle cx="9" cy="9" r="2"/><path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/></svg>';
                                  }}
                                />
                              </div>
                            )}

                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={triggerHeaderImageFileInput}
                              disabled={isUploadingHeaderImage || !isStoreConfigured}
                              className={`flex items-center gap-2 ${!isStoreConfigured ? 'opacity-50 cursor-not-allowed' : ''}`}
                              title={!isStoreConfigured ? t('settings.completeBasicInfo') : ''}
                            >
                              {isUploadingHeaderImage ? (
                                <span className="animate-spin">⟳</span>
                              ) : !isStoreConfigured ? (
                                <AlertTriangle className="w-4 h-4" />
                              ) : (
                                <Image className="w-4 h-4" />
                              )}
                              {t('settings.uploadHeaderImage') || "Enviar imagem de cabeçalho"}
                            </Button>
                          </div>

                          <FormDescription>
                            {t('settings.headerImageDesc') || "Envie a imagem de cabeçalho da sua loja"} ({t('settings.maxSize') || "máx. 2MB"})
                          </FormDescription>
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div>
                    <div className="flex items-center gap-2 mb-2">
                      <h3 className="text-sm font-medium">{t('settings.themeColor') || "Cores do Tema"}</h3>
                      {!isCustomizationAvailable && isFreePlan && (
                        <Crown className="h-4 w-4 text-yellow-600" />
                      )}
                    </div>
                    {!isCustomizationAvailable && (
                      <p className="text-xs text-muted-foreground mb-3">
                        {t('subscription.feature_blocked.allowCustomization.description')}
                      </p>
                    )}
                    <div className={`grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-2 ${!isCustomizationAvailable ? 'opacity-50' : ''}`}>
                      {themeOptions.map(option => (
                        <div
                          key={option.value}
                          className={`
                            border rounded p-1 transition-all
                            ${!isCustomizationAvailable
                              ? 'cursor-not-allowed'
                              : 'cursor-pointer hover:bg-accent'
                            }
                            ${accentColor === option.value ? 'ring-2 ring-primary' : ''}
                          `}
                          onClick={() => handleThemeChange(option.value)}
                          title={!isCustomizationAvailable ? t('subscription.upgrade_required') : ''}
                        >
                          <div className="flex items-center gap-2">
                            <div
                              className="w-5 h-5 rounded-full"
                              style={{ backgroundColor: option.color }}
                            />
                            <span className="text-xs">{option.label}</span>
                            {!isCustomizationAvailable && isFreePlan && (
                              <Crown className="h-3 w-3 text-yellow-600 ml-auto" />
                            )}
                          </div>
                          <div className="flex mt-1 gap-1">
                            <div
                              className="w-3 h-3 rounded-full"
                              style={{ backgroundColor: option.secondary }}
                            />
                            <div
                              className="w-3 h-3 rounded-full"
                              style={{ backgroundColor: option.accent }}
                            />
                          </div>
                        </div>
                      ))}
                    </div>
                    <FormField
                      control={form.control}
                      name="colors.primary"
                      render={({ field }) => (
                        <FormItem className="hidden">
                          <FormControl>
                            <Input {...field} type="hidden" />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="colors.secondary"
                      render={({ field }) => (
                        <FormItem className="hidden">
                          <FormControl>
                            <Input {...field} type="hidden" />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="colors.accent"
                      render={({ field }) => (
                        <FormItem className="hidden">
                          <FormControl>
                            <Input {...field} type="hidden" />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Layout options moved inside Visual section */}
                  <div className="space-y-4 pt-4 border-t">
                    <div className="flex items-center gap-2">
                      <h3 className="text-sm font-medium">{t('settings.layout') || "Layout da Loja"}</h3>
                      {!isCustomizationAvailable && isFreePlan && (
                        <Crown className="h-4 w-4 text-yellow-600" />
                      )}
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {!isCustomizationAvailable
                        ? t('subscription.feature_blocked.allowCustomization.description')
                        : (t('settings.layoutOptionsDesc') || "Escolha como os produtos serão exibidos na sua loja")
                      }
                    </p>

                    <FormField
                      control={form.control}
                      name="layout"
                      render={({ field }) => (
                        <FormItem>
                          <div className={!isCustomizationAvailable ? 'opacity-50 pointer-events-none' : ''}>
                            <StoreLayoutSelector
                              value={field.value}
                              onChange={(layoutType) => {
                                if (isCustomizationAvailable) {
                                  field.onChange(layoutType);
                                } else {
                                  handleLayoutChange(layoutType);
                                }
                              }}
                            />
                          </div>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="flex justify-end">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={handlePreviewStore}
                        className="flex items-center gap-2 ml-auto"
                        disabled={!store}
                        title={!isCustomizationAvailable ? t('subscription.upgrade_required') : ''}
                      >
                        <Eye className="h-4 w-4" />
                        {t('settings.previewSelectedLayout') || "Visualizar layout selecionado"}
                      </Button>
                    </div>
                  </div>

                  {/* Upgrade prompt quando funcionalidade não disponível */}
                  {!isCustomizationAvailable && isFreePlan && (
                    <div className="mt-6">
                      <UpgradePrompt
                        title="Personalização visual disponível no plano Premium"
                        description="Personalize as cores e layout da sua loja para criar uma experiência única para seus clientes. Faça upgrade para o plano Premium e tenha acesso a esta funcionalidade."
                        variant="card"
                      />
                    </div>
                  )}
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* FORMAS DE PAGAMENTO */}
            <AccordionItem value="payments" className="border rounded-lg overflow-hidden">
              <AccordionTrigger className="px-4 py-3 bg-neutral-50 hover:bg-neutral-100">
                <div className="flex items-center">
                  <CreditCard className="mr-2 h-4 w-4" />
                  <span>{t('settings.payments')}</span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="p-4">
                <div className="space-y-4">
                  <p className="text-sm text-muted-foreground">
                    {t('settings.paymentMethodsDesc')}
                  </p>

                  <div className="space-y-4">
                    <h3 className="text-sm font-medium">{t('settings.standardPaymentMethods')}</h3>
                    <FormField
                      control={form.control}
                      name="paymentMethods.cash"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                          <FormControl>
                            <Checkbox
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel>
                              {t('settings.cashPayment')}
                            </FormLabel>
                          </div>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="paymentMethods.creditCard"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                          <FormControl>
                            <Checkbox
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel>
                              {t('settings.creditCardPayment')}
                            </FormLabel>
                          </div>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="paymentMethods.debitCard"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                          <FormControl>
                            <Checkbox
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel>
                              {t('settings.debitCardPayment')}
                            </FormLabel>
                          </div>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="paymentMethods.pix"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                          <FormControl>
                            <Checkbox
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel>
                              {t('settings.pixPayment')}
                            </FormLabel>
                          </div>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="paymentMethods.bankTransfer"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                          <FormControl>
                            <Checkbox
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel>
                              {t('settings.bankTransferPayment')}
                            </FormLabel>
                          </div>
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="space-y-3 pt-3 border-t">
                    <h3 className="text-sm font-medium">{t('settings.customPaymentMethods')}</h3>
                    <div className="flex space-x-2">
                      <Input
                        value={newCustomMethod}
                        onChange={(e) => setNewCustomMethod(e.target.value)}
                        placeholder={t('settings.customPaymentMethodPlaceholder')}
                        className="flex-1"
                      />
                      <Button
                        type="button"
                        size="sm"
                        onClick={handleAddCustomMethod}
                      >
                        <Plus className="h-4 w-4 mr-1" />
                        {t('settings.add')}
                      </Button>
                    </div>

                    <div className="space-y-2">
                      {form.watch("paymentMethods.customMethods")?.map((method, index) => (
                        <div key={method + index} className="flex items-center justify-between p-2 border rounded-md">
                          <span>{method}</span>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => handleRemoveCustomMethod(method)}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* ENTREGA E RETIRADA */}
            <AccordionItem value="delivery" className="border rounded-lg overflow-hidden">
              <AccordionTrigger className="px-4 py-3 bg-neutral-50 hover:bg-neutral-100">
                <div className="flex items-center">
                  <Truck className="mr-2 h-4 w-4" />
                  <span>Entrega e Retirada</span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="p-4">
                <div className="space-y-4">
                  <p className="text-sm text-muted-foreground">
                    {t('settings.deliveryDesc') || "Configure as opções de entrega e retirada para sua loja"}
                  </p>

                  <FormField
                    control={form.control}
                    name="deliverySettings.allowDelivery"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>
                            {t('settings.allowDelivery') || "Permitir entrega"}
                          </FormLabel>
                          <FormDescription>
                            {t('settings.allowDeliveryDesc') || "Ative esta opção se sua loja faz entregas."}
                          </FormDescription>
                        </div>
                      </FormItem>
                    )}
                  />

                  {/* Configurações adicionais de entrega - exibidas apenas quando entrega está ativa */}
                  {form.watch("deliverySettings.allowDelivery") && (
                    <div className="space-y-4 bg-muted/20 p-4 rounded-md mt-2 border">
                      <h3 className="font-medium text-sm">Configurações de entrega</h3>

                      {/* Dias da semana permitidos para entrega */}
                      <div className="space-y-2">
                        <p className="text-sm">Dias permitidos para entrega:</p>
                        <div className="grid grid-cols-2 sm:grid-cols-4 gap-2">
                          {[
                            { value: "0", label: "Domingo" },
                            { value: "1", label: "Segunda" },
                            { value: "2", label: "Terça" },
                            { value: "3", label: "Quarta" },
                            { value: "4", label: "Quinta" },
                            { value: "5", label: "Sexta" },
                            { value: "6", label: "Sábado" },
                          ].map((day) => (
                            <FormField
                              key={day.value}
                              control={form.control}
                              name="deliverySettings.deliveryDays"
                              render={({ field }) => {
                                const isSelected = field.value?.includes(day.value);
                                return (
                                  <FormItem className="flex items-center space-x-2 space-y-0">
                                    <FormControl>
                                      <Checkbox
                                        checked={isSelected}
                                        onCheckedChange={(checked) => {
                                          if (checked) {
                                            field.onChange([...field.value || [], day.value]);
                                          } else {
                                            field.onChange(
                                              field.value?.filter((value: string) => value !== day.value) || []
                                            );
                                          }
                                        }}
                                      />
                                    </FormControl>
                                    <FormLabel className="!m-0">{day.label}</FormLabel>
                                  </FormItem>
                                );
                              }}
                            />
                          ))}
                        </div>
                      </div>

                      {/* Horários de entrega */}
                      <div className="space-y-4">
                        <div className="flex justify-between items-center">
                          <h4 className="text-sm font-medium">Horários disponíveis para entrega</h4>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              const currentSlots = form.getValues("deliverySettings.deliveryTimeSlots") || [];
                              const newTimeSlot = "";

                              form.setValue(
                                "deliverySettings.deliveryTimeSlots",
                                [...currentSlots, newTimeSlot]
                              );
                            }}
                          >
                            <Plus className="h-4 w-4 mr-1" />
                            Adicionar horário
                          </Button>
                        </div>

                        <div className="space-y-2">
                          {form.watch("deliverySettings.deliveryTimeSlots").length === 0 ? (
                            <div className="text-sm text-muted-foreground italic">
                              Nenhum horário adicionado. Clique em "Adicionar horário" para começar.
                            </div>
                          ) : (
                            form.watch("deliverySettings.deliveryTimeSlots").map((timeSlot: string, index: number) => (
                              <div key={index} className="flex items-center space-x-2">
                                <Input
                                  type="time"
                                  value={timeSlot}
                                  onChange={(e) => {
                                    const updatedSlots = [...form.getValues("deliverySettings.deliveryTimeSlots")];
                                    updatedSlots[index] = e.target.value;
                                    form.setValue("deliverySettings.deliveryTimeSlots", updatedSlots);
                                  }}
                                  className="flex-1"
                                />
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => {
                                    const updatedSlots = form.getValues("deliverySettings.deliveryTimeSlots")
                                      .filter((_: string, i: number) => i !== index);
                                    form.setValue("deliverySettings.deliveryTimeSlots", updatedSlots);
                                  }}
                                >
                                  <X className="h-4 w-4 text-destructive" />
                                </Button>
                              </div>
                            ))
                          )}
                        </div>

                        <FormDescription>
                          Adicione os horários em que sua loja realiza entregas.
                          Os clientes poderão selecionar um destes horários ao fazer o pedido.
                        </FormDescription>
                      </div>

                      {/* Taxa de entrega fixa */}
                      <div className="space-y-2">
                        <FormField
                          control={form.control}
                          name="deliverySettings.deliveryFee"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                Taxa de entrega fixa ({form.getValues("currency")})
                              </FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  min="0"
                                  step="0.01"
                                  {...field}
                                  value={field.value === undefined ? 0 : field.value}
                                  onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                                />
                              </FormControl>
                              <FormDescription>
                                Taxa fixa cobrada por entrega. Deixe zero para não cobrar taxa.
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* Prazo mínimo de antecedência */}
                      <div className="space-y-2">
                        <FormField
                          control={form.control}
                          name="deliverySettings.minAdvanceDays"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                Prazo mínimo de antecedência (em dias)
                              </FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  min="0"
                                  step="1"
                                  {...field}
                                  value={field.value === undefined ? 0 : field.value}
                                  onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                                />
                              </FormControl>
                              <FormDescription>
                                Quantos dias de antecedência o cliente precisa fazer o pedido.
                                Ex: se for 2, o cliente só pode agendar pedidos para 2 dias depois da data atual.
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>
                  )}

                  <FormField
                    control={form.control}
                    name="deliverySettings.allowPickup"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>
                            {t('settings.allowPickup') || "Permitir retirada por encomenda"}
                          </FormLabel>
                          <FormDescription>
                            {t('settings.allowPickupDesc') || "Ative esta opção se sua loja permite retirada dos pedidos."}
                          </FormDescription>
                        </div>
                      </FormItem>
                    )}
                  />

                  {/* Configurações adicionais de retirada - exibidas apenas quando retirada está ativa */}
                  {form.watch("deliverySettings.allowPickup") && (
                    <div className="space-y-4 bg-muted/20 p-4 rounded-md mt-2 border">
                      <h3 className="font-medium text-sm">Configurações de retirada</h3>

                      {/* Dias da semana permitidos para retirada */}
                      <div className="space-y-2">
                        <p className="text-sm">Dias permitidos para retirada:</p>
                        <div className="grid grid-cols-2 sm:grid-cols-4 gap-2">
                          {[
                            { value: "0", label: "Domingo" },
                            { value: "1", label: "Segunda" },
                            { value: "2", label: "Terça" },
                            { value: "3", label: "Quarta" },
                            { value: "4", label: "Quinta" },
                            { value: "5", label: "Sexta" },
                            { value: "6", label: "Sábado" },
                          ].map((day) => (
                            <FormField
                              key={day.value}
                              control={form.control}
                              name="deliverySettings.pickupDays"
                              render={({ field }) => {
                                const isSelected = field.value?.includes(day.value);
                                return (
                                  <FormItem className="flex items-center space-x-2 space-y-0">
                                    <FormControl>
                                      <Checkbox
                                        checked={isSelected}
                                        onCheckedChange={(checked) => {
                                          if (checked) {
                                            field.onChange([...field.value || [], day.value]);
                                          } else {
                                            field.onChange(
                                              field.value?.filter((value: string) => value !== day.value) || []
                                            );
                                          }
                                        }}
                                      />
                                    </FormControl>
                                    <FormLabel className="!m-0">{day.label}</FormLabel>
                                  </FormItem>
                                );
                              }}
                            />
                          ))}
                        </div>
                      </div>

                      {/* Horários de retirada */}
                      <div className="space-y-4">
                        <div className="flex justify-between items-center">
                          <h4 className="text-sm font-medium">Horários disponíveis para retirada</h4>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              const currentSlots = form.getValues("deliverySettings.pickupTimeSlots") || [];
                              const newTimeSlot = "";

                              form.setValue(
                                "deliverySettings.pickupTimeSlots",
                                [...currentSlots, newTimeSlot]
                              );
                            }}
                          >
                            <Plus className="h-4 w-4 mr-1" />
                            Adicionar horário
                          </Button>
                        </div>

                        <div className="space-y-2">
                          {form.watch("deliverySettings.pickupTimeSlots").length === 0 ? (
                            <div className="text-sm text-muted-foreground italic">
                              Nenhum horário adicionado. Clique em "Adicionar horário" para começar.
                            </div>
                          ) : (
                            form.watch("deliverySettings.pickupTimeSlots").map((timeSlot: string, index: number) => (
                              <div key={index} className="flex items-center space-x-2">
                                <Input
                                  type="time"
                                  value={timeSlot}
                                  onChange={(e) => {
                                    const updatedSlots = [...form.getValues("deliverySettings.pickupTimeSlots")];
                                    updatedSlots[index] = e.target.value;
                                    form.setValue("deliverySettings.pickupTimeSlots", updatedSlots);
                                  }}
                                  className="flex-1"
                                />
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => {
                                    const updatedSlots = form.getValues("deliverySettings.pickupTimeSlots")
                                      .filter((_: string, i: number) => i !== index);
                                    form.setValue("deliverySettings.pickupTimeSlots", updatedSlots);
                                  }}
                                >
                                  <X className="h-4 w-4 text-destructive" />
                                </Button>
                              </div>
                            ))
                          )}
                        </div>

                        <FormDescription>
                          Adicione os horários em que os clientes podem retirar seus pedidos.
                          Os clientes poderão selecionar um destes horários ao fazer o pedido.
                        </FormDescription>
                      </div>
                    </div>
                  )}

                  {/* Mensagem personalizada para o cliente */}
                  <div className="mt-4 space-y-2">
                    <FormField
                      control={form.control}
                      name="deliverySettings.customMessage"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Mensagem personalizada para o cliente
                          </FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Ex: Pedidos com no mínimo 2 dias de antecedência. Retiradas de terça a sábado, das 14h às 20h."
                              className="min-h-[80px]"
                              {...field}
                            />
                          </FormControl>
                          <FormDescription>
                            Esta mensagem será exibida para o cliente na finalização do pedido.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Períodos de Indisponibilidade */}
                  <div className="mt-6 space-y-4">
                    <div className="flex justify-between items-center">
                      <h3 className="font-medium">Períodos de Indisponibilidade</h3>
                      {!showUnavailablePeriodForm && (
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setShowUnavailablePeriodForm(true);
                            setEditingPeriodId(null);
                            setNewPeriodStartDate("");
                            setNewPeriodEndDate("");
                            setNewPeriodReason("");
                          }}
                        >
                          <CalendarX className="h-4 w-4 mr-2" />
                          Adicionar Período
                        </Button>
                      )}
                    </div>

                    <FormDescription>
                      Cadastre períodos em que a loja não estará disponível para entrega ou retirada de pedidos (férias, feriados, etc).
                      Estas datas serão bloqueadas no calendário do cliente.
                    </FormDescription>

                    {/* Formulário para adicionar ou editar período */}
                    {showUnavailablePeriodForm && (
                      <div className="border p-4 rounded-md space-y-4 bg-muted/20">
                        <h4 className="text-sm font-medium">
                          {editingPeriodId ? "Editar Período" : "Novo Período"}
                        </h4>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <label className="text-sm font-medium">Data Inicial</label>
                            <Input
                              type="date"
                              value={newPeriodStartDate}
                              onChange={(e) => setNewPeriodStartDate(e.target.value)}
                              className="w-full"
                            />
                          </div>

                          <div className="space-y-2">
                            <label className="text-sm font-medium">Data Final</label>
                            <Input
                              type="date"
                              value={newPeriodEndDate}
                              onChange={(e) => setNewPeriodEndDate(e.target.value)}
                              className="w-full"
                            />
                          </div>
                        </div>

                        <div className="space-y-2">
                          <label className="text-sm font-medium">Motivo (opcional)</label>
                          <Input
                            type="text"
                            value={newPeriodReason}
                            onChange={(e) => setNewPeriodReason(e.target.value)}
                            placeholder="Ex: Férias, Feriado, Manutenção"
                            className="w-full"
                          />
                        </div>

                        <div className="flex justify-end space-x-2">
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={resetUnavailablePeriodForm}
                          >
                            Cancelar
                          </Button>
                          <Button
                            type="button"
                            size="sm"
                            onClick={handleAddUnavailablePeriod}
                          >
                            {editingPeriodId ? "Atualizar" : "Adicionar"}
                          </Button>
                        </div>
                      </div>
                    )}

                    {/* Lista de períodos cadastrados */}
                    <div className="space-y-2">
                      {form.watch("deliverySettings.unavailablePeriods")?.length === 0 ? (
                        <div className="text-sm text-muted-foreground italic">
                          Nenhum período de indisponibilidade cadastrado.
                        </div>
                      ) : (
                        <div className="space-y-2">
                          {form.watch("deliverySettings.unavailablePeriods").map((period: any) => (
                            <div key={period.id} className="flex items-center justify-between border rounded-md p-3">
                              <div className="space-y-1">
                                <div className="font-medium text-sm flex items-center">
                                  <CalendarX className="h-4 w-4 mr-2 text-destructive" />
                                  {period.startDate === period.endDate
                                    ? format(new Date(period.startDate), "dd/MM/yyyy")
                                    : `${format(new Date(period.startDate), "dd/MM/yyyy")} - ${format(new Date(period.endDate), "dd/MM/yyyy")}`
                                  }
                                </div>
                                {period.reason && (
                                  <div className="text-sm text-muted-foreground">
                                    {period.reason}
                                  </div>
                                )}
                              </div>
                              <div className="flex space-x-1">
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => handleEditUnavailablePeriod(period)}
                                >
                                  <PencilIcon className="h-4 w-4" />
                                </Button>
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => handleRemoveUnavailablePeriod(period.id)}
                                >
                                  <Trash className="h-4 w-4 text-destructive" />
                                </Button>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="mt-4 text-sm text-amber-600 border border-amber-200 bg-amber-50 p-3 rounded-md">
                    {t('settings.deliveryWarning') || "Se nenhuma destas opções estiver marcada, o cliente não poderá finalizar o pedido."}
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* PLANOS DE ASSINATURA */}
            <AccordionItem value="subscription" className="border rounded-lg overflow-hidden">
              <AccordionTrigger className="px-4 py-3 bg-neutral-50 hover:bg-neutral-100">
                <div className="flex items-center">
                  <Crown className="mr-2 h-4 w-4" />
                  <span>{t('subscription.title')}</span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="p-4 space-y-4">
                <SubscriptionSettings />

                {/* Botão de teste grátis para usuários do plano gratuito */}
                <div className="flex justify-center pt-4 border-t">
                  <StartTrialButton
                    size="lg"
                    className="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white border-0"
                  />
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* IDIOMAS */}
            <AccordionItem value="languages" className="border rounded-lg overflow-hidden">
              <AccordionTrigger className="px-4 py-3 bg-neutral-50 hover:bg-neutral-100">
                <div className="flex items-center">
                  <Globe className="mr-2 h-4 w-4" />
                  <span>{t('settings.languages')}</span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="p-4">
                <div className="space-y-4">
                  <p className="text-sm text-muted-foreground">
                    {t('settings.languagesDesc')}
                  </p>

                  <Select
                    value={currentLanguage}
                    onValueChange={(value) => changeLanguage(value)}
                  >
                    <SelectTrigger className="w-full md:w-[200px]">
                      <SelectValue placeholder={t('settings.selectLanguage')} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="pt">
                        <div className="flex items-center">
                          <span className="mr-2">🇧🇷</span>
                          {t('settings.portuguese')}
                        </div>
                      </SelectItem>
                      <SelectItem value="en">
                        <div className="flex items-center">
                          <span className="mr-2">🇺🇸</span>
                          {t('settings.english')}
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Layout section removed - moved inside Visual section */}
          </Accordion>

          {/* Action Buttons */}
          <div className="mt-6 flex justify-between">
            <Button
              type="button"
              variant="outline"
              onClick={handlePreviewStore}
              disabled={!store}
            >
              {t('settings.previewStore')}
            </Button>
            <Button
              type="submit"
              disabled={storeMutation.isPending}
            >
              {storeMutation.isPending ? t('common.loading') : t('settings.saveChanges')}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}

export default StoreSettings;