import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/api';
import { RefreshCw, CheckCircle, XCircle, AlertTriangle, Clock, Database } from 'lucide-react';

interface SyncResult {
  success: boolean;
  subscriptionId?: string;
  localId?: number;
  error?: string;
  changes?: string[];
  timestamp: Date;
}

interface BulkSyncResult {
  totalProcessed: number;
  successful: number;
  failed: number;
  results: SyncResult[];
  duration: number;
}

interface SyncStats {
  totalSyncs: number;
  successfulSyncs: number;
  failedSyncs: number;
  lastSync?: string;
  recentErrors: string[];
}

interface SubscriptionSyncManagerProps {
  storeId?: number;
  isGlobalAdmin?: boolean;
}

export function SubscriptionSyncManager({ storeId, isGlobalAdmin = false }: SubscriptionSyncManagerProps) {
  const { t } = useTranslation();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [syncResult, setSyncResult] = useState<SyncResult | BulkSyncResult | null>(null);
  const [syncStats, setSyncStats] = useState<SyncStats | null>(null);

  // Carregar estatísticas de sincronização
  const loadSyncStats = async () => {
    if (!isGlobalAdmin) return;

    try {
      const response = await apiRequest('GET', '/api/subscriptions/sync-stats');
      setSyncStats(response);
    } catch (error) {
      console.error('Erro ao carregar estatísticas:', error);
    }
  };

  // Sincronização manual de uma assinatura específica
  const handleManualSync = async (subscriptionId: string, forceUpdate: boolean = false) => {
    setIsLoading(true);
    setSyncResult(null);

    try {
      const response = await apiRequest('POST', `/api/subscriptions/sync/${subscriptionId}`, {
        forceUpdate
      });

      setSyncResult(response.result);
      
      toast({
        title: t('common.success'),
        description: 'Sincronização concluída com sucesso',
      });

      // Recarregar estatísticas se for admin global
      if (isGlobalAdmin) {
        await loadSyncStats();
      }

    } catch (error: any) {
      console.error('Erro na sincronização:', error);
      
      toast({
        title: t('common.error'),
        description: error.message || 'Erro na sincronização',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Sincronização em lote para uma loja
  const handleBulkSync = async () => {
    if (!storeId) return;

    setIsLoading(true);
    setSyncResult(null);

    try {
      const response = await apiRequest('POST', `/api/subscriptions/bulk-sync/${storeId}`, {
        forceUpdate: false,
        maxConcurrent: 3
      });

      setSyncResult(response.result);
      
      toast({
        title: t('common.success'),
        description: `Sincronização em lote concluída: ${response.result.successful}/${response.result.totalProcessed} sucessos`,
      });

      // Recarregar estatísticas se for admin global
      if (isGlobalAdmin) {
        await loadSyncStats();
      }

    } catch (error: any) {
      console.error('Erro na sincronização em lote:', error);
      
      toast({
        title: t('common.error'),
        description: error.message || 'Erro na sincronização em lote',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Validar e corrigir dados de assinatura
  const handleValidateSubscription = async (subscriptionId: number) => {
    setIsLoading(true);

    try {
      const response = await apiRequest('POST', `/api/subscriptions/validate/${subscriptionId}`);
      
      toast({
        title: t('common.success'),
        description: 'Validação concluída',
      });

      // Recarregar estatísticas se for admin global
      if (isGlobalAdmin) {
        await loadSyncStats();
      }

    } catch (error: any) {
      console.error('Erro na validação:', error);
      
      toast({
        title: t('common.error'),
        description: error.message || 'Erro na validação',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Renderizar resultado de sincronização
  const renderSyncResult = () => {
    if (!syncResult) return null;

    const isBulkResult = 'totalProcessed' in syncResult;

    return (
      <Card className="mt-4">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {syncResult.success || (isBulkResult && syncResult.successful > 0) ? (
              <CheckCircle className="h-5 w-5 text-green-500" />
            ) : (
              <XCircle className="h-5 w-5 text-red-500" />
            )}
            Resultado da Sincronização
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isBulkResult ? (
            <div className="space-y-2">
              <div className="flex gap-4">
                <Badge variant="outline">Total: {syncResult.totalProcessed}</Badge>
                <Badge variant="default">Sucessos: {syncResult.successful}</Badge>
                <Badge variant="destructive">Falhas: {syncResult.failed}</Badge>
                <Badge variant="secondary">Duração: {syncResult.duration}ms</Badge>
              </div>
              
              {syncResult.results.length > 0 && (
                <div className="mt-4">
                  <h4 className="font-medium mb-2">Detalhes:</h4>
                  <div className="space-y-1 max-h-40 overflow-y-auto">
                    {syncResult.results.slice(0, 10).map((result, index) => (
                      <div key={index} className="text-sm flex items-center gap-2">
                        {result.success ? (
                          <CheckCircle className="h-3 w-3 text-green-500" />
                        ) : (
                          <XCircle className="h-3 w-3 text-red-500" />
                        )}
                        <span className="font-mono text-xs">
                          {result.subscriptionId || result.localId}
                        </span>
                        {result.error && (
                          <span className="text-red-600 text-xs">{result.error}</span>
                        )}
                      </div>
                    ))}
                    {syncResult.results.length > 10 && (
                      <div className="text-xs text-gray-500">
                        ... e mais {syncResult.results.length - 10} resultados
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="space-y-2">
              <div className="flex gap-2">
                <Badge variant={syncResult.success ? "default" : "destructive"}>
                  {syncResult.success ? "Sucesso" : "Falha"}
                </Badge>
                {syncResult.subscriptionId && (
                  <Badge variant="outline">{syncResult.subscriptionId}</Badge>
                )}
              </div>
              
              {syncResult.error && (
                <div className="text-red-600 text-sm">{syncResult.error}</div>
              )}
              
              {syncResult.changes && syncResult.changes.length > 0 && (
                <div>
                  <h4 className="font-medium text-sm">Mudanças:</h4>
                  <ul className="text-sm text-gray-600 list-disc list-inside">
                    {syncResult.changes.map((change, index) => (
                      <li key={index}>{change}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  // Renderizar estatísticas (apenas para admin global)
  const renderStats = () => {
    if (!isGlobalAdmin || !syncStats) return null;

    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Estatísticas de Sincronização
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold">{syncStats.totalSyncs}</div>
              <div className="text-sm text-gray-600">Total</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{syncStats.successfulSyncs}</div>
              <div className="text-sm text-gray-600">Sucessos</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{syncStats.failedSyncs}</div>
              <div className="text-sm text-gray-600">Falhas</div>
            </div>
            <div className="text-center">
              <div className="text-sm font-medium">
                {syncStats.lastSync ? new Date(syncStats.lastSync).toLocaleString() : 'Nunca'}
              </div>
              <div className="text-sm text-gray-600">Última Sync</div>
            </div>
          </div>
          
          {syncStats.recentErrors.length > 0 && (
            <div className="mt-4">
              <h4 className="font-medium text-sm mb-2 flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 text-yellow-500" />
                Erros Recentes
              </h4>
              <div className="space-y-1 max-h-32 overflow-y-auto">
                {syncStats.recentErrors.map((error, index) => (
                  <div key={index} className="text-xs text-red-600 bg-red-50 p-2 rounded">
                    {error}
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  React.useEffect(() => {
    if (isGlobalAdmin) {
      loadSyncStats();
    }
  }, [isGlobalAdmin]);

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <RefreshCw className="h-5 w-5" />
            Gerenciamento de Sincronização
          </CardTitle>
          <CardDescription>
            Ferramentas para sincronizar dados de assinatura entre Stripe e banco local
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {storeId && (
            <div>
              <h3 className="font-medium mb-2">Sincronização da Loja</h3>
              <Button
                onClick={handleBulkSync}
                disabled={isLoading}
                className="w-full sm:w-auto"
              >
                {isLoading ? (
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <RefreshCw className="h-4 w-4 mr-2" />
                )}
                Sincronizar Todas as Assinaturas
              </Button>
            </div>
          )}

          <Separator />

          <div>
            <h3 className="font-medium mb-2">Sincronização Manual</h3>
            <div className="flex gap-2">
              <input
                type="text"
                placeholder="ID da assinatura do Stripe (sub_...)"
                className="flex-1 px-3 py-2 border rounded-md"
                id="manual-sync-input"
              />
              <Button
                onClick={() => {
                  const input = document.getElementById('manual-sync-input') as HTMLInputElement;
                  if (input.value.trim()) {
                    handleManualSync(input.value.trim());
                  }
                }}
                disabled={isLoading}
              >
                {isLoading ? (
                  <RefreshCw className="h-4 w-4 animate-spin" />
                ) : (
                  <RefreshCw className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {renderStats()}
      {renderSyncResult()}
    </div>
  );
}
