import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useDashboardData } from "@/hooks/useDashboardData";
import { useFinancialData } from "@/hooks/useFinancialData";
import { MetricCard } from "./MetricCard";
import { FinancialSummary } from "./FinancialSummary";
import { RevenueChart } from "./RevenueChart";
import { TopProductsRevenue } from "./TopProductsRevenue";
import { PeriodFilter, PeriodValue } from "./PeriodFilter";
// import { DebugPanel } from "./DebugPanel"; // Removido para produção
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useLocation } from "wouter";
import {
  ShoppingCart,
  DollarSign,
  Clock,
  CheckCircle,
  TrendingUp,
  Users,
  Eye,
  BarChart3
} from "lucide-react";

export function DashboardMVP() {
  const { t } = useTranslation();
  const [, setLocation] = useLocation();
  const [selectedPeriod, setSelectedPeriod] = useState<PeriodValue>("thisMonth");
  const [debugLogs, setDebugLogs] = useState<string[]>([]);

  const { data: dashboardData, isLoading, error } = useDashboardData();
  const { data: financialData, isLoading: isLoadingFinancial, error: financialError } = useFinancialData(selectedPeriod);

  // Função para adicionar logs de debug
  const addDebugLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    const logMessage = `[${timestamp}] ${message}`;
    console.log(logMessage);
    setDebugLogs(prev => [...prev.slice(-9), logMessage]); // Manter apenas os últimos 10 logs
  };

  // Logs de debug para monitorar o estado
  useEffect(() => {
    addDebugLog(`Dashboard - isLoading: ${isLoading}, error: ${error?.message || 'none'}`);
    if (dashboardData) {
      addDebugLog(`Dashboard data loaded: ${JSON.stringify(Object.keys(dashboardData))}`);
    }
  }, [isLoading, error, dashboardData]);

  useEffect(() => {
    addDebugLog(`Financial - isLoading: ${isLoadingFinancial}, error: ${financialError?.message || 'none'}, period: ${selectedPeriod}`);
    if (financialData) {
      addDebugLog(`Financial data loaded: ${JSON.stringify(Object.keys(financialData))}`);
    }
  }, [isLoadingFinancial, financialError, financialData, selectedPeriod]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
      </div>
    );
  }

  if (error || !dashboardData) {
    return (
      <div className="p-4 bg-red-50 text-red-600 rounded-md">
        <h2 className="text-lg font-semibold">Erro</h2>
        <p>Não foi possível carregar os dados do dashboard</p>
      </div>
    );
  }

  const { summary, pendingOrders, siteActivity, topProducts, recentCustomers } = dashboardData;

  // Use financial data from the new hook, fallback to dashboard data if needed
  const financialMetrics = financialData || dashboardData.financialMetrics;

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100/50">
      <div className="space-y-6 p-4 sm:p-6 max-w-7xl mx-auto">

        {/* Debug Panel - Visível apenas quando há problemas */}
        {(error || financialError || debugLogs.length > 0) && (
          <Card className="p-4 bg-yellow-50 border-yellow-200">
            <h3 className="text-lg font-semibold text-yellow-800 mb-4">🔍 Debug Dashboard</h3>

            {/* Status das APIs */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div className="bg-white p-3 rounded border">
                <h4 className="font-medium text-gray-700 mb-2">Dashboard API Status</h4>
                <p className="text-sm">
                  <span className={`inline-block w-2 h-2 rounded-full mr-2 ${isLoading ? 'bg-yellow-500' : error ? 'bg-red-500' : 'bg-green-500'}`}></span>
                  {isLoading ? 'Carregando...' : error ? `Erro: ${error.message}` : 'OK'}
                </p>
                {dashboardData && (
                  <p className="text-xs text-gray-500 mt-1">
                    Dados: {Object.keys(dashboardData).join(', ')}
                  </p>
                )}
              </div>

              <div className="bg-white p-3 rounded border">
                <h4 className="font-medium text-gray-700 mb-2">Financial API Status</h4>
                <p className="text-sm">
                  <span className={`inline-block w-2 h-2 rounded-full mr-2 ${isLoadingFinancial ? 'bg-yellow-500' : financialError ? 'bg-red-500' : 'bg-green-500'}`}></span>
                  {isLoadingFinancial ? 'Carregando...' : financialError ? `Erro: ${financialError.message}` : 'OK'}
                </p>
                {financialData && (
                  <p className="text-xs text-gray-500 mt-1">
                    Dados: {Object.keys(financialData).join(', ')}
                  </p>
                )}
              </div>
            </div>

            {/* Logs de Debug */}
            {debugLogs.length > 0 && (
              <div className="bg-white p-3 rounded border">
                <h4 className="font-medium text-gray-700 mb-2">Logs de Debug</h4>
                <div className="text-xs text-gray-600 space-y-1 max-h-32 overflow-y-auto">
                  {debugLogs.map((log, index) => (
                    <div key={index} className="font-mono">{log}</div>
                  ))}
                </div>
              </div>
            )}

            {/* Informações de Ambiente */}
            <div className="bg-white p-3 rounded border mt-4">
              <h4 className="font-medium text-gray-700 mb-2">Configuração de Ambiente</h4>
              <div className="text-xs text-gray-600 space-y-1">
                <p><strong>VITE_API_URL:</strong> {import.meta.env.VITE_API_URL || 'não definida (usando URLs relativas)'}</p>
                <p><strong>Environment:</strong> {import.meta.env.VITE_APP_ENVIRONMENT || 'development'}</p>
                <p><strong>Mode:</strong> {import.meta.env.MODE}</p>
              </div>
            </div>

            {/* Botões de Teste */}
            <div className="bg-white p-3 rounded border mt-4">
              <h4 className="font-medium text-gray-700 mb-2">Testes Manuais</h4>
              <div className="flex gap-2 flex-wrap">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={async () => {
                    try {
                      addDebugLog('Testando /api/dashboard...');
                      const { apiRequest } = await import('@/lib/queryClient');
                      const response = await apiRequest('GET', '/api/dashboard');
                      addDebugLog(`Dashboard test success: ${JSON.stringify(Object.keys(response))}`);
                    } catch (error) {
                      addDebugLog(`Dashboard test failed: ${error.message}`);
                    }
                  }}
                >
                  Testar Dashboard API
                </Button>

                <Button
                  size="sm"
                  variant="outline"
                  onClick={async () => {
                    try {
                      addDebugLog('Testando /api/dashboard/financial...');
                      const { apiRequest } = await import('@/lib/queryClient');
                      const response = await apiRequest('GET', `/api/dashboard/financial?period=${selectedPeriod}`);
                      addDebugLog(`Financial test success: ${JSON.stringify(Object.keys(response))}`);
                    } catch (error) {
                      addDebugLog(`Financial test failed: ${error.message}`);
                    }
                  }}
                >
                  Testar Financial API
                </Button>

                <Button
                  size="sm"
                  variant="outline"
                  onClick={async () => {
                    try {
                      addDebugLog('Testando buildApiUrl...');
                      const { buildApiUrl } = await import('@/lib/queryClient');
                      const testUrl = buildApiUrl('/api/test');
                      addDebugLog(`buildApiUrl test: /api/test → ${testUrl}`);
                    } catch (error) {
                      addDebugLog(`buildApiUrl test failed: ${error.message}`);
                    }
                  }}
                >
                  Testar URL Builder
                </Button>
              </div>
            </div>
          </Card>
        )}
        {/* Resumo Rápido - 3 Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          <MetricCard
            title="Pedidos Pendentes"
            value={summary.pendingOrders}
            icon={<ShoppingCart />}
            color="blue"
            clickable={true}
            onClick={() => setLocation('/admin/orders?status=pending')}
          />

          <MetricCard
            title="Pedidos Confirmados"
            value={summary.confirmedOrders}
            icon={<CheckCircle />}
            color="green"
            clickable={true}
            onClick={() => setLocation('/admin/orders?status=confirmed')}
          />

          <MetricCard
            title="Próxima Entrega"
            value={summary.nextDelivery?.customerName || "Nenhuma"}
            subtitle={summary.nextDelivery?.deliveryTime}
            icon={<Clock />}
            color="orange"
            clickable={!!summary.nextDelivery}
            onClick={summary.nextDelivery ? () => setLocation(`/admin/orders/${summary.nextDelivery!.id}`) : undefined}
          />
        </div>



      {/* Métricas Financeiras */}
      {financialMetrics && (
        <div className="space-y-6">
          {/* Header with Period Filter */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <h2 className="text-xl font-semibold text-gray-900">{t('dashboard.financialAnalysis')}</h2>
            <PeriodFilter
              selectedPeriod={selectedPeriod}
              onPeriodChange={setSelectedPeriod}
              className="sm:w-auto"
            />
          </div>

          {/* Debug Panel removido para produção */}

          {/* Loading state for financial data */}
          {isLoadingFinancial ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {Array.from({ length: 4 }).map((_, i) => (
                <Card key={i} className="p-5 animate-pulse">
                  <div className="space-y-3">
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                    <div className="h-8 bg-gray-100 rounded"></div>
                  </div>
                </Card>
              ))}
            </div>
          ) : (
            <>
              {/* Resumo Financeiro */}
              <FinancialSummary
                summary={financialMetrics.summary}
                monthlyComparison={financialMetrics.monthlyComparison}
                sparklineData={financialData?.sparklineData}
              />

              {/* Gráfico de Receita e Top Produtos */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <RevenueChart data={financialMetrics.revenueChart} />
                <TopProductsRevenue products={financialMetrics.topProductsByRevenue} />
              </div>
            </>
          )}
        </div>
      )}

      {/* Atividade do Site - Grid 2x2 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <MetricCard
          title="Visitas no Mês"
          value={siteActivity.monthlyVisits}
          icon={<Eye />}
          color="blue"
        />

        <MetricCard
          title="Visitas Hoje"
          value={siteActivity.todayVisits}
          icon={<TrendingUp />}
          color="green"
        />

        <MetricCard
          title="Taxa de Conversão"
          value={`${siteActivity.conversionRate}%`}
          icon={<BarChart3 />}
          color="orange"
        />

        <MetricCard
          title="Clientes Recorrentes"
          value={siteActivity.returningCustomers}
          icon={<Users />}
          color="blue"
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top 10 Produtos */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Top 10 Produtos Mais Vendidos</h3>
          <p className="text-sm text-gray-600 mb-4">Baseado em pedidos confirmados e entregues</p>
          {topProducts.length === 0 ? (
            <p className="text-gray-500">Nenhum produto vendido ainda</p>
          ) : (
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {topProducts.map((product, index) => (
                <div key={product.name} className="flex items-center gap-3">
                  <div className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-bold ${
                    index === 0 ? 'bg-yellow-500 text-white' :
                    index === 1 ? 'bg-gray-400 text-white' :
                    index === 2 ? 'bg-orange-500 text-white' :
                    'bg-blue-500 text-white'
                  }`}>
                    {index + 1}
                  </div>
                  <div>
                    <p className="font-medium">{product.name}</p>
                    <p className="text-sm text-gray-600">{product.totalSold} vendidos</p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </Card>

        {/* Clientes Recentes */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Clientes Recentes</h3>
          {recentCustomers.length === 0 ? (
            <p className="text-gray-500">Nenhum cliente encontrado</p>
          ) : (
            <div className="space-y-3">
              {recentCustomers.map((customer) => (
                <div key={customer.id} className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">{customer.name}</p>
                    <p className="text-sm text-gray-600">
                      {customer.totalOrders} pedidos • Último: {customer.lastOrderDate}
                    </p>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setLocation(`/admin/customers/${customer.id}`)}
                    >
                      Ver
                    </Button>
                    {customer.phone && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.open(`https://wa.me/${customer.phone.replace(/\D/g, '')}`, '_blank')}
                      >
                        WhatsApp
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </Card>
      </div>
      </div>
    </div>
  );
}