import { useState } from 'react';
import { <PERSON> } from 'wouter';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import { Separator } from '@/components/ui/separator';
import { useAuth } from '@/context/FirebaseAuthContext';
import { useTranslation } from '@/hooks/useTranslation';
import { FcGoogle } from 'react-icons/fc';
import { Mail, Eye, EyeOff, Loader2, Sparkles } from 'lucide-react';
import { cn } from '@/lib/utils';

interface AuthFormProps {
  mode: 'login' | 'register';
}

// Formulários de validação com i18n
const createLoginSchema = (t: any) => z.object({
  email: z.string().email(t('common.invalidEmail')),
  password: z.string().min(6, t('auth.passwordMinLength')),
});

const createRegisterSchema = (t: any) => z.object({
  username: z.string().min(3, t('auth.usernameMinLength')),
  email: z.string().email(t('common.invalidEmail')),
  password: z.string().min(6, t('auth.passwordMinLength')),
  confirmPassword: z.string().min(6, t('auth.passwordMinLength')),
}).refine((data) => data.password === data.confirmPassword, {
  message: t('auth.passwordsDoNotMatch'),
  path: ['confirmPassword'],
});

type LoginFormValues = z.infer<typeof loginSchema>;
type RegisterFormValues = z.infer<typeof registerSchema>;

export function AuthForm({ mode }: AuthFormProps) {
  const { t } = useTranslation();
  const { loading, signInWithGoogle, signInWithEmail, signUpWithEmail } = useAuth();
  const [isProcessing, setIsProcessing] = useState(false);
  const [authMethod, setAuthMethod] = useState<'google' | 'email'>('google');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // Schemas com tradução
  const loginSchema = createLoginSchema(t);
  const registerSchema = createRegisterSchema(t);

  // Formulário de login
  const loginForm = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  // Formulário de registro
  const registerForm = useForm<RegisterFormValues>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      username: '',
      email: '',
      password: '',
      confirmPassword: '',
    },
  });

  // Autenticação com Google
  const handleGoogleAuth = async () => {
    try {
      setIsProcessing(true);
      await signInWithGoogle();
    } catch (error) {
      console.error("Erro ao autenticar com Google:", error);
    } finally {
      setIsProcessing(false);
    }
  };

  // Autenticação com Email e Senha - Login
  const onLoginSubmit = async (data: LoginFormValues) => {
    try {
      setIsProcessing(true);
      await signInWithEmail(data.email, data.password);
    } catch (error) {
      console.error("Erro ao fazer login com email:", error);
    } finally {
      setIsProcessing(false);
    }
  };

  // Autenticação com Email e Senha - Registro
  const onRegisterSubmit = async (data: RegisterFormValues) => {
    try {
      setIsProcessing(true);
      await signUpWithEmail(data.email, data.password, data.username);
    } catch (error) {
      console.error("Erro ao registrar com email:", error);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="w-full max-w-md mx-auto">
      {/* Header com logo e gradiente */}
      <div className="text-center mb-8">
        <div className="inline-flex items-center justify-center w-20 h-20 rounded-3xl bg-gradient-to-br from-pink-500 via-pink-400 to-yellow-400 mb-6 shadow-lg">
          <Sparkles className="w-10 h-10 text-white" />
        </div>
        <h1 className="text-3xl font-bold bg-gradient-to-r from-pink-600 to-yellow-500 bg-clip-text text-transparent mb-2">
          {t('auth.title')}
        </h1>
        <p className="text-gray-600 text-lg">
          {mode === 'login' ? t('auth.loginTitle') : t('auth.registerTitle')}
        </p>
      </div>

      {/* Card principal com design iOS */}
      <Card className="border-0 shadow-xl bg-white/95 backdrop-blur-sm rounded-3xl overflow-hidden">
        <CardContent className="p-8 space-y-6">
          {/* Método de autenticação - Google primeiro */}
          <div className="space-y-4">
            <Button
              className={cn(
                "w-full h-14 rounded-2xl font-semibold text-base transition-all duration-200",
                "bg-white border-2 border-gray-200 text-gray-700 hover:border-gray-300 hover:shadow-md",
                "active:scale-[0.98] disabled:opacity-50 disabled:cursor-not-allowed",
                "flex items-center justify-center gap-3"
              )}
              onClick={handleGoogleAuth}
              disabled={loading || isProcessing}
            >
              {loading || isProcessing ? (
                <Loader2 className="w-5 h-5 animate-spin" />
              ) : (
                <FcGoogle className="w-6 h-6" />
              )}
              <span>
                {loading || isProcessing
                  ? t('common.loading')
                  : mode === 'login'
                    ? t('auth.loginWithGoogle')
                    : t('auth.registerWithGoogle')
                }
              </span>
            </Button>

            {/* Separador */}
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <Separator className="w-full" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="bg-white px-4 text-gray-500 font-medium">
                  {t('auth.orContinueWith')}
                </span>
              </div>
            </div>
          </div>
          {/* Formulário de email */}
          <div className="space-y-5">
            {mode === 'login' ? (
              <Form {...loginForm}>
                <form onSubmit={loginForm.handleSubmit(onLoginSubmit)} className="space-y-5">
                  <FormField
                    control={loginForm.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-gray-700 font-semibold text-sm">
                          {t('common.email')}
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="email"
                            placeholder={t('auth.emailPlaceholder')}
                            className={cn(
                              "h-12 rounded-xl border-2 border-gray-200 bg-gray-50/50",
                              "focus:border-pink-400 focus:bg-white transition-all duration-200",
                              "text-base placeholder:text-gray-400"
                            )}
                            {...field}
                            autoComplete="email"
                          />
                        </FormControl>
                        <FormMessage className="text-red-500 text-sm" />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={loginForm.control}
                    name="password"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-gray-700 font-semibold text-sm">
                          {t('common.password')}
                        </FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Input
                              type={showPassword ? "text" : "password"}
                              placeholder={t('auth.passwordPlaceholder')}
                              className={cn(
                                "h-12 rounded-xl border-2 border-gray-200 bg-gray-50/50 pr-12",
                                "focus:border-pink-400 focus:bg-white transition-all duration-200",
                                "text-base placeholder:text-gray-400"
                              )}
                              {...field}
                              autoComplete="current-password"
                            />
                            <Button
                              type="button"
                              variant="ghost"
                              size="icon"
                              className="absolute right-0 top-0 h-12 w-12 rounded-xl hover:bg-gray-100"
                              onClick={() => setShowPassword(!showPassword)}
                            >
                              {showPassword ? <EyeOff className="w-5 h-5 text-gray-500" /> : <Eye className="w-5 h-5 text-gray-500" />}
                            </Button>
                          </div>
                        </FormControl>
                        <FormMessage className="text-red-500 text-sm" />
                      </FormItem>
                    )}
                  />

                  <Button
                    type="submit"
                    className={cn(
                      "w-full h-14 rounded-2xl font-semibold text-base transition-all duration-200",
                      "bg-gradient-to-r from-pink-500 to-yellow-500 hover:from-pink-600 hover:to-yellow-600",
                      "text-white shadow-lg hover:shadow-xl active:scale-[0.98]",
                      "disabled:opacity-50 disabled:cursor-not-allowed"
                    )}
                    disabled={loading || isProcessing}
                  >
                    {loading || isProcessing ? (
                      <div className="flex items-center gap-2">
                        <Loader2 className="w-5 h-5 animate-spin" />
                        {t('auth.loggingIn')}
                      </div>
                    ) : (
                      t('common.login')
                    )}
                  </Button>
                </form>
              </Form>
            ) : (
              <Form {...registerForm}>
                <form onSubmit={registerForm.handleSubmit(onRegisterSubmit)} className="space-y-5">
                  <FormField
                    control={registerForm.control}
                    name="username"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-gray-700 font-semibold text-sm">
                          {t('auth.username')}
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder={t('auth.usernamePlaceholder')}
                            className={cn(
                              "h-12 rounded-xl border-2 border-gray-200 bg-gray-50/50",
                              "focus:border-pink-400 focus:bg-white transition-all duration-200",
                              "text-base placeholder:text-gray-400"
                            )}
                            {...field}
                            autoComplete="username"
                          />
                        </FormControl>
                        <FormMessage className="text-red-500 text-sm" />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={registerForm.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-gray-700 font-semibold text-sm">
                          {t('common.email')}
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="email"
                            placeholder={t('auth.emailPlaceholder')}
                            className={cn(
                              "h-12 rounded-xl border-2 border-gray-200 bg-gray-50/50",
                              "focus:border-pink-400 focus:bg-white transition-all duration-200",
                              "text-base placeholder:text-gray-400"
                            )}
                            {...field}
                            autoComplete="email"
                          />
                        </FormControl>
                        <FormMessage className="text-red-500 text-sm" />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={registerForm.control}
                    name="password"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-gray-700 font-semibold text-sm">
                          {t('common.password')}
                        </FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Input
                              type={showPassword ? "text" : "password"}
                              placeholder={t('auth.passwordPlaceholder')}
                              className={cn(
                                "h-12 rounded-xl border-2 border-gray-200 bg-gray-50/50 pr-12",
                                "focus:border-pink-400 focus:bg-white transition-all duration-200",
                                "text-base placeholder:text-gray-400"
                              )}
                              {...field}
                              autoComplete="new-password"
                            />
                            <Button
                              type="button"
                              variant="ghost"
                              size="icon"
                              className="absolute right-0 top-0 h-12 w-12 rounded-xl hover:bg-gray-100"
                              onClick={() => setShowPassword(!showPassword)}
                            >
                              {showPassword ? <EyeOff className="w-5 h-5 text-gray-500" /> : <Eye className="w-5 h-5 text-gray-500" />}
                            </Button>
                          </div>
                        </FormControl>
                        <FormMessage className="text-red-500 text-sm" />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={registerForm.control}
                    name="confirmPassword"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-gray-700 font-semibold text-sm">
                          {t('auth.confirmPassword')}
                        </FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Input
                              type={showConfirmPassword ? "text" : "password"}
                              placeholder={t('auth.confirmPasswordPlaceholder')}
                              className={cn(
                                "h-12 rounded-xl border-2 border-gray-200 bg-gray-50/50 pr-12",
                                "focus:border-pink-400 focus:bg-white transition-all duration-200",
                                "text-base placeholder:text-gray-400"
                              )}
                              {...field}
                              autoComplete="new-password"
                            />
                            <Button
                              type="button"
                              variant="ghost"
                              size="icon"
                              className="absolute right-0 top-0 h-12 w-12 rounded-xl hover:bg-gray-100"
                              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                            >
                              {showConfirmPassword ? <EyeOff className="w-5 h-5 text-gray-500" /> : <Eye className="w-5 h-5 text-gray-500" />}
                            </Button>
                          </div>
                        </FormControl>
                        <FormMessage className="text-red-500 text-sm" />
                      </FormItem>
                    )}
                  />

                  <Button
                    type="submit"
                    className={cn(
                      "w-full h-14 rounded-2xl font-semibold text-base transition-all duration-200",
                      "bg-gradient-to-r from-pink-500 to-yellow-500 hover:from-pink-600 hover:to-yellow-600",
                      "text-white shadow-lg hover:shadow-xl active:scale-[0.98]",
                      "disabled:opacity-50 disabled:cursor-not-allowed"
                    )}
                    disabled={loading || isProcessing}
                  >
                    {loading || isProcessing ? (
                      <div className="flex items-center gap-2">
                        <Loader2 className="w-5 h-5 animate-spin" />
                        {t('auth.creatingAccount')}
                      </div>
                    ) : (
                      t('common.register')
                    )}
                  </Button>
                </form>
              </Form>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Footer com link para alternar entre login/registro */}
      <div className="text-center mt-6">
        <p className="text-gray-600">
          {mode === 'login' ? (
            <>
              {t('common.noAccount')}{' '}
              <Link
                href="/register"
                className="font-semibold text-pink-600 hover:text-pink-700 transition-colors duration-200"
              >
                {t('common.register')}
              </Link>
            </>
          ) : (
            <>
              {t('common.hasAccount')}{' '}
              <Link
                href="/login"
                className="font-semibold text-pink-600 hover:text-pink-700 transition-colors duration-200"
              >
                {t('common.login')}
              </Link>
            </>
          )}
        </p>
      </div>
    </div>
  );
}

export default AuthForm;
