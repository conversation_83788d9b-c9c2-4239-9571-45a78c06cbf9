import React from 'react';
import { useTranslation } from 'react-i18next';

interface EnvironmentBadgeProps {
  className?: string;
}

export default function EnvironmentBadge({ className = '' }: EnvironmentBadgeProps) {
  const { t } = useTranslation();
  
  // Obter ambiente da variável de ambiente
  const environment = import.meta.env.VITE_APP_ENVIRONMENT || 'development';
  
  // Configurações por ambiente
  const getEnvironmentConfig = () => {
    switch (environment) {
      case 'production':
        return {
          label: t('environment.production') || 'PRODUÇÃO',
          bgColor: 'bg-green-500',
          textColor: 'text-white',
          borderColor: 'border-green-600',
          show: false, // Ocultar em produção por padrão
        };
      case 'development':
      default:
        return {
          label: t('environment.development') || 'DESENVOLVIMENTO',
          bgColor: 'bg-orange-500',
          textColor: 'text-white',
          borderColor: 'border-orange-600',
          show: true, // Sempre mostrar em desenvolvimento
        };
    }
  };

  const config = getEnvironmentConfig();

  // Se configurado para não mostrar, retornar null
  if (!config.show) {
    return null;
  }

  return (
    <div
      className={`
        fixed top-0 left-0 right-0 z-50
        ${config.bgColor} ${config.textColor} ${config.borderColor}
        border-b-2 shadow-sm
        ${className}
      `}
    >
      <div className="max-w-7xl mx-auto px-4 py-1">
        <div className="flex items-center justify-center">
          <div className="flex items-center space-x-2">
            {/* Ícone de ambiente */}
            <div className={`
              w-2 h-2 rounded-full 
              ${environment === 'production' ? 'bg-green-200' : 'bg-orange-200'}
              animate-pulse
            `} />
            
            {/* Label do ambiente */}
            <span className="text-xs font-semibold uppercase tracking-wider">
              {config.label}
            </span>
            
            {/* Informação adicional em desenvolvimento */}
            {environment === 'development' && (
              <span className="text-xs opacity-75 hidden sm:inline">
                • {t('environment.localDevelopment') || 'Desenvolvimento Local'}
              </span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

// Hook para obter informações do ambiente
export function useEnvironment() {
  const environment = import.meta.env.VITE_APP_ENVIRONMENT || 'development';
  
  return {
    environment,
    isDevelopment: environment === 'development',
    isProduction: environment === 'production',
    isStaging: environment === 'staging',
  };
}

// Componente wrapper que adiciona padding-top quando o badge está visível
interface EnvironmentAwareLayoutProps {
  children: React.ReactNode;
  className?: string;
}

export function EnvironmentAwareLayout({ children, className = '' }: EnvironmentAwareLayoutProps) {
  const { isDevelopment } = useEnvironment();
  
  return (
    <div className={`${isDevelopment ? 'pt-8' : ''} ${className}`}>
      <EnvironmentBadge />
      {children}
    </div>
  );
}
