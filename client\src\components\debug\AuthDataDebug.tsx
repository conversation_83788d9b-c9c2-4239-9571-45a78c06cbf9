import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/context/FirebaseAuthContext';
import { useStore } from '@/context/StoreContext';
import { useSubscription } from '@/context/SubscriptionContext';
import { useDataRefresh } from '@/hooks/useDataRefresh';
import { useUserChangeDetection } from '@/hooks/useUserChangeDetection';
import { RefreshCw, User, Store, CreditCard, AlertCircle, Eye, Clock } from 'lucide-react';

/**
 * Componente de debug para verificar sincronização de dados após login
 *
 * ⚠️ APENAS PARA DEBUG - NÃO USAR EM PRODUÇÃO
 *
 * Este componente foi criado temporariamente para testar a correção
 * do problema de sincronização de dados após login. Deve ser removido
 * ou mantido apenas para troubleshooting futuro.
 */
export function AuthDataDebug() {
  const { user, isAuthenticated } = useAuth();
  const { store, isLoading: storeLoading } = useStore();
  const { subscription, usageInfo, isLoading: subscriptionLoading } = useSubscription();
  const { refreshAllUserData } = useDataRefresh();
  const { currentUserId, previousUserId, hasUserChanged } = useUserChangeDetection();
  const [lastRefresh, setLastRefresh] = useState<Date | null>(null);
  const [refreshCount, setRefreshCount] = useState(0);

  const handleForceRefresh = async () => {
    console.log('🔄 Forçando refresh manual dos dados...');
    await refreshAllUserData();
    setLastRefresh(new Date());
    setRefreshCount(prev => prev + 1);
  };

  // Auto-refresh quando o usuário muda
  useEffect(() => {
    if (isAuthenticated && user) {
      console.log('👤 Usuário autenticado detectado, dados devem ser atualizados automaticamente');
    }
  }, [isAuthenticated, user]);

  if (!isAuthenticated) {
    return (
      <Card className="border-yellow-200 bg-yellow-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-yellow-800">
            <AlertCircle className="w-5 h-5" />
            Debug de Autenticação
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-yellow-700">Usuário não autenticado. Faça login para ver os dados.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center gap-2">
              <User className="w-5 h-5" />
              Debug de Dados do Usuário
            </span>
            <Button 
              onClick={handleForceRefresh}
              variant="outline"
              size="sm"
              className="flex items-center gap-2"
            >
              <RefreshCw className="w-4 h-4" />
              Forçar Refresh
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Informações de Debug */}
          <div className="bg-blue-50 p-3 rounded-lg text-sm space-y-1">
            <div><strong>Debug Info:</strong></div>
            <div>Refresh manual: {refreshCount}x</div>
            {lastRefresh && <div>Último: {lastRefresh.toLocaleTimeString()}</div>}
            <div>Usuário mudou: {hasUserChanged ? '✅ Sim' : '❌ Não'}</div>
            <div>Usuário atual: {currentUserId || 'Nenhum'}</div>
            <div>Usuário anterior: {previousUserId || 'Nenhum'}</div>
          </div>

          {/* Dados do Usuário */}
          <div className="space-y-2">
            <h4 className="font-semibold flex items-center gap-2">
              <User className="w-4 h-4" />
              Usuário Firebase
            </h4>
            <div className="bg-gray-50 p-3 rounded-lg text-sm">
              <div><strong>UID:</strong> {user?.id}</div>
              <div><strong>Email:</strong> {user?.email}</div>
              <div><strong>Nome:</strong> {user?.displayName || 'N/A'}</div>
              <Badge variant={isAuthenticated ? "default" : "destructive"}>
                {isAuthenticated ? "Autenticado" : "Não autenticado"}
              </Badge>
            </div>
          </div>

          {/* Dados da Loja */}
          <div className="space-y-2">
            <h4 className="font-semibold flex items-center gap-2">
              <Store className="w-4 h-4" />
              Dados da Loja
              {storeLoading && <RefreshCw className="w-4 h-4 animate-spin" />}
            </h4>
            <div className="bg-gray-50 p-3 rounded-lg text-sm">
              {store ? (
                <>
                  <div><strong>ID:</strong> {store.id}</div>
                  <div><strong>Nome:</strong> {store.name}</div>
                  <div><strong>Slug:</strong> {store.slug}</div>
                  <div><strong>Firebase UID:</strong> {store.firebaseUid}</div>
                  <Badge variant="default">Loja carregada</Badge>
                </>
              ) : (
                <>
                  <div>Nenhuma loja encontrada</div>
                  <Badge variant="secondary">Sem loja</Badge>
                </>
              )}
            </div>
          </div>

          {/* Dados da Assinatura */}
          <div className="space-y-2">
            <h4 className="font-semibold flex items-center gap-2">
              <CreditCard className="w-4 h-4" />
              Dados da Assinatura
              {subscriptionLoading && <RefreshCw className="w-4 h-4 animate-spin" />}
            </h4>
            <div className="bg-gray-50 p-3 rounded-lg text-sm">
              {subscription ? (
                <>
                  <div><strong>Plano:</strong> {subscription.planType}</div>
                  <div><strong>Status:</strong> {subscription.status}</div>
                  <div><strong>Trial End:</strong> {subscription.trialEnd ? new Date(subscription.trialEnd).toLocaleDateString() : 'N/A'}</div>
                  <Badge variant={subscription.status === 'active' ? "default" : "destructive"}>
                    {subscription.status}
                  </Badge>
                </>
              ) : (
                <>
                  <div>Nenhuma assinatura encontrada</div>
                  <Badge variant="secondary">Sem assinatura</Badge>
                </>
              )}
            </div>
          </div>

          {/* Dados de Uso */}
          {usageInfo && (
            <div className="space-y-2">
              <h4 className="font-semibold">Informações de Uso</h4>
              <div className="bg-gray-50 p-3 rounded-lg text-sm">
                <div><strong>Produtos:</strong> {usageInfo.usage.products.current}/{usageInfo.usage.products.limit}</div>
                <div><strong>Pedidos:</strong> {usageInfo.usage.orders.current}/{usageInfo.usage.orders.limit}</div>
              </div>
            </div>
          )}

          {/* Status de Carregamento */}
          <div className="space-y-2">
            <h4 className="font-semibold">Status de Carregamento</h4>
            <div className="flex gap-2">
              <Badge variant={storeLoading ? "destructive" : "default"}>
                Loja: {storeLoading ? "Carregando..." : "Carregada"}
              </Badge>
              <Badge variant={subscriptionLoading ? "destructive" : "default"}>
                Assinatura: {subscriptionLoading ? "Carregando..." : "Carregada"}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
