import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Clock, Crown, CreditCard, AlertTriangle, CheckCircle, XCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

interface TrialStatusBadgeProps {
  subscription?: {
    id: number;
    planType: 'free' | 'premium';
    status: string;
    currentPeriodEnd?: string;
    trialEnd?: string;
    isTrialActive?: boolean;
    daysRemaining?: number;
  };
  showDetails?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

export function TrialStatusBadge({ 
  subscription, 
  showDetails = false, 
  size = 'md' 
}: TrialStatusBadgeProps) {
  if (!subscription) {
    return (
      <Badge variant="secondary" className={cn(
        "flex items-center gap-1",
        size === 'sm' && "text-xs px-2 py-0.5",
        size === 'lg' && "text-sm px-3 py-1"
      )}>
        <XCircle className="h-3 w-3" />
        Sem assinatura
      </Badge>
    );
  }

  const now = new Date();
  const isTrialActive = subscription.trialEnd && new Date(subscription.trialEnd) > now;
  const isTrialExpired = subscription.trialEnd && new Date(subscription.trialEnd) <= now;
  const daysRemaining = subscription.trialEnd 
    ? Math.ceil((new Date(subscription.trialEnd).getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
    : 0;

  // Determine status and styling
  let badgeContent: React.ReactNode;
  let badgeVariant: "default" | "secondary" | "destructive" | "outline";
  let badgeClassName = "";

  if (subscription.planType === 'free') {
    badgeContent = (
      <>
        <CreditCard className="h-3 w-3" />
        {showDetails ? 'Plano Gratuito' : 'Gratuito'}
      </>
    );
    badgeVariant = "secondary";
    badgeClassName = "text-gray-600 bg-gray-100";
  } else if (subscription.planType === 'premium') {
    if (subscription.status === 'active') {
      if (isTrialActive) {
        // Active trial
        if (daysRemaining <= 2) {
          badgeContent = (
            <>
              <AlertTriangle className="h-3 w-3" />
              {showDetails ? `Trial expira em ${daysRemaining} dia${daysRemaining !== 1 ? 's' : ''}` : `${daysRemaining}d trial`}
            </>
          );
          badgeVariant = "destructive";
        } else if (daysRemaining <= 5) {
          badgeContent = (
            <>
              <Clock className="h-3 w-3" />
              {showDetails ? `Trial: ${daysRemaining} dias restantes` : `${daysRemaining}d trial`}
            </>
          );
          badgeVariant = "secondary";
          badgeClassName = "text-orange-600 bg-orange-100";
        } else {
          badgeContent = (
            <>
              <Clock className="h-3 w-3" />
              {showDetails ? `Trial: ${daysRemaining} dias restantes` : `${daysRemaining}d trial`}
            </>
          );
          badgeVariant = "default";
          badgeClassName = "text-blue-600 bg-blue-100";
        }
      } else {
        // Active paid subscription
        badgeContent = (
          <>
            <Crown className="h-3 w-3" />
            {showDetails ? 'Premium Ativo' : 'Premium'}
          </>
        );
        badgeVariant = "default";
        badgeClassName = "text-green-600 bg-green-100";
      }
    } else if (subscription.status === 'canceled') {
      if (isTrialExpired) {
        badgeContent = (
          <>
            <XCircle className="h-3 w-3" />
            {showDetails ? 'Trial Expirado' : 'Trial Expirado'}
          </>
        );
        badgeVariant = "destructive";
        badgeClassName = "text-orange-600 bg-orange-100";
      } else {
        badgeContent = (
          <>
            <XCircle className="h-3 w-3" />
            {showDetails ? 'Cancelado' : 'Cancelado'}
          </>
        );
        badgeVariant = "destructive";
      }
    } else if (subscription.status === 'past_due') {
      badgeContent = (
        <>
          <AlertTriangle className="h-3 w-3" />
          {showDetails ? 'Pagamento em Atraso' : 'Em Atraso'}
        </>
      );
      badgeVariant = "destructive";
      badgeClassName = "text-yellow-600 bg-yellow-100";
    } else {
      badgeContent = (
        <>
          <AlertTriangle className="h-3 w-3" />
          {showDetails ? `Status: ${subscription.status}` : subscription.status}
        </>
      );
      badgeVariant = "outline";
    }
  } else {
    badgeContent = (
      <>
        <AlertTriangle className="h-3 w-3" />
        {showDetails ? 'Status Desconhecido' : 'Desconhecido'}
      </>
    );
    badgeVariant = "outline";
  }

  return (
    <Badge 
      variant={badgeVariant} 
      className={cn(
        "flex items-center gap-1",
        size === 'sm' && "text-xs px-2 py-0.5",
        size === 'lg' && "text-sm px-3 py-1",
        badgeClassName
      )}
    >
      {badgeContent}
    </Badge>
  );
}

interface TrialCountdownProps {
  trialEnd: string;
  className?: string;
}

export function TrialCountdown({ trialEnd, className }: TrialCountdownProps) {
  const [timeRemaining, setTimeRemaining] = React.useState<{
    days: number;
    hours: number;
    minutes: number;
    seconds: number;
  } | null>(null);

  React.useEffect(() => {
    const updateCountdown = () => {
      const now = new Date().getTime();
      const trialEndTime = new Date(trialEnd).getTime();
      const difference = trialEndTime - now;

      if (difference > 0) {
        const days = Math.floor(difference / (1000 * 60 * 60 * 24));
        const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((difference % (1000 * 60)) / 1000);

        setTimeRemaining({ days, hours, minutes, seconds });
      } else {
        setTimeRemaining(null);
      }
    };

    updateCountdown();
    const interval = setInterval(updateCountdown, 1000);

    return () => clearInterval(interval);
  }, [trialEnd]);

  if (!timeRemaining) {
    return (
      <div className={cn("text-sm text-red-600 font-medium", className)}>
        Trial expirado
      </div>
    );
  }

  const isUrgent = timeRemaining.days <= 2;
  const isWarning = timeRemaining.days <= 5;

  return (
    <div className={cn(
      "text-sm font-medium",
      isUrgent ? "text-red-600" : isWarning ? "text-orange-600" : "text-blue-600",
      className
    )}>
      {timeRemaining.days > 0 ? (
        `${timeRemaining.days}d ${timeRemaining.hours}h ${timeRemaining.minutes}m`
      ) : timeRemaining.hours > 0 ? (
        `${timeRemaining.hours}h ${timeRemaining.minutes}m ${timeRemaining.seconds}s`
      ) : (
        `${timeRemaining.minutes}m ${timeRemaining.seconds}s`
      )}
    </div>
  );
}

interface TrialProgressBarProps {
  trialStart?: string;
  trialEnd: string;
  className?: string;
}

export function TrialProgressBar({ trialStart, trialEnd, className }: TrialProgressBarProps) {
  const now = new Date().getTime();
  const trialEndTime = new Date(trialEnd).getTime();
  const trialStartTime = trialStart ? new Date(trialStart).getTime() : trialEndTime - (7 * 24 * 60 * 60 * 1000); // Assume 7 days if no start date

  const totalDuration = trialEndTime - trialStartTime;
  const elapsed = now - trialStartTime;
  const progress = Math.min(Math.max((elapsed / totalDuration) * 100, 0), 100);

  const isExpired = now > trialEndTime;
  const isUrgent = progress > 80;
  const isWarning = progress > 60;

  return (
    <div className={cn("w-full", className)}>
      <div className="flex justify-between text-xs text-muted-foreground mb-1">
        <span>Trial Progress</span>
        <span>{progress.toFixed(0)}%</span>
      </div>
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div
          className={cn(
            "h-2 rounded-full transition-all duration-300",
            isExpired ? "bg-red-500" : 
            isUrgent ? "bg-red-400" : 
            isWarning ? "bg-orange-400" : 
            "bg-blue-400"
          )}
          style={{ width: `${Math.min(progress, 100)}%` }}
        />
      </div>
    </div>
  );
}
