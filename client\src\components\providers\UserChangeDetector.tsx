import { ReactNode } from 'react';
import { useUserChangeDetection } from '@/hooks/useUserChangeDetection';

interface UserChangeDetectorProps {
  children: ReactNode;
}

/**
 * Componente que detecta mudanças de usuário e gerencia a limpeza de dados
 * Deve ser colocado dentro dos providers de autenticação
 */
export function UserChangeDetector({ children }: UserChangeDetectorProps) {
  // Este hook irá detectar mudanças de usuário e limpar/recarregar dados automaticamente
  useUserChangeDetection();

  return <>{children}</>;
}
