import React, { useState, ReactNode, useEffect, useRef } from 'react';
import { Link, useLocation } from 'wouter';
import { Button } from '@/components/ui/button';
import { useTranslation } from '@/hooks/useTranslation';
import { useAuth } from '@/hooks/useAuth';
import { Store, Menu, PanelsLeftBottom, ShoppingCart, Instagram, Phone, MessageSquareText } from 'lucide-react';
import FloatingCartButton from '@/components/store/FloatingCartButton';
import EnvironmentBadge, { useEnvironment } from '@/components/common/EnvironmentBadge';

interface StoreLayoutProps {
  children: ReactNode;
  store: any;
  showCartButton?: boolean;
}

export function StoreLayout({ children, store, showCartButton = true }: StoreLayoutProps) {
  const { t } = useTranslation();
  const { isAuthenticated } = useAuth();
  const [, setLocation] = useLocation();
  // Removido estado do modal do carrinho pois agora é uma página
  const [isHeaderVisible, setIsHeaderVisible] = useState(true);
  const [lastScrollY, setLastScrollY] = useState(0);
  const headerRef = useRef<HTMLDivElement>(null);

  // Estado para espaço reservado do header
  const [headerHeight, setHeaderHeight] = useState(0);

  // Hook para verificar ambiente
  const { isDevelopment } = useEnvironment();

  // Apply store colors
  const storeColors = store?.colors || { primary: "#FF6B6B", secondary: "#4ECDC4", accent: "#FFD166" };

  // CSS variables for applying direct color values
  const cssColorVariables = {
    "--primary-color": storeColors.primary,
    "--secondary-color": storeColors.secondary,
    "--accent-color": storeColors.accent,
    "--background-color": "#FFFFFF",
    "--foreground-color": "#111"
  } as React.CSSProperties;

  // Store theme class
  const storeThemeClass = `store-theme`;

  // Efeito para controlar a visibilidade do header com base na direção do scroll
  useEffect(() => {
    // Função para controlar o scroll
    const controlHeader = () => {
      if (typeof window !== 'undefined') {
        // Obtendo a posição atual do scroll
        const currentScrollY = window.scrollY;

        // Determinar a direção do scroll
        // Se a posição atual for maior que a última registrada, estamos rolando para baixo
        const isScrollingDown = currentScrollY > lastScrollY;

        // Somente atualizar a visibilidade se estivermos rolando mais de 5px
        // Isso evita oscilações em pequenos movimentos de scroll
        if (Math.abs(currentScrollY - lastScrollY) > 5) {
          // Se estiver rolando para baixo e o header está visível, escondê-lo
          // Se estiver rolando para cima e o header está escondido, mostrá-lo
          setIsHeaderVisible(!isScrollingDown);
        }

        // Atualizar a última posição do scroll
        setLastScrollY(currentScrollY);
      }
    };

    // Listener para o evento de scroll
    window.addEventListener('scroll', controlHeader);

    // Medir a altura do header para reservar espaço
    if (headerRef.current) {
      setHeaderHeight(headerRef.current.offsetHeight);
    }

    // Cleanup function
    return () => {
      window.removeEventListener('scroll', controlHeader);
    };
  }, [lastScrollY]);

  return (
    <div className={`min-h-screen flex flex-col ${storeThemeClass}`} style={cssColorVariables}>
      {/* Badge de ambiente */}
      <EnvironmentBadge />

      {/* Espaço reservado para o header (considerando badge se presente) */}
      <div style={{ height: headerHeight + (isDevelopment ? 32 : 0) }} className="w-full"></div>

      {/* Header de estilo iOS */}
      <header
        ref={headerRef}
        className={`fixed left-0 right-0 z-40 shadow-sm transition-transform duration-300 ease-in-out ${isHeaderVisible ? 'translate-y-0' : '-translate-y-full'}`}
        style={{
          top: isDevelopment ? '32px' : '0px',
          transform: isHeaderVisible ? `translateY(${isDevelopment ? '0' : '0'})` : 'translateY(-100%)',
          backgroundColor: store?.colors?.primary || '#2ECC71',
          color: 'white'
        }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-3">
            <div className="flex items-center space-x-3">
              {store?.logo ? (
                <img
                  src={store.logo}
                  alt={`${store?.name} logo`}
                  className="h-8 w-8 object-cover rounded-full border-2 border-white"
                />
              ) : null}
              <h1 className="text-white text-xl font-bold font-heading">{store?.name || 'Store'}</h1>
            </div>
            <div className="flex items-center space-x-2">
              {/* Admin button */}
              {isAuthenticated && (
                <Button
                  variant="ghost"
                  size="icon"
                  className="hover:bg-white/20 text-white"
                  onClick={() => setLocation('/admin')}
                >
                  <PanelsLeftBottom className="h-5 w-5" />
                </Button>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Store Banner */}
      <div className={`relative ${store?.headerImage ? 'h-48' : 'bg-gradient-to-r from-primary to-secondary'} text-white`}>
        {store?.headerImage ? (
          <>
            <img
              src={store.headerImage}
              alt={`${store?.name} banner`}
              className="w-full h-full object-cover absolute inset-0"
            />
            <div className="absolute inset-0 bg-black bg-opacity-40"></div>
          </>
        ) : null}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 text-center relative z-10">
          {store?.description && (
            <p className="text-lg mb-6">{store.description}</p>
          )}

          {/* CTA Button */}
          <Button
            onClick={() => {
              // Scroll suave até o dropdown de categorias
              const categoriesElement = document.querySelector('[data-categories-section]');
              if (categoriesElement) {
                categoriesElement.scrollIntoView({
                  behavior: 'smooth',
                  block: 'start'
                });
              }
            }}
            size="lg"
            className="bg-white hover:bg-gray-50 font-semibold px-6 py-3 md:px-8 md:py-4 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 active:scale-95 text-base md:text-lg"
            style={{
              color: store?.colors?.primary || '#2ECC71',
              borderColor: store?.colors?.primary || '#2ECC71'
            }}
          >
            {t('storefront.placeOrder')}
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <main className="flex-grow bg-neutral-light">
        <div className="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
          {React.Children.map(children, child => {
            // Passa as propriedades relacionadas ao header para os componentes filhos
            if (React.isValidElement(child)) {
              return React.cloneElement(child as React.ReactElement<any>, {
                isHeaderVisible,
                headerHeight,
              });
            }
            return child;
          })}
        </div>
      </main>

      {/* Floating Cart Button - navegacão para página de carrinho */}
      <FloatingCartButton />

      {/* Footer */}
      <footer className="bg-white border-t border-gray-200">
        <div className="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
          {/* Contatos e redes sociais */}
          <div className="flex flex-col md:flex-row justify-center items-center space-y-4 md:space-y-0 md:space-x-8 mb-6">
            {store?.instagram && (
              <a
                href={`https://instagram.com/${store.instagram}`}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-2 text-neutral-dark hover:text-primary transition-colors duration-200 group"
              >
                <div className="bg-pink-50 p-2 rounded-full group-hover:bg-pink-100 transition-colors">
                  <Instagram className="h-6 w-6 text-pink-600" />
                </div>
                <span className="font-medium">@{store.instagram}</span>
              </a>
            )}
            {store?.whatsapp && (
              <a
                href={`https://wa.me/${store.countryCode ? store.countryCode.replace('+', '') : '55'}${store.whatsapp}`}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-2 text-neutral-dark hover:text-primary transition-colors duration-200 group"
              >
                <div className="bg-green-50 p-2 rounded-full group-hover:bg-green-100 transition-colors">
                  <MessageSquareText className="h-6 w-6 text-green-600" />
                </div>
                <span className="font-medium">{store.countryCode || '+55'} {store.whatsapp}</span>
              </a>
            )}
          </div>

          {/* Copyright */}
          <div className="border-t border-gray-100 pt-6">
            <p className="text-center text-neutral-dark text-sm">
              &copy; {new Date().getFullYear()} {store?.name || 'Store'}. {t('storefront.poweredBy')}.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}

export default StoreLayout;
