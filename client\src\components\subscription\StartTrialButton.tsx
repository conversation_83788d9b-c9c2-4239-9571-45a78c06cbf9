import { useState } from 'react';
import { Zap, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { useStoreTrial } from '@/hooks/useStoreTrial';
import { useTranslation } from '@/hooks/useTranslation';

interface StartTrialButtonProps {
  variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link' | 'destructive';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  className?: string;
  showIcon?: boolean;
  children?: React.ReactNode;
}

export function StartTrialButton({
  variant = 'default',
  size = 'default',
  className = '',
  showIcon = true,
  children
}: StartTrialButtonProps) {
  const { t } = useTranslation();
  const { canStartTrial, startTrial, isStartingTrial } = useStoreTrial();
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);

  // Se não pode iniciar trial, não renderizar o botão
  if (!canStartTrial) {
    return null;
  }

  const handleStartTrial = async () => {
    const success = await startTrial();
    if (success) {
      setShowConfirmDialog(false);
    }
  };

  const buttonText = children || t('subscription.trial.startTrial');

  return (
    <>
      <Button
        variant={variant}
        size={size}
        onClick={() => setShowConfirmDialog(true)}
        className={`bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white border-0 ${className}`}
        disabled={isStartingTrial}
      >
        {showIcon && <Zap className="h-4 w-4 mr-2" />}
        {buttonText}
      </Button>

      {/* Dialog de confirmação */}
      <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <AlertDialogContent className="max-w-md">
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center space-x-2">
              <Zap className="h-5 w-5 text-green-600" />
              <span>{t('subscription.trial.confirmTitle')}</span>
            </AlertDialogTitle>
            <AlertDialogDescription className="space-y-4">
              <p>{t('subscription.trial.confirmDescription')}</p>
              
              <div className="bg-gradient-to-r from-green-50 to-blue-50 p-4 rounded-lg border border-green-200">
                <h4 className="font-semibold text-green-800 mb-2">
                  {t('subscription.trial.benefits.title')}
                </h4>
                <ul className="text-sm text-green-700 space-y-1">
                  <li>• {t('subscription.trial.benefits.unlimitedProducts')}</li>
                  <li>• {t('subscription.trial.benefits.unlimitedOrders')}</li>
                  <li>• {t('subscription.trial.benefits.pdfGeneration')}</li>
                  <li>• {t('subscription.trial.benefits.analytics')}</li>
                  <li>• {t('subscription.trial.benefits.coupons')}</li>
                  <li>• {t('subscription.trial.benefits.support')}</li>
                </ul>
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isStartingTrial}>
              {t('common.cancel')}
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleStartTrial}
              disabled={isStartingTrial}
              className="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700"
            >
              {isStartingTrial ? (
                <div className="flex items-center space-x-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span>{t('common.loading')}</span>
                </div>
              ) : (
                <div className="flex items-center space-x-2">
                  <Zap className="h-4 w-4" />
                  <span>{t('subscription.trial.startTrial')}</span>
                </div>
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
