import { useTranslation } from 'react-i18next';
import { Crown, AlertTriangle, TrendingUp } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useSubscription } from '@/context/SubscriptionContext';
import { cn } from '@/lib/utils';

interface UsageIndicatorProps {
  feature: 'maxProducts' | 'maxOrdersPerMonth';
  label: string;
  icon?: React.ReactNode;
  showUpgradeButton?: boolean;
  compact?: boolean;
}

export function UsageIndicator({
  feature,
  label,
  icon,
  showUpgradeButton = true,
  compact = false
}: UsageIndicatorProps) {
  const { t } = useTranslation();
  const { usageInfo, createCheckoutSession } = useSubscription();

  if (!usageInfo) return null;

  // Mapear feature para a estrutura correta do usageInfo
  const featureKey = feature === 'maxProducts' ? 'products' : 'orders';
  const usage = usageInfo.usage[featureKey];

  if (!usage) return null;

  const percentage = usage.limit === -1 ? 0 : Math.min(100, (usage.current / usage.limit) * 100);
  const isNearLimit = percentage > 80;
  const isAtLimit = usage.isLimitExceeded;
  const isUnlimited = usage.limit === -1;

  const handleUpgrade = async () => {
    try {
      const checkoutUrl = await createCheckoutSession();
      if (checkoutUrl) {
        window.location.href = checkoutUrl;
      }
    } catch (error) {
      console.error('Erro ao criar checkout:', error);
    }
  };

  if (compact) {
    return (
      <div className="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
        <div className="flex items-center gap-2">
          {icon}
          <span className="text-sm font-medium">{label}</span>
        </div>
        <div className="flex items-center gap-2">
          <span className={cn(
            "text-sm font-medium",
            isAtLimit && "text-red-600",
            isNearLimit && !isAtLimit && "text-yellow-600"
          )}>
            {usage.current}
            {!isUnlimited && ` / ${usage.limit}`}
            {isUnlimited && " (∞)"}
          </span>
          {isAtLimit && (
            <Badge variant="destructive" className="text-xs">
              Limite
            </Badge>
          )}
        </div>
      </div>
    );
  }

  return (
    <Card className={cn(
      "transition-all duration-200",
      isAtLimit && "border-red-200 bg-red-50",
      isNearLimit && !isAtLimit && "border-yellow-200 bg-yellow-50"
    )}>
      <CardContent className="p-4">
        <div className="space-y-3">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {icon}
              <span className="font-medium text-gray-900">{label}</span>
            </div>
            {isAtLimit && (
              <Badge variant="destructive" className="flex items-center gap-1">
                <AlertTriangle className="w-3 h-3" />
                Limite atingido
              </Badge>
            )}
            {isNearLimit && !isAtLimit && (
              <Badge variant="outline" className="border-yellow-500 text-yellow-700">
                Próximo ao limite
              </Badge>
            )}
          </div>

          {/* Usage Stats */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Uso atual</span>
              <span className={cn(
                "font-medium",
                isAtLimit && "text-red-600",
                isNearLimit && !isAtLimit && "text-yellow-600",
                !isNearLimit && "text-gray-900"
              )}>
                {usage.current}
                {!isUnlimited && ` de ${usage.limit}`}
                {isUnlimited && " (ilimitado)"}
              </span>
            </div>

            {!isUnlimited && (
              <Progress 
                value={percentage} 
                className={cn(
                  "h-2",
                  isAtLimit && "[&>div]:bg-red-500",
                  isNearLimit && !isAtLimit && "[&>div]:bg-yellow-500"
                )}
              />
            )}
          </div>

          {/* Upgrade Button */}
          {isAtLimit && showUpgradeButton && usageInfo.planConfig.id === 'free' && (
            <Button
              onClick={handleUpgrade}
              size="sm"
              className="w-full bg-gradient-to-r from-yellow-600 to-orange-600 hover:from-yellow-700 hover:to-orange-700"
            >
              <Crown className="w-3 h-3 mr-1" />
              Fazer Upgrade para Premium
            </Button>
          )}

          {/* Info for unlimited */}
          {isUnlimited && (
            <div className="flex items-center gap-2 text-sm text-green-600 bg-green-50 p-2 rounded">
              <TrendingUp className="w-4 h-4" />
              <span>Uso ilimitado no plano Premium</span>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

// Componente para mostrar múltiplos indicadores
export function UsageOverview() {
  const { t } = useTranslation();
  const { usageInfo } = useSubscription();

  if (!usageInfo) return null;

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-900">
        Uso do Plano
      </h3>

      <div className="grid gap-4 md:grid-cols-2">
        <UsageIndicator
          feature="maxProducts"
          label={t('subscription.usage.products')}
          icon={<TrendingUp className="w-4 h-4 text-blue-500" />}
        />

        <UsageIndicator
          feature="maxOrdersPerMonth"
          label={t('subscription.usage.orders')}
          icon={<TrendingUp className="w-4 h-4 text-green-500" />}
        />
      </div>
    </div>
  );
}

// Componente compacto para header/navbar
export function CompactUsageIndicator() {
  const { usageInfo } = useSubscription();

  if (!usageInfo) return null;

  const productUsage = usageInfo.usage.products;
  const orderUsage = usageInfo.usage.orders;

  if (!productUsage || !orderUsage) return null;

  const hasLimitIssue = productUsage.isLimitExceeded || orderUsage.isLimitExceeded;

  if (!hasLimitIssue) return null;

  return (
    <div className="flex items-center gap-2 px-3 py-1 bg-red-50 border border-red-200 rounded-full">
      <AlertTriangle className="w-3 h-3 text-red-500" />
      <span className="text-xs font-medium text-red-700">
        Limite atingido
      </span>
    </div>
  );
}

// Hook para usar indicadores de uso
export function useUsageIndicators() {
  const { usageInfo } = useSubscription();

  if (!usageInfo) {
    return {
      hasLimitIssues: false,
      nearLimitIssues: false,
      productUsage: null,
      orderUsage: null,
    };
  }

  const productUsage = usageInfo.usage.products;
  const orderUsage = usageInfo.usage.orders;

  if (!productUsage || !orderUsage) {
    return {
      hasLimitIssues: false,
      nearLimitIssues: false,
      productUsage: null,
      orderUsage: null,
    };
  }

  const hasLimitIssues = productUsage.isLimitExceeded || orderUsage.isLimitExceeded;
  const nearLimitIssues =
    (!productUsage.isLimitExceeded && productUsage.limit !== -1 && (productUsage.current / productUsage.limit) > 0.8) ||
    (!orderUsage.isLimitExceeded && orderUsage.limit !== -1 && (orderUsage.current / orderUsage.limit) > 0.8);

  return {
    hasLimitIssues,
    nearLimitIssues,
    productUsage,
    orderUsage,
  };
}