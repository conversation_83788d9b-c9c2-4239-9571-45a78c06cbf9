import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useAuth } from '@/context/FirebaseAuthContext';
import { apiRequest } from '@/lib/queryClient';

// Store types
interface StoreColors {
  primary: string;
  secondary: string;
  accent: string;
}

interface PaymentMethods {
  cash: boolean;
  creditCard: boolean;
  debitCard: boolean;
  pix: boolean;
  bankTransfer: boolean;
  customMethods?: string[];
}

interface DeliverySettings {
  allowDelivery: boolean;
  allowPickup: boolean;
  pickupDays: string[];
  pickupTimeSlots: string[];
  deliveryDays: string[];
  deliveryTimeSlots: string[];
  deliveryFee: number;
  minAdvanceDays: number;
  customMessage: string;
  unavailablePeriods: Array<{
    startDate: string;
    endDate: string;
    reason?: string;
  }>;
}

interface Store {
  id: number;
  name: string;
  slug: string;
  description: string | null;
  logo: string | null;
  headerImage: string | null;
  colors: StoreColors;
  paymentMethods: PaymentMethods;
  currency: string; // Moeda da loja
  deliverySettings: DeliverySettings;
  countryCode?: string;
  whatsapp?: string;
  instagram?: string;
  layout?: number;
  layoutSettings?: Record<string, any>;
  // Campos de endereço da loja
  addressStreet?: string;
  addressNumber?: string;
  addressComplement?: string;
  addressNeighborhood?: string;
  addressCity?: string;
  addressState?: string;
  contactEmail?: string;
}

// Context type
interface StoreContextType {
  store: Store | null;
  isLoading: boolean;
  error: Error | null;
  createStore: (storeData: Partial<Store>) => Promise<Store>;
  updateStore: (storeData: Partial<Store>) => Promise<Store>;
  uploadLogo: (file: File) => Promise<{ logo: string; store: Store }>;
  uploadHeaderImage: (file: File) => Promise<{ headerImage: string; store: Store }>;
  fetchStoreBySlug: (slug: string) => Promise<Store | null>;
  refreshStoreData: (slug: string) => Promise<Store | null>;
}

// Create context
const StoreContext = createContext<StoreContextType | undefined>(undefined);

// Provider component
export function StoreProvider({ children }: { children: ReactNode }) {
  const [store, setStore] = useState<Store | null>(null);
  const { isAuthenticated, user } = useAuth();

  // Fetch store data for authenticated users
  const { data, isLoading, error } = useQuery({
    queryKey: ['/api/stores/me'],
    // No custom queryFn here - let the default fetch with auth headers handle it
    // This will use the default queryFn from queryClient.ts, which properly includes auth headers
    enabled: isAuthenticated
  });

  // Reset store when user changes or logs out
  useEffect(() => {
    if (!isAuthenticated || !user) {
      console.log("🔄 Usuário não autenticado ou mudou - limpando dados da loja");
      setStore(null);
    }
  }, [isAuthenticated, user?.id]); // Depend on user.id to detect user changes

  // Update local state when data changes
  useEffect(() => {
    if (data) {
      console.log("Dados da loja recebidos no StoreContext:", data);

      // Usar exatamente os dados retornados do banco
      const storeData = data as any;

      console.log("Dados da loja processados:", storeData);
      setStore(storeData);
    } else if (isAuthenticated) {
      // Se não há dados mas o usuário está autenticado, limpar store
      console.log("🔄 Sem dados da loja para usuário autenticado - limpando store");
      setStore(null);
    }
  }, [data, isAuthenticated]);

  // Create a new store
  const createStore = async (storeData: Partial<Store>): Promise<Store> => {
    try {
      const response = await apiRequest('POST', '/api/stores', storeData);
      const newStore = await response.json();
      setStore(newStore);
      return newStore;
    } catch (error) {
      console.error('Create store error:', error);
      throw error;
    }
  };

  // Update store
  const updateStore = async (storeData: Partial<Store>): Promise<Store> => {
    try {
      const response = await apiRequest('PUT', '/api/stores/me', storeData);
      const updatedStore = await response.json();
      setStore(updatedStore);
      return updatedStore;
    } catch (error) {
      console.error('Update store error:', error);
      throw error;
    }
  };

  // Upload logo
  const uploadLogo = async (file: File): Promise<{ logo: string; store: Store }> => {
    try {
      // Get the current user uid from auth context
      if (!user || !user.firebaseUser) {
        throw new Error("Você precisa estar logado para fazer upload do logo");
      }

      // Create FormData to send file
      const formData = new FormData();
      formData.append('logo', file);

      // Add the uid to the headers or as a query parameter since authentication is done via middleware
      const uid = user.firebaseUser.uid;

      console.log('Uploading logo with uid:', uid);

      const response = await fetch(`/api/stores/me/logo?uid=${uid}`, {
        method: 'POST',
        body: formData,
        credentials: 'include' // Include cookies for session-based auth
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to upload logo');
      }

      const data = await response.json();

      // Update local store state with new logo URL
      if (data.store) {
        setStore(data.store);
      }

      return data;
    } catch (error) {
      console.error('Upload logo error:', error);
      throw error;
    }
  };

  // Upload header image
  const uploadHeaderImage = async (file: File): Promise<{ headerImage: string; store: Store }> => {
    try {
      // Get the current user uid from auth context
      if (!user || !user.firebaseUser) {
        throw new Error("Você precisa estar logado para fazer upload da imagem de cabeçalho");
      }

      // Create FormData to send file
      const formData = new FormData();
      formData.append('headerImage', file);

      // Add the uid to the headers or as a query parameter since authentication is done via middleware
      const uid = user.firebaseUser.uid;

      console.log('Uploading header image with uid:', uid);

      const response = await fetch(`/api/stores/me/header-image?uid=${uid}`, {
        method: 'POST',
        body: formData,
        credentials: 'include' // Include cookies for session-based auth
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to upload header image');
      }

      const data = await response.json();

      // Update local store state with new header image URL
      if (data.store) {
        setStore(data.store);
      }

      return data;
    } catch (error) {
      console.error('Upload header image error:', error);
      throw error;
    }
  };

  // Cache para armazenar dados da loja por slug
  const storeCache = new Map<string, { data: Store; timestamp: number }>();
  const CACHE_TTL = 5 * 60 * 1000; // 5 minutos em milissegundos

  // Fetch store by slug (for public view)
  const fetchStoreBySlug = async (slug: string, forceRefresh = false): Promise<Store | null> => {
    try {
      // Verificar cache primeiro se não for forçado a atualizar
      const now = Date.now();
      const cachedData = storeCache.get(slug);

      if (!forceRefresh && cachedData && (now - cachedData.timestamp < CACHE_TTL)) {
        console.log(`[StoreContext] Using cached store data for slug: ${slug}`);
        return cachedData.data;
      }

      console.log(`[StoreContext] Fetching fresh store data for slug: ${slug}`);

      const response = await fetch(`/api/public/stores/${slug}`, {
        credentials: 'include'
      });

      if (!response.ok) {
        if (response.status === 404) {
          return null;
        }
        throw new Error('Failed to fetch store data');
      }

      const storeData = await response.json();

      // Logs resumidos para diagnóstico
      console.log("[StoreContext] Store data received:", {
        id: storeData.id,
        name: storeData.name,
        hasPaymentMethods: !!storeData.paymentMethods,
        hasDeliverySettings: !!storeData.deliverySettings
      });

      const processStoreData = (data: any) => {
        if (!data) return null;

        // Definir valores padrão para campos críticos
        const defaultPaymentMethods = {
          cash: true,
          creditCard: false,
          debitCard: false,
          pix: false,
          bankTransfer: false,
          customMethods: []
        };

        const defaultDeliverySettings = {
          allowDelivery: false,
          allowPickup: true,
          pickupDays: [],
          pickupTimeSlots: [],
          deliveryDays: [],
          deliveryTimeSlots: [],
          deliveryFee: 0,
          minAdvanceDays: 0,
          customMessage: "",
          unavailablePeriods: []
        };

        // Verificar e processar paymentMethods
        let paymentMethodsData;
        if (data.paymentMethods) {
          paymentMethodsData = {
            ...defaultPaymentMethods,
            ...data.paymentMethods,
            customMethods: data.paymentMethods.customMethods || []
          };
        } else if (data.payment_methods) {
          paymentMethodsData = {
            ...defaultPaymentMethods,
            ...data.payment_methods,
            customMethods: data.payment_methods.customMethods || []
          };
        } else {
          paymentMethodsData = defaultPaymentMethods;
        }

        // Verificar e processar deliverySettings
        let deliverySettingsData;
        if (data.deliverySettings) {
          deliverySettingsData = {
            ...defaultDeliverySettings,
            ...data.deliverySettings
          };
        } else if (data.delivery_settings) {
          deliverySettingsData = {
            ...defaultDeliverySettings,
            ...data.delivery_settings
          };
        } else {
          deliverySettingsData = defaultDeliverySettings;
        }

        // Retornar os dados processados
        return {
          ...data,
          paymentMethods: paymentMethodsData,
          deliverySettings: deliverySettingsData
        };
      };

      const processedStoreData = processStoreData(storeData) as Store;

      // Atualizar o cache
      storeCache.set(slug, {
        data: processedStoreData,
        timestamp: now
      });

      return processedStoreData;
    } catch (error) {
      console.error('Error fetching store by slug:', error);
      throw error;
    }
  };

  // Função para recarregar dados da loja (usada principalmente pela CartPage)
  const refreshStoreData = async (slug: string): Promise<Store | null> => {
    console.log(`[StoreContext] Refreshing store data for slug: ${slug}`);

    // Se já temos dados da loja no estado e é o mesmo slug, não precisamos recarregar
    if (store && store.slug === slug) {
      console.log(`[StoreContext] Store data already loaded for slug: ${slug}`);
      return store;
    }

    try {
      const storeData = await fetchStoreBySlug(slug);
      if (storeData) {
        setStore(storeData);
      }
      return storeData;
    } catch (error) {
      console.error('Error refreshing store data:', error);
      throw error;
    }
  };

  return (
    <StoreContext.Provider
      value={{
        store,
        isLoading,
        error: error as Error | null,
        createStore,
        updateStore,
        uploadLogo,
        uploadHeaderImage,
        fetchStoreBySlug,
        refreshStoreData
      }}
    >
      {children}
    </StoreContext.Provider>
  );
}

// Hook to use the store context
export function useStore() {
  const context = useContext(StoreContext);
  if (context === undefined) {
    throw new Error('useStore must be used within a StoreProvider');
  }
  return context;
}
