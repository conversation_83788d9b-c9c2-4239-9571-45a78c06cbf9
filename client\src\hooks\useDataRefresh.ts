import { useQueryClient } from "@tanstack/react-query";
import { useCallback } from "react";

/**
 * Hook para forçar atualização de dados após mudanças de autenticação
 */
export function useDataRefresh() {
  const queryClient = useQueryClient();

  /**
   * Força a invalidação e refetch de todos os dados do usuário
   */
  const refreshAllUserData = useCallback(async () => {
    console.log('🔄 Forçando refresh completo dos dados do usuário...');

    try {
      // 1. Primeiro, remover dados antigos do cache para evitar dados stale
      const queriesToRemove = [
        ['/api/stores/me'],
        ['/api/dashboard'],
        ['/api/analytics/dashboard'],
        ['/api/subscriptions/usage'],
        ['/api/orders'],
        ['/api/products'],
        ['/api/customers'],
        ['/api/coupons'],
        ['/api/analytics/financial'],
        ['/api/analytics/products'],
        ['/api/analytics/customers']
      ];

      // Remover queries específicas do cache
      queriesToRemove.forEach(queryKey => {
        queryClient.removeQueries({ queryKey });
      });

      // Remover queries com padrões específicos
      queryClient.removeQueries({
        predicate: (query) => {
          const queryKey = query.queryKey;
          if (!Array.isArray(queryKey) || typeof queryKey[0] !== 'string') return false;

          const key = queryKey[0] as string;
          return key.startsWith('/api/orders/') ||
                 key.startsWith('/api/products/') ||
                 key.startsWith('/api/customers/') ||
                 key.startsWith('/api/analytics/') ||
                 key.startsWith('/api/subscriptions/') ||
                 key.startsWith('/api/coupons/');
        }
      });

      console.log('🗑️ Cache antigo removido');

      // 2. Aguardar um pouco para garantir que as remoções foram processadas
      await new Promise(resolve => setTimeout(resolve, 50));

      // 3. Invalidar todas as queries principais para forçar refetch
      const queriesToInvalidate = [
        ['/api/stores/me'],
        ['/api/dashboard'],
        ['/api/analytics/dashboard'],
        ['/api/subscriptions/usage'],
        ['/api/orders'],
        ['/api/products'],
        ['/api/customers'],
        ['/api/coupons'],
        ['/api/analytics/financial'],
        ['/api/analytics/products'],
        ['/api/analytics/customers']
      ];

      // Invalidar queries específicas
      await Promise.all(
        queriesToInvalidate.map(queryKey =>
          queryClient.invalidateQueries({ queryKey })
        )
      );

      // 4. Invalidar queries que começam com padrões específicos
      await queryClient.invalidateQueries({
        predicate: (query) => {
          const queryKey = query.queryKey;
          if (!Array.isArray(queryKey) || typeof queryKey[0] !== 'string') return false;

          const key = queryKey[0] as string;
          return key.startsWith('/api/orders/') ||
                 key.startsWith('/api/products/') ||
                 key.startsWith('/api/customers/') ||
                 key.startsWith('/api/analytics/') ||
                 key.startsWith('/api/subscriptions/') ||
                 key.startsWith('/api/coupons/');
        }
      });

      // 5. Aguardar um pouco para garantir que as invalidações foram processadas
      await new Promise(resolve => setTimeout(resolve, 100));

      // 6. Forçar refetch das queries mais importantes
      const criticalQueries = [
        ['/api/stores/me'],
        ['/api/subscriptions/usage'],
        ['/api/dashboard']
      ];

      await Promise.all(
        criticalQueries.map(queryKey =>
          queryClient.refetchQueries({
            queryKey,
            type: 'active'
          })
        )
      );

      console.log('✅ Refresh completo dos dados concluído');
    } catch (error) {
      console.error('❌ Erro durante refresh dos dados:', error);
    }
  }, [queryClient]);

  /**
   * Limpa todo o cache (usado no logout)
   */
  const clearAllData = useCallback(() => {
    console.log('🗑️ Limpando todo o cache...');
    queryClient.clear();
    console.log('✅ Cache limpo');
  }, [queryClient]);

  /**
   * Invalida apenas dados específicos de uma entidade
   */
  const refreshEntityData = useCallback(async (entity: 'orders' | 'products' | 'customers' | 'analytics' | 'subscriptions') => {
    console.log(`🔄 Refreshing ${entity} data...`);
    
    const entityQueries: Record<string, string[]> = {
      orders: ['/api/orders', '/api/dashboard'],
      products: ['/api/products', '/api/dashboard'],
      customers: ['/api/customers', '/api/dashboard'],
      analytics: ['/api/analytics/dashboard', '/api/analytics/financial', '/api/analytics/products', '/api/analytics/customers'],
      subscriptions: ['/api/subscriptions/usage', '/api/subscriptions/status']
    };

    const queries = entityQueries[entity] || [];
    
    await Promise.all(
      queries.map(queryKey => 
        queryClient.invalidateQueries({ queryKey: [queryKey] })
      )
    );

    // Invalidar queries relacionadas
    await queryClient.invalidateQueries({
      predicate: (query) => {
        const queryKey = query.queryKey;
        if (!Array.isArray(queryKey) || typeof queryKey[0] !== 'string') return false;
        
        const key = queryKey[0] as string;
        return key.startsWith(`/api/${entity}/`);
      }
    });

    console.log(`✅ ${entity} data refreshed`);
  }, [queryClient]);

  return {
    refreshAllUserData,
    clearAllData,
    refreshEntityData
  };
}
