import { useQuery } from '@tanstack/react-query';
import { useAuth } from '@/context/FirebaseAuthContext';
import { apiRequest } from '@/lib/queryClient';

interface GlobalAdminUser {
  id: number;
  email: string;
  fullName: string;
  isGlobalAdmin: boolean;
}

interface GlobalAnalytics {
  summary: {
    totalStores: number;
    activeStores: number;
    premiumStores: number;
    totalRevenue: number;
    totalOrders: number;
    avgOrderValue: number;
    revenueGrowth: number;
    orderGrowth: number;
    trialConversionRate: number;
    activeTrials: number;
  };
  topStoresByOrders: Array<{
    storeId: number;
    storeName: string;
    storeSlug: string;
    orderCount: number;
    isActive: boolean;
  }>;
  topStoresByRevenue: Array<{
    storeId: number;
    storeName: string;
    storeSlug: string;
    revenue: number;
    isActive: boolean;
  }>;
  subscriptionDistribution: {
    free: number;
    activePaid: number;
    activeTrial: number;
    expiredTrialCanceled: number;
    canceled: number;
    pastDue: number;
  };
  trialMetrics: {
    totalTrialsStarted: number;
    trialsConverted: number;
    trialConversionRate: number;
    activeTrialSubscriptions: Array<{
      subscriptionId: number;
      storeId: number;
      trialEnd: string;
      daysRemaining: number;
    }>;
    avgTrialDaysRemaining: number;
  };
}

interface GlobalStore {
  id: number;
  name: string;
  slug: string;
  description?: string;
  logo?: string;
  createdAt: string;
  updatedAt?: string;
  isActive: boolean;
  subscription?: {
    id: number;
    planType: 'free' | 'premium';
    status: string;
    currentPeriodEnd?: string;
    trialEnd?: string;
    isTrialActive?: boolean;
    daysRemaining?: number;
  };
  user?: {
    id: number;
    email: string;
    fullName: string;
    createdAt: string;
  };
  metrics?: {
    totalProducts: number;
    totalOrders: number;
    totalCustomers: number;
    recentOrders: number;
    recentRevenue: number;
    avgOrderValue: number;
  };
}

interface GlobalStoresResponse {
  stores: GlobalStore[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalStores: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
}

/**
 * Hook para verificar se o usuário atual é um super-administrador global
 */
export function useGlobalAdminGuard() {
  const { user, isAuthenticated } = useAuth();

  return useQuery<GlobalAdminUser>({
    queryKey: ['/api/auth/user'],
    enabled: isAuthenticated && !!user,
    retry: false,
    staleTime: 5 * 60 * 1000, // 5 minutos
    select: (data: any) => {
      // Mapear is_global_admin para isGlobalAdmin se necessário
      return {
        ...data,
        isGlobalAdmin: data.isGlobalAdmin || data.is_global_admin || false
      };
    }
  });
}

/**
 * Hook para buscar analytics globais da plataforma
 */
export function useGlobalAnalytics() {
  const { data: adminUser } = useGlobalAdminGuard();

  return useQuery<GlobalAnalytics>({
    queryKey: ['/api/admin/global/analytics'],
    enabled: !!adminUser?.isGlobalAdmin,
    refetchInterval: 5 * 60 * 1000, // Atualizar a cada 5 minutos
    staleTime: 2 * 60 * 1000, // 2 minutos
  });
}

/**
 * Hook para buscar lojas globais com filtros e paginação
 */
export function useGlobalStores(params: {
  page?: number;
  limit?: number;
  status?: 'active' | 'inactive';
  planType?: 'free' | 'premium';
  search?: string;
  sortBy?: 'name' | 'createdAt' | 'lastActivity';
  sortOrder?: 'asc' | 'desc';
} = {}) {
  const { data: adminUser } = useGlobalAdminGuard();

  const queryParams = new URLSearchParams();
  if (params.page) queryParams.append('page', params.page.toString());
  if (params.limit) queryParams.append('limit', params.limit.toString());
  if (params.status) queryParams.append('status', params.status);
  if (params.planType) queryParams.append('planType', params.planType);
  if (params.search) queryParams.append('search', params.search);
  if (params.sortBy) queryParams.append('sortBy', params.sortBy);
  if (params.sortOrder) queryParams.append('sortOrder', params.sortOrder);

  const queryString = queryParams.toString();
  const url = `/api/admin/global/stores${queryString ? `?${queryString}` : ''}`;

  return useQuery<GlobalStoresResponse>({
    queryKey: [url],
    enabled: !!adminUser?.isGlobalAdmin,
    staleTime: 30 * 1000, // 30 segundos
  });
}

/**
 * Hook para buscar detalhes específicos de uma loja
 */
export function useGlobalStoreDetails(storeId: number) {
  const { data: adminUser } = useGlobalAdminGuard();

  return useQuery<GlobalStore>({
    queryKey: [`/api/admin/global/stores/${storeId}`],
    enabled: !!adminUser?.isGlobalAdmin && !!storeId,
    staleTime: 60 * 1000, // 1 minuto
  });
}

/**
 * Função para alternar status de uma loja
 */
export async function toggleStoreStatus(storeId: number, isActive: boolean): Promise<void> {
  await apiRequest('PATCH', `/api/admin/global/stores/${storeId}/toggle-status`, {
    isActive
  });
}

/**
 * Função para promover/remover usuário como admin global
 */
export async function toggleUserGlobalAdmin(userId: number, isGlobalAdmin: boolean): Promise<void> {
  await apiRequest('PATCH', `/api/admin/global/users/${userId}/toggle-admin`, {
    isGlobalAdmin
  });
}



/**
 * Função para atualizar plano de uma assinatura
 */
export async function updateSubscriptionPlan(subscriptionId: number, planType: 'free' | 'premium'): Promise<void> {
  await apiRequest('PATCH', `/api/admin/global/subscriptions/${subscriptionId}/update-plan`, {
    planType
  });
}

/**
 * Função para atualizar status de uma assinatura
 */
export async function updateSubscriptionStatus(subscriptionId: number, status: 'active' | 'past_due' | 'canceled' | 'unpaid' | 'incomplete'): Promise<void> {
  await apiRequest('PATCH', `/api/admin/global/subscriptions/${subscriptionId}/update-status`, {
    status
  });
}

/**
 * Função para iniciar teste grátis para uma loja específica
 */
export async function startStoreTrial(storeId: number): Promise<{ checkoutUrl: string }> {
  console.log('🔄 startStoreTrial chamada para loja:', storeId);
  try {
    const response = await apiRequest('POST', `/api/admin/global/stores/${storeId}/start-trial`);
    console.log('📦 startStoreTrial response:', response);

    // Se response é um objeto Response, extrair JSON
    let result;
    if (response instanceof Response) {
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Erro na API: ${response.status} ${errorText}`);
      }
      result = await response.json();
    } else {
      // Se já é o objeto de dados
      result = response;
    }

    console.log('✅ startStoreTrial resultado parseado:', result);
    return result;
  } catch (error) {
    console.log('❌ startStoreTrial erro:', error);
    throw error;
  }
}

/**
 * Função utilitária para verificar se o usuário tem permissões de admin global
 */
export function useIsGlobalAdmin(): boolean {
  const { data: adminUser, isLoading } = useGlobalAdminGuard();

  if (isLoading) return false;
  return adminUser?.isGlobalAdmin || false;
}

/**
 * Hook para buscar configurações globais
 */
export function useGlobalSettings() {
  const { data: adminUser } = useGlobalAdminGuard();

  return useQuery<GlobalSetting[]>({
    queryKey: ['/api/admin/global/settings'],
    enabled: !!adminUser?.isGlobalAdmin,
    staleTime: 5 * 60 * 1000, // 5 minutos
  });
}

/**
 * Hook para buscar uma configuração específica
 */
export function useGlobalSetting(key: string) {
  const { data: adminUser } = useGlobalAdminGuard();

  return useQuery<GlobalSetting>({
    queryKey: [`/api/admin/global/settings/${key}`],
    enabled: !!adminUser?.isGlobalAdmin && !!key,
    staleTime: 5 * 60 * 1000, // 5 minutos
  });
}

/**
 * Função para atualizar uma configuração global
 */
export async function updateGlobalSetting(key: string, value: any): Promise<void> {
  await apiRequest('PUT', `/api/admin/global/settings/${key}`, {
    value
  });
}

// Tipos para configurações globais
export interface GlobalSetting {
  id: number;
  key: string;
  value: any;
  description?: string;
  updatedAt: string;
  updatedBy?: number;
}

export interface GlobalSettingsValue {
  freeMaxProducts?: number;
  freeMaxOrdersPerMonth?: number;
  premiumMaxProducts?: number;
  premiumMaxOrdersPerMonth?: number;
}

export type { GlobalAdminUser, GlobalAnalytics, GlobalStore, GlobalStoresResponse };
