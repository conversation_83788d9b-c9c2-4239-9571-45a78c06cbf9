import { useState } from 'react';
import { useStore } from '@/context/StoreContext';
import { useSubscription } from '@/context/SubscriptionContext';
import { useTranslation } from '@/hooks/useTranslation';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';

interface StartTrialResponse {
  checkoutUrl: string;
  message: string;
}

/**
 * Hook para gerenciar o teste grátis da loja atual
 */
export function useStoreTrial() {
  const { store } = useStore();
  const { subscription } = useSubscription();
  const { t } = useTranslation();
  const { toast } = useToast();
  const [isStartingTrial, setIsStartingTrial] = useState(false);

  /**
   * Verifica se a loja pode iniciar o teste grátis
   */
  const canStartTrial = () => {
    if (!store || !subscription) return false;
    
    return (
      subscription.planType === 'free' && 
      !subscription.trialEnd
    );
  };

  /**
   * Inicia o teste grátis para a loja atual
   */
  const startTrial = async (): Promise<boolean> => {
    if (!store) {
      return false;
    }

    if (!canStartTrial()) {
      toast({
        title: t('common.error'),
        description: t('subscription.trial.notEligible'),
        variant: 'destructive',
      });
      return false;
    }

    setIsStartingTrial(true);

    try {
      const response = await apiRequest('POST', `/api/stores/${store.id}/start-trial`);

      // Se response é um objeto Response, extrair JSON
      let result: StartTrialResponse;
      if (response instanceof Response) {
        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`Erro na API: ${response.status} ${errorText}`);
        }
        result = await response.json();
      } else {
        result = response;
      }

      if (result && result.checkoutUrl) {
        // Abrir checkout do Stripe em nova aba
        window.open(result.checkoutUrl, '_blank');

        toast({
          title: t('common.success'),
          description: t('subscription.trial.started'),
        });

        return true;
      } else {
        toast({
          title: t('common.error'),
          description: t('subscription.trial.error'),
          variant: 'destructive',
        });
        return false;
      }
    } catch (error: any) {
      let errorMessage = t('subscription.trial.error');

      if (error.message?.includes('já possui plano premium')) {
        errorMessage = t('subscription.trial.alreadyPremium');
      } else if (error.message?.includes('já utilizou o período de teste')) {
        errorMessage = t('subscription.trial.alreadyUsed');
      }

      toast({
        title: t('common.error'),
        description: errorMessage,
        variant: 'destructive',
      });

      return false;
    } finally {
      setIsStartingTrial(false);
    }
  };

  return {
    canStartTrial: canStartTrial(),
    startTrial,
    isStartingTrial,
  };
}
