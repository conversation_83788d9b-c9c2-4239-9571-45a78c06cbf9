import { useEffect, useRef } from 'react';
import { useAuth } from '@/context/FirebaseAuthContext';
import { useDataRefresh } from './useDataRefresh';

/**
 * Hook para detectar mudanças de usuário e garantir limpeza adequada dos dados
 */
export function useUserChangeDetection() {
  const { user, isAuthenticated } = useAuth();
  const { clearAllData, refreshAllUserData } = useDataRefresh();
  const previousUserIdRef = useRef<string | null>(null);
  const isInitialLoadRef = useRef(true);

  useEffect(() => {
    const currentUserId = user?.id || null;
    const previousUserId = previousUserIdRef.current;

    console.log('🔍 User change detection:', {
      currentUserId,
      previousUserId,
      isAuthenticated,
      isInitialLoad: isInitialLoadRef.current
    });

    // Se é o carregamento inicial, apenas armazenar o usuário atual
    if (isInitialLoadRef.current) {
      previousUserIdRef.current = currentUserId;
      isInitialLoadRef.current = false;
      return;
    }

    // Detectar mudança de usuário
    if (previousUserId !== currentUserId) {
      console.log('🔄 Mudança de usuário detectada!', {
        from: previousUserId,
        to: currentUserId
      });

      // Limpar todos os dados primeiro
      clearAllData();

      // Se há um novo usuário, aguardar um pouco e então refrescar
      if (currentUserId && isAuthenticated) {
        console.log('👤 Novo usuário logado - preparando refresh dos dados...');
        setTimeout(async () => {
          try {
            await refreshAllUserData();
            console.log('✅ Dados do novo usuário carregados');
          } catch (error) {
            console.error('❌ Erro ao carregar dados do novo usuário:', error);
          }
        }, 200); // Aguardar 200ms para garantir que o contexto foi atualizado
      }

      // Atualizar referência do usuário anterior
      previousUserIdRef.current = currentUserId;
    }
  }, [user?.id, isAuthenticated, clearAllData, refreshAllUserData]);

  // Limpar dados quando usuário faz logout
  useEffect(() => {
    if (!isAuthenticated && !isInitialLoadRef.current) {
      console.log('👋 Logout detectado - limpando todos os dados');
      clearAllData();
      previousUserIdRef.current = null;
    }
  }, [isAuthenticated, clearAllData]);

  return {
    currentUserId: user?.id || null,
    previousUserId: previousUserIdRef.current,
    hasUserChanged: previousUserIdRef.current !== (user?.id || null)
  };
}
