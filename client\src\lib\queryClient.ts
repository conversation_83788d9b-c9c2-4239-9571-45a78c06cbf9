import { QueryClient, QueryFunction } from "@tanstack/react-query";

async function throwIfResNotOk(res: Response) {
  if (!res.ok) {
    const text = (await res.text()) || res.statusText;
    throw new Error(`${res.status}: ${text}`);
  }
}

// Função utilitária para construir URLs de API
export function buildApiUrl(path: string): string {
  const apiUrl = import.meta.env.VITE_API_URL;

  // Se VITE_API_URL estiver definida, verificar se não é o mesmo domínio
  if (apiUrl) {
    try {
      const currentHost = window.location.host;
      const apiUrlObj = new URL(apiUrl);
      const apiHost = apiUrlObj.host;

      // Se for o mesmo domínio, usar URL relativa para evitar loops
      if (currentHost === apiHost) {
        console.log(`[buildApiUrl] Same domain detected (${currentHost}), using relative URL`);
        return path;
      }

      // Se for domínio diferente, usar URL absoluta
      console.log(`[buildApiUrl] Different domain detected (${currentHost} → ${apiHost}), using absolute URL`);

      // Remove barra inicial do path se existir
      const cleanPath = path.startsWith('/') ? path.slice(1) : path;
      // Remove barra final da apiUrl se existir
      const cleanApiUrl = apiUrl.endsWith('/') ? apiUrl.slice(0, -1) : apiUrl;
      return `${cleanApiUrl}/${cleanPath}`;
    } catch (error) {
      console.warn(`[buildApiUrl] Error parsing API URL, falling back to relative:`, error);
      return path;
    }
  }

  // Fallback para desenvolvimento (URL relativa)
  console.log(`[buildApiUrl] No VITE_API_URL defined, using relative URL`);
  return path;
}

// Com o novo esquema de autenticação Firebase, precisamos enviar o UID como parâmetro de consulta
async function getAuthHeaders(): Promise<Record<string, string>> {
  try {
    // Verificar se temos o Firebase inicializado e o usuário está logado
    const firebaseModule = await import('./firebase');
    const { auth } = firebaseModule;

    const currentUser = auth.currentUser;
    if (currentUser && currentUser.uid) {
      return {};  // Retornamos um objeto vazio, pois o UID será adicionado como query param
    }
  } catch (error) {
    console.error("Error getting auth user:", error);
  }

  return {};
}

export async function apiRequest(
  method: string,
  url: string,
  data?: unknown | undefined,
): Promise<Response> {
  console.log(`[apiRequest] Starting: ${method} ${url}`);

  // Build the full API URL
  const fullUrl = buildApiUrl(url);
  console.log(`[apiRequest] Full API URL: ${fullUrl}`);

  // Get auth headers and check if we need to add UID
  let finalUrl = fullUrl;
  let authHeaders = {};

  try {
    // Verificar se temos o Firebase inicializado e o usuário está logado
    const firebaseModule = await import('./firebase');
    const { auth } = firebaseModule;

    const currentUser = auth.currentUser;

    if (currentUser && currentUser.uid) {
      // Adicionar o UID como parâmetro de consulta
      const separator = finalUrl.includes('?') ? '&' : '?';
      finalUrl = `${finalUrl}${separator}uid=${currentUser.uid}`;
      console.log(`[apiRequest] Added UID to URL: ${finalUrl}`);

      // Tentar obter o token de autenticação
      try {
        const token = await currentUser.getIdToken();
        authHeaders = {
          'Authorization': `Bearer ${token}`
        };
        console.log(`[apiRequest] Auth token obtained successfully`);
      } catch (tokenError) {
        console.warn('[apiRequest] Could not get ID token:', tokenError);
      }
    } else {
      console.log(`[apiRequest] No authenticated user found`);
    }
  } catch (error) {
    console.error("Error getting auth user for request:", error);
  }

  // Prepare headers
  const headers: Record<string, string> = {
    ...(data ? { "Content-Type": "application/json" } : {}),
    ...authHeaders,
    // Desabilitar cache HTTP para garantir dados atualizados
    'Cache-Control': 'no-cache, no-store, must-revalidate',
    'Pragma': 'no-cache',
    'Expires': '0'
  };

  console.log(`[apiRequest] Making fetch request with headers:`, headers);

  const res = await fetch(finalUrl, {
    method,
    headers,
    body: data ? JSON.stringify(data) : undefined,
    credentials: "include",
  });

  console.log(`[apiRequest] Response received: ${res.status} ${res.statusText}`);

  await throwIfResNotOk(res);
  return res;
}

type UnauthorizedBehavior = "returnNull" | "throw";
export const getQueryFn: <T>(options: {
  on401: UnauthorizedBehavior;
}) => QueryFunction<T> =
  ({ on401: unauthorizedBehavior }) =>
  async ({ queryKey }) => {
    // Processar queryKey para lidar com parâmetros de rota
    let url = queryKey[0] as string;

    // Verificamos se há mais elementos no queryKey e o primeiro é uma string
    // que termina com um ID (ex: ['/api/products', 1])
    if (queryKey.length > 1 && typeof url === 'string' && typeof queryKey[1] !== 'undefined') {
      url = `${url}/${queryKey[1]}`;
    }

    console.log(`Query request: ${url}`);
    console.log(`Query key:`, queryKey);

    // Build the full API URL
    const fullUrl = buildApiUrl(url);
    console.log(`Full Query URL: ${fullUrl}`);

    // Adicionar o UID ao URL se o usuário estiver autenticado
    let finalUrl = fullUrl;
    try {
      // Verificar se temos o Firebase inicializado e o usuário está logado
      const firebaseModule = await import('./firebase');
      const { auth } = firebaseModule;

      const currentUser = auth.currentUser;
      if (currentUser && currentUser.uid) {
        // Adicionar o UID como parâmetro de consulta
        const separator = finalUrl.includes('?') ? '&' : '?';
        finalUrl = `${finalUrl}${separator}uid=${currentUser.uid}`;
        console.log('Added UID to query URL:', finalUrl);
      }
    } catch (error) {
      console.error("Error getting auth user for query:", error);
    }

    // Get auth headers if user is logged in
    const authHeaders = await getAuthHeaders();
    console.log('Query auth headers:', authHeaders);

    const res = await fetch(finalUrl, {
      credentials: "include",
      headers: {
        ...authHeaders,
        // Desabilitar cache HTTP para garantir dados atualizados
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      } as HeadersInit
    });

    console.log(`Query response: ${res.status} ${res.statusText}`);

    if (unauthorizedBehavior === "returnNull" && res.status === 401) {
      console.log('Returning null due to 401 status');
      return null;
    }

    await throwIfResNotOk(res);
    const data = await res.json();
    console.log('Query response data:', data);
    return data;
  };

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      queryFn: getQueryFn({ on401: "throw" }),
      refetchInterval: false,
      refetchOnWindowFocus: false,
      staleTime: 0, // Temporariamente removendo cache para testar
      retry: false,
    },
    mutations: {
      retry: false,
    },
  },
});
