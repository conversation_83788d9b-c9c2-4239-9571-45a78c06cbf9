/**
 * Utilitários para upload de arquivos usando nossa API local
 */

/**
 * Faz upload de uma imagem de produto para o servidor
 * @param file O arquivo de imagem a ser enviado
 * @param storeId ID da loja associada ao produto
 * @returns URL da imagem no servidor
 */
export const uploadProductImage = async (file: File, storeId: number): Promise<string> => {
  if (!file) {
    throw new Error('Nenhum arquivo fornecido');
  }
  
  try {
    const formData = new FormData();
    formData.append('image', file);
    formData.append('storeId', storeId.toString());
    
    // Verificar se temos o Firebase inicializado e o usuário está logado
    const { auth } = await import('./firebase');
    const currentUser = auth.currentUser;
    
    let url = '/api/products/upload-image';
    let headers = {};
    
    // Adicionar uid como parâmetro de consulta e token de autenticação
    if (currentUser) {
      if (currentUser.uid) {
        url = `${url}?uid=${currentUser.uid}`;
      }
      
      // Obter o token de autenticação e adicioná-lo ao cabeçalho
      try {
        const token = await currentUser.getIdToken();
        headers = {
          'Authorization': `Bearer ${token}`
        };

      } catch (error) {
        console.error('Erro ao obter token de autenticação:', error);
      }
    }
    
    const response = await fetch(url, {
      method: 'POST',
      body: formData,
      credentials: 'include',
      headers
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      console.error('Erro ao fazer upload da imagem:', errorData);
      throw new Error(errorData.error || 'Falha ao fazer upload da imagem');
    }
    
    const data = await response.json();
    return data.url;
  } catch (error) {
    console.error('Erro no upload da imagem:', error);
    throw error;
  }
};

/**
 * Faz upload de uma imagem de categoria para o servidor
 * @param file O arquivo de imagem a ser enviado
 * @param storeId ID da loja associada à categoria
 * @returns URL da imagem no servidor
 */
export const uploadCategoryLogo = async (file: File, storeId: number): Promise<string> => {
  if (!file) {
    throw new Error('Nenhum arquivo fornecido');
  }
  
  try {
    const formData = new FormData();
    formData.append('image', file);
    formData.append('storeId', storeId.toString());
    
    // Verificar se temos o Firebase inicializado e o usuário está logado
    const { auth } = await import('./firebase');
    const currentUser = auth.currentUser;
    
    let url = '/api/categories/upload-logo';
    let headers = {};
    
    // Adicionar uid como parâmetro de consulta e token de autenticação
    if (currentUser) {
      if (currentUser.uid) {
        url = `${url}?uid=${currentUser.uid}`;
      }
      
      // Obter o token de autenticação e adicioná-lo ao cabeçalho
      try {
        const token = await currentUser.getIdToken();
        headers = {
          'Authorization': `Bearer ${token}`
        };

      } catch (error) {
        console.error('Erro ao obter token de autenticação:', error);
      }
    }
    
    const response = await fetch(url, {
      method: 'POST',
      body: formData,
      credentials: 'include',
      headers
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      console.error('Erro ao fazer upload do logo:', errorData);
      throw new Error(errorData.error || 'Falha ao fazer upload do logo');
    }
    
    const data = await response.json();
    return data.url;
  } catch (error) {
    console.error('Erro no upload do logo:', error);
    throw error;
  }
};

/**
 * Apaga uma imagem de produto do servidor
 * @param imageUrl URL da imagem a ser apagada
 * @returns Sucesso da operação
 */
export const deleteProductImage = async (imageUrl: string): Promise<boolean> => {
  try {
    // Verificar se temos o Firebase inicializado e o usuário está logado
    const { auth } = await import('./firebase');
    const currentUser = auth.currentUser;
    
    // Vamos enviar a URL completa para o backend poder determinar o tipo de armazenamento (GCS ou local)
    let url = `/api/products/delete-image/${encodeURIComponent(imageUrl)}`;
    let headers = {};
    
    // Adicionar uid como parâmetro de consulta e token de autenticação
    if (currentUser) {
      if (currentUser.uid) {
        url = `${url}?uid=${currentUser.uid}`;
      }
      
      // Obter o token de autenticação e adicioná-lo ao cabeçalho
      try {
        const token = await currentUser.getIdToken();
        headers = {
          'Authorization': `Bearer ${token}`
        };

      } catch (error) {
        console.error('Erro ao obter token de autenticação:', error);
      }
    }
    
    const response = await fetch(url, {
      method: 'DELETE',
      headers,
      credentials: 'include'
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      console.error('Erro ao apagar imagem:', errorData);
      throw new Error(errorData.error || 'Falha ao apagar imagem');
    }
    
    return true;
  } catch (error) {
    console.error('Erro ao apagar imagem:', error);
    return false;
  }
};