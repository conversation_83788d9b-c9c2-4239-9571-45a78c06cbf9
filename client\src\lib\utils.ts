import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"
import { countryCodes } from "./countryCodes"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Format a number as currency with appropriate locale and currency
export function formatCurrency(value: number, currencySymbol = 'R$', currency = 'BRL'): string {
  // Ensure value is a number
  const numValue = Number(value) || 0;

  // Use the provided currency symbol directly for formatting
  try {
    // Format with 2 decimal places and use Brazilian number format (comma as decimal separator)
    const formattedValue = numValue.toFixed(2).replace('.', ',');

    // Return with the currency symbol
    return `${currencySymbol} ${formattedValue}`;
  } catch (e) {
    // Any error falls back to simple formatting
    console.error('Error in formatCurrency:', e);
    return `${currencySymbol} ${numValue.toFixed(2).replace('.', ',')}`;
  }
}

// Format currency using store settings
export function formatStoreCurrency(value: number, storeCurrency?: string): string {
  // Se a moeda da loja for definida, use-a; caso contrário, use o formato padrão
  if (storeCurrency) {
    // Usamos um formato simples com a moeda definida na loja
    return `${storeCurrency} ${value.toFixed(2)}`;
  }

  // Fallback para o formato padrão
  return formatCurrency(value);
}

// Format a number with a custom currency symbol
export function formatWithCurrencySymbol(value: number, currencySymbol: string = 'R$'): string {
  return `${currencySymbol} ${value.toFixed(2).replace('.', ',')}`;
}

// Formatar telefone com código do país
export function formatPhoneWithCountryCode(phone: string, countryCode?: string): string {
  if (!phone) return '';

  // Se já tiver um código de país (+xx), retornar como está
  if (phone.startsWith('+')) return phone;

  // Se tiver um código de país fornecido
  if (countryCode) {
    // Se o código já for no formato +xx, use-o diretamente
    if (countryCode.startsWith('+')) {
      return `${countryCode} ${phone}`;
    }

    // Converter o código do país (ex: 'br') para o formato internacional (+xx)
    const country = countryCodes.find(c => c.id === countryCode);
    if (country) {
      // Remove qualquer texto entre parênteses, incluindo os próprios parênteses
      const cleanCode = country.code.replace(/\s*\([^)]*\)\s*/g, '');
      return `${cleanCode} ${phone}`;
    }
  }

  // Retorna o telefone original se não houver código de país
  return phone;
}

// Format date to local date string
export function formatDate(date: string | Date): string {
  if (!date) return '';

  try {
    // Extrair data do formato ISO (YYYY-MM-DD)
    // Importante: ISO strings no formato 'YYYY-MM-DDT00:00:00.000Z' são armazenadas em UTC
    // e precisamos garantir que não ocorra nenhuma mudança de fuso horário
    const isoString = typeof date === 'string' ? date : date.toISOString();

    // Obter apenas a parte da data (YYYY-MM-DD)
    const datePart = isoString.split('T')[0];
    const [year, month, day] = datePart.split('-');

    // Formatar no padrão brasileiro (DD/MM/YYYY)
    const result = `${day}/${month}/${year}`;

    return result;
  } catch (error) {
    console.error('Error formatting date:', error);
    return '';
  }
}
