import { useState, useEffect } from 'react';
import { useQuery, useMutation } from '@tanstack/react-query';
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Plus, ArrowUp, ArrowDown, Save, AlertCircle, Eye, EyeOff } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Link } from 'wouter';
import { useTranslation } from '@/hooks/useTranslation';
import AdminSidebarLayout from '@/components/admin/AdminSidebarLayout';
import { apiRequest } from '@/lib/queryClient';
import { queryClient } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';

export default function CategoriesList() {
  const { t } = useTranslation();
  const { toast } = useToast();

  // Estado para controlar categorias ordenáveis
  const [orderedCategories, setOrderedCategories] = useState<any[]>([]);
  const [isReordering, setIsReordering] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  const { data: categories = [], isLoading: isLoadingCategories, error: categoriesError } = useQuery({
    queryKey: ['/api/categories']
  });

  // Atualiza o estado de ordenação quando os dados são carregados
  useEffect(() => {
    if (categories && categories.length > 0) {
      setOrderedCategories(categories);
    }
  }, [categories]);

  // Mutation para salvar a ordem das categorias
  const reorderMutation = useMutation({
    mutationFn: async () => {
      return apiRequest('POST', '/api/categories/reorder', { categories: orderedCategories });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/categories'] });
      setIsReordering(false);
      setHasChanges(false);
      toast({
        title: t('common.success'),
        description: t('admin.categoriesReordered')
      });
    },
    onError: (error) => {
      toast({
        variant: "destructive",
        title: t('common.error'),
        description: error instanceof Error ? error.message : String(error)
      });
    }
  });

  // Mutation para atualizar visibilidade de categoria
  const toggleVisibilityMutation = useMutation({
    mutationFn: async ({ id, visible, index }: { id: number, visible: boolean, index: number }) => {
      // Update local state immediately for better UX
      const newCategories = [...orderedCategories];
      newCategories[index] = { ...newCategories[index], visible };
      setOrderedCategories(newCategories);

      return apiRequest('PATCH', `/api/categories/${id}`, { visible });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/categories'] });
      toast({
        title: t('common.success'),
        description: t('admin.visibilityUpdated')
      });
    },
    onError: (error, variables) => {
      // Revert the local state change on error
      const { id, visible, index } = variables;
      const newCategories = [...orderedCategories];
      newCategories[index] = { ...newCategories[index], visible: !visible };
      setOrderedCategories(newCategories);

      toast({
        variant: "destructive",
        title: t('common.error'),
        description: error instanceof Error ? error.message : String(error)
      });
    }
  });

  // Função para mover uma categoria para cima na ordem
  const moveUp = (index: number) => {
    if (index === 0) return; // Já está no topo

    const newCategories = [...orderedCategories];
    const temp = newCategories[index];
    newCategories[index] = newCategories[index - 1];
    newCategories[index - 1] = temp;

    setOrderedCategories(newCategories);
    setHasChanges(true);
  };

  // Função para mover uma categoria para baixo na ordem
  const moveDown = (index: number) => {
    if (index === orderedCategories.length - 1) return; // Já está no final

    const newCategories = [...orderedCategories];
    const temp = newCategories[index];
    newCategories[index] = newCategories[index + 1];
    newCategories[index + 1] = temp;

    setOrderedCategories(newCategories);
    setHasChanges(true);
  };

  // Função para salvar a ordem das categorias
  const saveOrder = () => {
    reorderMutation.mutate();
  };

  return (
    <AdminSidebarLayout title={t('admin.categories')}>
      <div className="container space-y-6 px-0">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            <Button
              variant={isReordering ? "secondary" : "outline"}
              onClick={() => setIsReordering(!isReordering)}
              disabled={categories.length < 2}
            >
              {isReordering ? t('common.cancel') : t('admin.reorderCategories')}
            </Button>

            {isReordering && hasChanges && (
              <Button
                variant="default"
                onClick={saveOrder}
                disabled={reorderMutation.isPending}
              >
                {reorderMutation.isPending ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                ) : (
                  <Save className="h-4 w-4 mr-2" />
                )}
                {t('common.save')}
              </Button>
            )}
          </div>

          <Link href="/admin/categories/new">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              {t('admin.newCategory')}
            </Button>
          </Link>
        </div>

      {categoriesError && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>{t('common.error')}</AlertTitle>
          <AlertDescription>
            {categoriesError instanceof Error ? categoriesError.message : String(categoriesError)}
          </AlertDescription>
        </Alert>
      )}

      <div className="flex flex-col gap-4">
        {isLoadingCategories ? (
          <Card>
            <CardContent className="pt-6">
              <div className="h-28 flex items-center justify-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            </CardContent>
          </Card>
        ) : orderedCategories.length === 0 ? (
          <Card>
            <CardContent className="pt-6">
              <div className="h-28 flex items-center justify-center text-center text-muted-foreground">
                {t('admin.noCategories')}
              </div>
            </CardContent>
          </Card>
        ) : (
          orderedCategories.map((category: any, index: number) => (
            <Card key={category.id} className={`overflow-hidden ${isReordering ? 'border-2 border-dashed border-gray-200 hover:border-primary' : ''}`}>
              <CardContent className="p-0">
                <div className="flex flex-row">
                  {/* Logo à esquerda */}
                  {category.logo && (
                    <div className="w-24 h-24 bg-muted flex-shrink-0">
                      <img
                        src={category.logo}
                        alt={category.name}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          (e.target as HTMLImageElement).style.display = 'none';
                        }}
                      />
                    </div>
                  )}

                  {/* Informações ao centro */}
                  <div className="p-4 flex-grow">
                    <h3 className="font-medium text-lg">{category.name}</h3>
                    <p className="text-sm text-muted-foreground mt-1">
                      {category.description || t('admin.noDescription')}
                    </p>
                    {isReordering && (
                      <p className="text-xs text-muted-foreground mt-2">
                        {t('admin.positionNumber', { position: index + 1 })}
                      </p>
                    )}
                  </div>

                  {/* Botões à direita */}
                  <div className="p-4 flex items-center gap-2">
                    {isReordering ? (
                      <div className="flex flex-col gap-2">
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() => moveUp(index)}
                          disabled={index === 0}
                        >
                          <ArrowUp className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() => moveDown(index)}
                          disabled={index === orderedCategories.length - 1}
                        >
                          <ArrowDown className="h-4 w-4" />
                        </Button>
                      </div>
                    ) : (
                      <>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => toggleVisibilityMutation.mutate({
                            id: category.id,
                            visible: !category.visible,
                            index
                          })}
                          title={category.visible ? t('admin.visible') : t('admin.hidden')}
                          disabled={toggleVisibilityMutation.isPending}
                          className={category.visible ? "text-green-500 hover:text-green-600" : "text-gray-400 hover:text-gray-500"}
                        >
                          {toggleVisibilityMutation.isPending ? (
                            <div className="animate-spin h-4 w-4 border-b-2 border-current"></div>
                          ) : category.visible ? (
                            <Eye className="h-4 w-4" />
                          ) : (
                            <EyeOff className="h-4 w-4" />
                          )}
                        </Button>

                        <Link href={`/admin/categories/${category.id}/edit`}>
                          <Button variant="outline" size="sm">
                            {t('common.edit')}
                          </Button>
                        </Link>
                      </>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
      </div>
    </AdminSidebarLayout>
  );
}