import { useEffect, useState } from 'react';
import { useLocation } from 'wouter';
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import AdminSidebarLayout from '@/components/admin/AdminSidebarLayout';
import { useTranslation } from '@/hooks/useTranslation';
import { useStore } from '@/context/StoreContext';
import { apiRequest } from "@/lib/queryClient";
import { countryCodes } from "@/lib/countryCodes";
import { useToast } from "@/hooks/use-toast";
import { formatDate, formatPhoneWithCountryCode } from "@/lib/utils";
import { formatCurrency } from "@/lib/utils";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  User, Mail, Phone, CalendarClock, ShoppingBag,
  Calendar, CreditCard, Check, ArrowLeft, ExternalLink
} from "lucide-react";

// Schema de validação para edição de cliente
const customerFormSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters" }),
  email: z.string().email({ message: "Please enter a valid email address" }).optional().or(z.literal("")),
  countryCode: z.string().default("+55"),
  phone: z.string().optional().or(z.literal(""))
});

type CustomerFormValues = z.infer<typeof customerFormSchema>;

// Order status badge component
const OrderStatusBadge = ({ status }: { status: string }) => {
  const { t } = useTranslation();

  const getVariant = () => {
    switch (status) {
      case 'pending':
        return "warning";
      case 'processing':
        return "info";
      case 'shipped':
        return "default";
      case 'completed':
        return "success";
      case 'cancelled':
        return "destructive";
      default:
        return "default";
    }
  };

  return (
    <Badge variant={getVariant() as any}>
      {t(`dashboard.${status}`)}
    </Badge>
  );
};

export default function CustomerDetailPage({ id }: { id: number }) {
  const { t } = useTranslation();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [, setLocation] = useLocation();
  const { store, isLoading: isStoreLoading } = useStore();
  const [activeTab, setActiveTab] = useState("details");

  // Form for editing customer
  const form = useForm<CustomerFormValues>({
    resolver: zodResolver(customerFormSchema),
    defaultValues: {
      name: "",
      email: "",
      countryCode: "+55", // Padrão para Brasil
      phone: ""
    }
  });

  // Redirect to settings if no store exists
  useEffect(() => {
    if (!isStoreLoading && !store) {
      setLocation('/admin/settings');
    }
  }, [store, isStoreLoading, setLocation]);

  // Fetch customer
  const { data: customer, isLoading: isCustomerLoading } = useQuery({
    queryKey: ['/api/customers', id],
    queryFn: async () => {
      const response = await apiRequest('GET', `/api/customers/${id}`);
      return response.json();
    },
  });

  // Fetch customer orders
  const { data: customerOrders, isLoading: isOrdersLoading } = useQuery({
    queryKey: ['/api/customers', id, 'orders'],
    queryFn: async () => {
      const response = await apiRequest('GET', `/api/customers/${id}/orders`);
      return response.json();
    },
  });

  // Update form values when customer data is loaded
  useEffect(() => {
    if (customer) {
      form.reset({
        name: customer.name,
        email: customer.email || "",
        countryCode: customer.countryCode || "+55",
        phone: customer.phone || ""
      });
    }
  }, [customer, form]);

  // Mutation to update customer
  const updateCustomerMutation = useMutation({
    mutationFn: async (values: CustomerFormValues) => {
      const response = await apiRequest(
        'PATCH',
        `/api/customers/${id}`,
        values
      );
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: t('customers.updateSuccess'),
        description: t('customers.updateSuccessMessage')
      });
      queryClient.invalidateQueries({ queryKey: ['/api/customers', id] });
      queryClient.invalidateQueries({ queryKey: ['/api/customers'] });
      setActiveTab("details");
    },
    onError: () => {
      toast({
        title: t('customers.updateError'),
        description: t('customers.updateErrorMessage'),
        variant: "destructive"
      });
    }
  });

  // Handle form submission
  const onSubmit = (values: CustomerFormValues) => {
    updateCustomerMutation.mutate(values);
  };

  // Format WhatsApp number for link
  const getWhatsAppLink = () => {
    if (!customer?.phone) return null;

    const countryCode = customer.countryCode || "+55";
    const phoneNumber = customer.phone.replace(/\D/g, '');
    const formattedNumber = `${countryCode.replace('+', '')}${phoneNumber}`;

    return `https://wa.me/${formattedNumber}`;
  };

  return (
    <AdminSidebarLayout title={t('customers.customerDetails')}>
      <div className="mb-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => window.history.back()}
          className="flex items-center"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          {t('common.back')}
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="details">{t('customers.details')}</TabsTrigger>
          <TabsTrigger value="edit">{t('common.edit')}</TabsTrigger>
          <TabsTrigger value="orders">{t('customers.orders')}</TabsTrigger>
        </TabsList>

        <TabsContent value="details">
          <Card>
            <CardHeader>
              <CardTitle>{isCustomerLoading ? <Skeleton className="h-8 w-48" /> : customer?.name}</CardTitle>
              <CardDescription>
                {t('customers.customerSince', { date: customer ? formatDate(customer.createdAt) : '...' })}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isCustomerLoading ? (
                <div className="space-y-4">
                  <Skeleton className="h-6 w-full" />
                  <Skeleton className="h-6 w-full" />
                  <Skeleton className="h-6 w-full" />
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="flex items-start">
                    <User className="mr-3 h-5 w-5 text-muted-foreground mt-0.5" />
                    <div>
                      <div className="font-medium">{t('customers.name')}</div>
                      <div className="text-muted-foreground">{customer?.name}</div>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <Mail className="mr-3 h-5 w-5 text-muted-foreground mt-0.5" />
                    <div>
                      <div className="font-medium">{t('customers.email')}</div>
                      <div className="text-muted-foreground">{customer?.email || '-'}</div>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <Phone className="mr-3 h-5 w-5 text-muted-foreground mt-0.5" />
                    <div>
                      <div className="font-medium">{t('customers.phone')}</div>
                      <div className="text-muted-foreground">
                        {customer?.phone ?
                          (customer.countryCode ?
                            `${customer.countryCode} ${customer.phone}` :
                            formatPhoneWithCountryCode(customer.phone, "br")
                          ) :
                          '-'
                        }
                      </div>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <CalendarClock className="mr-3 h-5 w-5 text-muted-foreground mt-0.5" />
                    <div>
                      <div className="font-medium">{t('customers.createdAt')}</div>
                      <div className="text-muted-foreground">{formatDate(customer?.createdAt)}</div>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button
                variant="outline"
                onClick={() => setActiveTab("edit")}
              >
                {t('common.edit')}
              </Button>

              {customer?.phone && (
                <Button
                  variant="default"
                  className="bg-green-600 hover:bg-green-700"
                  onClick={() => window.open(getWhatsAppLink() || '', '_blank')}
                >
                  <ExternalLink className="mr-2 h-4 w-4" />
                  {t('customers.contactViaWhatsApp')}
                </Button>
              )}
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="edit">
          <Card>
            <CardHeader>
              <CardTitle>{t('customers.editCustomer')}</CardTitle>
              <CardDescription>{t('customers.editCustomerDescription')}</CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('customers.name')}</FormLabel>
                        <FormControl>
                          <Input placeholder={t('customers.namePlaceholder')} {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('customers.email')}</FormLabel>
                        <FormControl>
                          <Input placeholder={t('customers.emailPlaceholder')} {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <FormField
                      control={form.control}
                      name="countryCode"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t('customers.countryCode')}</FormLabel>
                          <Select
                            value={field.value}
                            onValueChange={field.onChange}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Selecione o código do país" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {countryCodes.map((country) => (
                                <SelectItem key={country.id} value={country.code}>
                                  <div className="flex items-center">
                                    <span className="mr-2">{country.flag}</span>
                                    <span>{country.name}</span>
                                    <span className="ml-1 text-muted-foreground">{country.code}</span>
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="phone"
                      render={({ field }) => (
                        <FormItem className="md:col-span-2">
                          <FormLabel>{t('customers.phone')}</FormLabel>
                          <FormControl>
                            <Input placeholder={t('customers.phonePlaceholder')} {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="flex justify-end space-x-2 pt-4">
                    <Button
                      variant="outline"
                      type="button"
                      onClick={() => setActiveTab("details")}
                    >
                      {t('common.cancel')}
                    </Button>
                    <Button
                      type="submit"
                      disabled={updateCustomerMutation.isPending}
                    >
                      {updateCustomerMutation.isPending ? (
                        <span className="flex items-center">
                          <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          {t('common.saving')}
                        </span>
                      ) : (
                        <span className="flex items-center">
                          <Check className="mr-2 h-4 w-4" />
                          {t('common.save')}
                        </span>
                      )}
                    </Button>
                  </div>
                </form>
              </Form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="orders">
          <Card>
            <CardHeader>
              <CardTitle>{t('customers.orders')}</CardTitle>
              <CardDescription>
                {isOrdersLoading
                  ? t('common.loading')
                  : t('customers.ordersCount', { count: customerOrders?.length || 0 })}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isOrdersLoading ? (
                <div className="space-y-4">
                  <Skeleton className="h-24 w-full" />
                  <Skeleton className="h-24 w-full" />
                </div>
              ) : customerOrders?.length > 0 ? (
                <div className="space-y-4">
                  {customerOrders.map((order: any) => (
                    <div key={order.id} className="border rounded-md p-4 hover:bg-neutral-50">
                      <div className="flex justify-between items-center mb-3">
                        <div className="flex items-center">
                          <div className="font-medium">#{order.id}</div>
                          <div className="ml-3">
                            <OrderStatusBadge status={order.status} />
                          </div>
                        </div>
                        <div className="font-medium text-primary">
                          {formatCurrency(order.total)}
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-2 text-sm text-muted-foreground mb-3">
                        <div className="flex items-center">
                          <Calendar className="h-3 w-3 mr-1" />
                          {formatDate(order.createdAt)}
                        </div>
                        <div className="flex items-center">
                          <CreditCard className="h-3 w-3 mr-1" />
                          {order.paymentMethod}
                        </div>
                      </div>

                      {/* Order items summary */}
                      <div className="text-sm">
                        <div className="font-medium">{t('orders.items')}:</div>
                        <ul className="list-disc list-inside text-muted-foreground">
                          {order.items?.slice(0, 2).map((item: any) => (
                            <li key={item.id} className="truncate">
                              {item.quantity}x {item.product?.name} ({formatCurrency(item.price)})
                            </li>
                          ))}
                          {order.items?.length > 2 && (
                            <li className="text-muted-foreground">
                              {t('common.andMore', { count: order.items.length - 2 })}
                            </li>
                          )}
                        </ul>
                      </div>

                      <div className="mt-3 flex justify-end">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            // Armazenar a referência no localStorage
                            localStorage.setItem('orderReferer', 'customer');
                            localStorage.setItem('orderRefererId', id.toString());
                            setLocation(`/admin/orders/${order.id}`);
                          }}
                        >
                          {t('orders.viewDetails')}
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  {t('customers.noOrders')}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </AdminSidebarLayout>
  );
}
