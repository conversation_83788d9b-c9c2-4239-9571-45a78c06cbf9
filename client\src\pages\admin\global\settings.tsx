import { useState, useEffect, useRef } from 'react';
import { useLocation } from 'wouter';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import GlobalAdminLayout from '@/components/global-admin/GlobalAdminLayout';
import { useGlobalAdminGuard } from '@/hooks/useGlobalAdmin';
import { useTranslation } from '@/hooks/useTranslation';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import { Switch } from '@/components/ui/switch';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Settings, Save, RotateCcw, AlertTriangle, FileText, BarChart3, MessageCircle, Tag, Palette, DollarSign } from 'lucide-react';

// Schema de validação para configurações de limites
const planLimitsSchema = z.object({
  // Limitações numéricas
  freeMaxProducts: z.number().min(1, 'Deve ser pelo menos 1').max(1000, 'Máximo 1000'),
  freeMaxOrdersPerMonth: z.number().min(-1, 'Use -1 para ilimitado').max(1000, 'Máximo 1000'),
  premiumMaxProducts: z.number().min(1, 'Deve ser pelo menos 1').max(10000, 'Máximo 10000'),
  premiumMaxOrdersPerMonth: z.number().min(-1, 'Use -1 para ilimitado').max(10000, 'Máximo 10000'),

  // Funcionalidades do plano gratuito
  freeAllowPdfGeneration: z.boolean(),
  freeAllowAnalytics: z.boolean(),
  freeAllowWhatsappIntegration: z.boolean(),
  freeAllowCoupons: z.boolean(),
  freeAllowCustomization: z.boolean(),

  // Funcionalidades do plano premium
  premiumAllowPdfGeneration: z.boolean(),
  premiumAllowAnalytics: z.boolean(),
  premiumAllowWhatsappIntegration: z.boolean(),
  premiumAllowCoupons: z.boolean(),
  premiumAllowCustomization: z.boolean(),
}).refine((data) => {
  // Validar que limitações do premium sejam >= gratuito (exceto quando -1)
  const freeProducts = data.freeMaxProducts;
  const premiumProducts = data.premiumMaxProducts;
  const freeOrders = data.freeMaxOrdersPerMonth;
  const premiumOrders = data.premiumMaxOrdersPerMonth;

  if (premiumProducts !== -1 && freeProducts > premiumProducts) {
    return false;
  }

  if (premiumOrders !== -1 && freeOrders !== -1 && freeOrders > premiumOrders) {
    return false;
  }

  return true;
}, {
  message: "Limitações do plano premium devem ser maiores ou iguais ao plano gratuito",
  path: ["premiumMaxProducts"]
});

type PlanLimitsFormData = z.infer<typeof planLimitsSchema>;

// Schema de validação para configurações de preços
const planPricingSchema = z.object({
  premiumMonthlyPrice: z.number().min(0.01, 'Preço deve ser maior que R$ 0,01').max(999.99, 'Preço máximo R$ 999,99').optional(),
  premiumYearlyPrice: z.number().min(0.01, 'Preço deve ser maior que R$ 0,01').max(9999.99, 'Preço máximo R$ 9.999,99').optional(),
}).refine((data) => {
  // Validar que ambos os campos estão preenchidos
  if (!data.premiumMonthlyPrice || !data.premiumYearlyPrice) {
    return false;
  }
  // Validar que o preço anual seja menor que 12x o preço mensal (para manter desconto)
  const monthlyTotal = data.premiumMonthlyPrice * 12;
  return data.premiumYearlyPrice < monthlyTotal;
}, {
  message: "Ambos os preços devem ser preenchidos e o preço anual deve ser menor que 12x o preço mensal",
  path: ["premiumYearlyPrice"]
});

type PlanPricingFormData = z.infer<typeof planPricingSchema>;

interface GlobalSetting {
  id: number;
  key: string;
  value: any;
  description?: string;
  updatedAt: string;
  updatedBy?: number;
}

export default function GlobalSettings() {
  const [, setLocation] = useLocation();
  const [showRestoreDialog, setShowRestoreDialog] = useState(false);
  const { t } = useTranslation();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { data: adminUser, isLoading: isLoadingUser, error: userError } = useGlobalAdminGuard();

  // Redirecionar se não for admin global
  useEffect(() => {
    if (!isLoadingUser && (!adminUser || !adminUser.isGlobalAdmin)) {
      setLocation('/admin');
    }
  }, [adminUser, isLoadingUser, setLocation]);

  // Buscar configurações globais
  const { data: settingsResponse, isLoading: isLoadingSettings, error: settingsError } = useQuery<{settings: GlobalSetting[]}>({
    queryKey: ['/api/admin/global/settings'],
    enabled: !!adminUser?.isGlobalAdmin,
  });

  const settings = settingsResponse?.settings || [];

  // Encontrar configuração de limites de planos
  const planLimitsSetting = settings.find(setting => setting.key === 'plan_limits');
  const planLimitsValue = planLimitsSetting?.value || {
    // Limitações numéricas
    freeMaxProducts: 10,
    freeMaxOrdersPerMonth: -1,
    premiumMaxProducts: 50,
    premiumMaxOrdersPerMonth: -1,

    // Funcionalidades do plano gratuito
    freeAllowPdfGeneration: false,
    freeAllowAnalytics: false,
    freeAllowWhatsappIntegration: false,
    freeAllowCoupons: false,
    freeAllowCustomization: false,

    // Funcionalidades do plano premium
    premiumAllowPdfGeneration: true,
    premiumAllowAnalytics: true,
    premiumAllowWhatsappIntegration: true,
    premiumAllowCoupons: true,
    premiumAllowCustomization: true,
  };

  // Encontrar configuração de preços de planos
  const planPricingSetting = settings.find(setting => setting.key === 'plan_pricing');

  const planPricingValue = planPricingSetting?.value ? {
    premiumMonthlyPrice: Number(planPricingSetting.value.premiumMonthlyPrice) || 29.90,
    premiumYearlyPrice: Number(planPricingSetting.value.premiumYearlyPrice) || 299.00,
  } : {
    premiumMonthlyPrice: 29.90,
    premiumYearlyPrice: 299.00,
  };



  // Configurar formulário para limites
  const form = useForm<PlanLimitsFormData>({
    resolver: zodResolver(planLimitsSchema),
    defaultValues: planLimitsValue,
  });

  // Configurar formulário para preços
  const pricingForm = useForm<PlanPricingFormData>({
    resolver: zodResolver(planPricingSchema),
    defaultValues: planPricingValue,
    mode: 'onChange',
  });

  // Atualizar valores do formulário quando os dados chegarem
  useEffect(() => {
    if (planLimitsValue) {
      form.reset(planLimitsValue);
    }
  }, [planLimitsValue, form]);

  // Usar useRef para controlar se já foi inicializado
  const pricingFormInitialized = useRef(false);

  useEffect(() => {
    if (planPricingValue && !isLoadingSettings && !pricingFormInitialized.current) {
      pricingForm.reset({
        premiumMonthlyPrice: planPricingValue.premiumMonthlyPrice,
        premiumYearlyPrice: planPricingValue.premiumYearlyPrice,
      });
      pricingFormInitialized.current = true;
    }
  }, [planPricingValue, pricingForm, isLoadingSettings]);

  // Mutation para atualizar configurações
  const updateSettingMutation = useMutation({
    mutationFn: async (data: { key: string; value: any }) => {
      console.log('Mutation data:', data);
      const response = await apiRequest('PUT', `/api/admin/global/settings/${data.key}`, {
        value: data.value
      });
      console.log('Mutation response:', response);
      return response;
    },
    onSuccess: (data) => {
      console.log('Mutation success:', data);
      toast({
        title: 'Configurações atualizadas',
        description: 'As configurações foram salvas com sucesso.',
      });
      queryClient.invalidateQueries({ queryKey: ['/api/admin/global/settings'] });
    },
    onError: (error: any) => {
      console.error('Mutation error:', error);
      toast({
        title: 'Erro ao salvar',
        description: error.message || 'Erro ao atualizar configurações.',
        variant: 'destructive',
      });
    },
  });

  // Mutation para restaurar configurações padrão
  const restoreDefaultsMutation = useMutation({
    mutationFn: async () => {
      return apiRequest('POST', '/api/admin/global/settings/restore-defaults');
    },
    onSuccess: () => {
      toast({
        title: 'Configurações restauradas',
        description: 'As configurações padrão foram restauradas com sucesso.',
      });
      queryClient.invalidateQueries({ queryKey: ['/api/admin/global/settings'] });
      setShowRestoreDialog(false);
    },
    onError: (error: any) => {
      toast({
        title: 'Erro ao restaurar',
        description: error.message || 'Erro ao restaurar configurações padrão.',
        variant: 'destructive',
      });
    },
  });

  // Mostrar loading enquanto verifica permissões
  if (isLoadingUser) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Settings className="h-12 w-12 text-blue-500 mx-auto mb-4" />
          <p className="text-gray-600">{t('common.loading')}</p>
        </div>
      </div>
    );
  }

  // Mostrar erro se não conseguir verificar usuário
  if (userError || !adminUser?.isGlobalAdmin) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Alert className="max-w-md">
          <AlertDescription>
            Acesso negado. Permissões de super-administrador necessárias.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  const handleSubmit = (data: PlanLimitsFormData) => {
    console.log('Submitting form data:', data);
    updateSettingMutation.mutate({
      key: 'plan_limits',
      value: data,
    });
  };

  const handlePricingSubmit = (data: PlanPricingFormData) => {
    console.log('Submitting pricing data:', data);
    updateSettingMutation.mutate({
      key: 'plan_pricing',
      value: data,
    });
  };

  const handleReset = () => {
    form.reset(planLimitsValue);
  };

  const handlePricingReset = () => {
    pricingForm.reset(planPricingValue);
    pricingFormInitialized.current = true; // Manter como inicializado após reset manual
  };

  const handleRestoreDefaults = () => {
    setShowRestoreDialog(true);
  };

  const confirmRestoreDefaults = () => {
    restoreDefaultsMutation.mutate();
  };

  return (
    <GlobalAdminLayout
      title="Configurações Globais"
      description="Gerencie configurações do sistema e limites de planos"
    >
      {/* Card de Limites de Planos */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Settings className="h-5 w-5" />
            <span>Limites de Planos</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isLoadingSettings ? (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {[...Array(4)].map((_, i) => (
                  <div key={i} className="space-y-2">
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-10 w-full" />
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-8">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Plano Gratuito */}
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">Plano Gratuito</h3>

                  {/* Limitações Numéricas */}
                  <div className="space-y-4">
                    <h4 className="text-md font-medium text-gray-700">Limitações Numéricas</h4>

                    <div className="space-y-2">
                      <Label htmlFor="freeMaxProducts">Máximo de Produtos</Label>
                      <Input
                        id="freeMaxProducts"
                        type="number"
                        min="1"
                        max="1000"
                        {...form.register('freeMaxProducts', { valueAsNumber: true })}
                      />
                      {form.formState.errors.freeMaxProducts && (
                        <p className="text-sm text-red-600">
                          {form.formState.errors.freeMaxProducts.message}
                        </p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="freeMaxOrdersPerMonth">Máximo de Pedidos por Mês</Label>
                      <Input
                        id="freeMaxOrdersPerMonth"
                        type="number"
                        min="-1"
                        max="1000"
                        placeholder="-1 para ilimitado"
                        {...form.register('freeMaxOrdersPerMonth', { valueAsNumber: true })}
                      />
                      {form.formState.errors.freeMaxOrdersPerMonth && (
                        <p className="text-sm text-red-600">
                          {form.formState.errors.freeMaxOrdersPerMonth.message}
                        </p>
                      )}
                      <p className="text-sm text-gray-500">
                        Use -1 para pedidos ilimitados
                      </p>
                    </div>
                  </div>

                  {/* Funcionalidades */}
                  <div className="space-y-4">
                    <h4 className="text-md font-medium text-gray-700">Funcionalidades</h4>

                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <FileText className="h-4 w-4 text-gray-500" />
                          <Label htmlFor="freeAllowPdfGeneration">Geração de PDFs</Label>
                        </div>
                        <Switch
                          id="freeAllowPdfGeneration"
                          checked={form.watch('freeAllowPdfGeneration')}
                          onCheckedChange={(checked) => form.setValue('freeAllowPdfGeneration', checked)}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <BarChart3 className="h-4 w-4 text-gray-500" />
                          <Label htmlFor="freeAllowAnalytics">Analytics</Label>
                        </div>
                        <Switch
                          id="freeAllowAnalytics"
                          checked={form.watch('freeAllowAnalytics')}
                          onCheckedChange={(checked) => form.setValue('freeAllowAnalytics', checked)}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <MessageCircle className="h-4 w-4 text-gray-500" />
                          <Label htmlFor="freeAllowWhatsappIntegration">Integração WhatsApp</Label>
                        </div>
                        <Switch
                          id="freeAllowWhatsappIntegration"
                          checked={form.watch('freeAllowWhatsappIntegration')}
                          onCheckedChange={(checked) => form.setValue('freeAllowWhatsappIntegration', checked)}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Tag className="h-4 w-4 text-gray-500" />
                          <Label htmlFor="freeAllowCoupons">Sistema de Cupons</Label>
                        </div>
                        <Switch
                          id="freeAllowCoupons"
                          checked={form.watch('freeAllowCoupons')}
                          onCheckedChange={(checked) => form.setValue('freeAllowCoupons', checked)}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Palette className="h-4 w-4 text-gray-500" />
                          <Label htmlFor="freeAllowCustomization">Customização</Label>
                        </div>
                        <Switch
                          id="freeAllowCustomization"
                          checked={form.watch('freeAllowCustomization')}
                          onCheckedChange={(checked) => form.setValue('freeAllowCustomization', checked)}
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Plano Premium */}
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">Plano Premium</h3>

                  {/* Limitações Numéricas */}
                  <div className="space-y-4">
                    <h4 className="text-md font-medium text-gray-700">Limitações Numéricas</h4>

                    <div className="space-y-2">
                      <Label htmlFor="premiumMaxProducts">Máximo de Produtos</Label>
                      <Input
                        id="premiumMaxProducts"
                        type="number"
                        min="1"
                        max="10000"
                        {...form.register('premiumMaxProducts', { valueAsNumber: true })}
                      />
                      {form.formState.errors.premiumMaxProducts && (
                        <p className="text-sm text-red-600">
                          {form.formState.errors.premiumMaxProducts.message}
                        </p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="premiumMaxOrdersPerMonth">Máximo de Pedidos por Mês</Label>
                      <Input
                        id="premiumMaxOrdersPerMonth"
                        type="number"
                        min="-1"
                        max="10000"
                        placeholder="-1 para ilimitado"
                        {...form.register('premiumMaxOrdersPerMonth', { valueAsNumber: true })}
                      />
                      {form.formState.errors.premiumMaxOrdersPerMonth && (
                        <p className="text-sm text-red-600">
                          {form.formState.errors.premiumMaxOrdersPerMonth.message}
                        </p>
                      )}
                      <p className="text-sm text-gray-500">
                        Use -1 para pedidos ilimitados
                      </p>
                    </div>
                  </div>

                  {/* Funcionalidades */}
                  <div className="space-y-4">
                    <h4 className="text-md font-medium text-gray-700">Funcionalidades</h4>

                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <FileText className="h-4 w-4 text-gray-500" />
                          <Label htmlFor="premiumAllowPdfGeneration">Geração de PDFs</Label>
                        </div>
                        <Switch
                          id="premiumAllowPdfGeneration"
                          checked={form.watch('premiumAllowPdfGeneration')}
                          onCheckedChange={(checked) => form.setValue('premiumAllowPdfGeneration', checked)}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <BarChart3 className="h-4 w-4 text-gray-500" />
                          <Label htmlFor="premiumAllowAnalytics">Analytics</Label>
                        </div>
                        <Switch
                          id="premiumAllowAnalytics"
                          checked={form.watch('premiumAllowAnalytics')}
                          onCheckedChange={(checked) => form.setValue('premiumAllowAnalytics', checked)}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <MessageCircle className="h-4 w-4 text-gray-500" />
                          <Label htmlFor="premiumAllowWhatsappIntegration">Integração WhatsApp</Label>
                        </div>
                        <Switch
                          id="premiumAllowWhatsappIntegration"
                          checked={form.watch('premiumAllowWhatsappIntegration')}
                          onCheckedChange={(checked) => form.setValue('premiumAllowWhatsappIntegration', checked)}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Tag className="h-4 w-4 text-gray-500" />
                          <Label htmlFor="premiumAllowCoupons">Sistema de Cupons</Label>
                        </div>
                        <Switch
                          id="premiumAllowCoupons"
                          checked={form.watch('premiumAllowCoupons')}
                          onCheckedChange={(checked) => form.setValue('premiumAllowCoupons', checked)}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Palette className="h-4 w-4 text-gray-500" />
                          <Label htmlFor="premiumAllowCustomization">Customização</Label>
                        </div>
                        <Switch
                          id="premiumAllowCustomization"
                          checked={form.watch('premiumAllowCustomization')}
                          onCheckedChange={(checked) => form.setValue('premiumAllowCustomization', checked)}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Botões de ação */}
              <div className="flex items-center justify-between pt-6 border-t">
                <Dialog open={showRestoreDialog} onOpenChange={setShowRestoreDialog}>
                  <DialogTrigger asChild>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleRestoreDefaults}
                      disabled={updateSettingMutation.isPending || restoreDefaultsMutation.isPending}
                    >
                      <RotateCcw className="h-4 w-4 mr-2" />
                      Restaurar Padrões
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle className="flex items-center space-x-2">
                        <AlertTriangle className="h-5 w-5 text-amber-500" />
                        <span>Confirmar Restauração</span>
                      </DialogTitle>
                      <DialogDescription>
                        Esta ação irá restaurar todas as configurações de planos para os valores padrão do sistema.
                        Todas as configurações personalizadas serão perdidas. Esta ação não pode ser desfeita.
                      </DialogDescription>
                    </DialogHeader>
                    <DialogFooter>
                      <Button
                        variant="outline"
                        onClick={() => setShowRestoreDialog(false)}
                        disabled={restoreDefaultsMutation.isPending}
                      >
                        Cancelar
                      </Button>
                      <Button
                        variant="destructive"
                        onClick={confirmRestoreDefaults}
                        disabled={restoreDefaultsMutation.isPending}
                      >
                        {restoreDefaultsMutation.isPending ? 'Restaurando...' : 'Confirmar Restauração'}
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>

                <div className="flex items-center space-x-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleReset}
                    disabled={updateSettingMutation.isPending || restoreDefaultsMutation.isPending}
                  >
                    <RotateCcw className="h-4 w-4 mr-2" />
                    Resetar Formulário
                  </Button>
                  <Button
                    type="submit"
                    disabled={updateSettingMutation.isPending || restoreDefaultsMutation.isPending}
                  >
                    <Save className="h-4 w-4 mr-2" />
                    {updateSettingMutation.isPending ? 'Salvando...' : 'Salvar Configurações'}
                  </Button>
                </div>
              </div>
            </form>
          )}

          {settingsError && (
            <Alert className="mt-6">
              <AlertDescription>
                Erro ao carregar configurações: {settingsError.message}
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Card de Configuração de Preços */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <DollarSign className="h-5 w-5" />
            <span>Configuração de Preços de Assinatura</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isLoadingSettings ? (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {[...Array(2)].map((_, i) => (
                  <div key={i} className="space-y-2">
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-10 w-full" />
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <form onSubmit={pricingForm.handleSubmit(handlePricingSubmit)} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Preço Mensal */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">Plano Premium Mensal</h3>

                  <div className="space-y-2">
                    <Label htmlFor="premiumMonthlyPrice">Preço Mensal (R$)</Label>
                    <Input
                      id="premiumMonthlyPrice"
                      type="number"
                      step="0.01"
                      min="0.01"
                      max="999.99"
                      placeholder="29.90"
                      disabled={updateSettingMutation.isPending}
                      value={pricingForm.watch('premiumMonthlyPrice') ?? ''}
                      onChange={(e) => {
                        const value = e.target.value;
                        if (value === '') {
                          pricingForm.setValue('premiumMonthlyPrice', undefined as any);
                        } else {
                          const numValue = parseFloat(value);
                          if (!isNaN(numValue)) {
                            pricingForm.setValue('premiumMonthlyPrice', numValue);
                          }
                        }
                      }}
                    />
                    {pricingForm.formState.errors.premiumMonthlyPrice && (
                      <p className="text-sm text-red-600">
                        {pricingForm.formState.errors.premiumMonthlyPrice.message}
                      </p>
                    )}
                    <p className="text-sm text-gray-500">
                      Valor cobrado mensalmente dos usuários Premium
                    </p>
                  </div>
                </div>

                {/* Preço Anual */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">Plano Premium Anual</h3>

                  <div className="space-y-2">
                    <Label htmlFor="premiumYearlyPrice">Preço Anual (R$)</Label>
                    <Input
                      id="premiumYearlyPrice"
                      type="number"
                      step="0.01"
                      min="0.01"
                      max="9999.99"
                      placeholder="299.00"
                      disabled={updateSettingMutation.isPending}
                      value={pricingForm.watch('premiumYearlyPrice') ?? ''}
                      onChange={(e) => {
                        const value = e.target.value;
                        if (value === '') {
                          pricingForm.setValue('premiumYearlyPrice', undefined as any);
                        } else {
                          const numValue = parseFloat(value);
                          if (!isNaN(numValue)) {
                            pricingForm.setValue('premiumYearlyPrice', numValue);
                          }
                        }
                      }}
                    />
                    {pricingForm.formState.errors.premiumYearlyPrice && (
                      <p className="text-sm text-red-600">
                        {pricingForm.formState.errors.premiumYearlyPrice.message}
                      </p>
                    )}
                    <p className="text-sm text-gray-500">
                      Valor cobrado anualmente (deve ser menor que 12x o preço mensal)
                    </p>
                    {(() => {
                      const monthlyPrice = pricingForm.watch('premiumMonthlyPrice');
                      const yearlyPrice = pricingForm.watch('premiumYearlyPrice');

                      if (monthlyPrice && yearlyPrice && typeof monthlyPrice === 'number' && typeof yearlyPrice === 'number') {
                        const monthlyEquivalent = (yearlyPrice / 12).toFixed(2);
                        const yearlySavings = ((monthlyPrice * 12) - yearlyPrice).toFixed(2);

                        return (
                          <div className="text-sm text-blue-600">
                            <p>Equivalente mensal: R$ {monthlyEquivalent}</p>
                            <p>Economia anual: R$ {yearlySavings}</p>
                          </div>
                        );
                      }
                      return null;
                    })()}
                  </div>
                </div>
              </div>

              {/* Botões de ação para preços */}
              <div className="flex items-center justify-between pt-6 border-t">
                <div className="text-sm text-amber-600 bg-amber-50 p-3 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <AlertTriangle className="h-4 w-4" />
                    <span className="font-medium">Importante:</span>
                  </div>
                  <p className="mt-1">
                    Alterações nos preços afetam apenas novos usuários. Usuários existentes mantêm o valor contratado até a próxima renovação.
                  </p>
                </div>

                <div className="flex items-center space-x-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handlePricingReset}
                    disabled={updateSettingMutation.isPending}
                  >
                    <RotateCcw className="h-4 w-4 mr-2" />
                    Resetar
                  </Button>
                  <Button
                    type="submit"
                    disabled={updateSettingMutation.isPending}
                  >
                    <Save className="h-4 w-4 mr-2" />
                    {updateSettingMutation.isPending ? 'Salvando...' : 'Salvar Preços'}
                  </Button>
                </div>
              </div>
            </form>
          )}
        </CardContent>
      </Card>
    </GlobalAdminLayout>
  );
}
