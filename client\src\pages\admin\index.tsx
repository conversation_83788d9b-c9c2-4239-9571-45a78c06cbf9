import { useEffect } from 'react';
import { useLocation } from 'wouter';
import AdminSidebarLayout from '@/components/admin/AdminSidebarLayout';
import { DashboardMVP } from '@/components/admin/dashboard/DashboardMVP';
import { useTranslation } from '@/hooks/useTranslation';
import { useStore } from '@/context/StoreContext';

export default function AdminDashboard() {
  const { t } = useTranslation();
  const [, setLocation] = useLocation();
  const { store, isLoading } = useStore();

  // Only redirect to settings if explicitly needed for store setup
  // The dashboard should show even without a store, displaying appropriate messages

  return (
    <AdminSidebarLayout
      title={t('dashboard.title')}
      description={t('dashboard.welcome')}
    >
      <DashboardMVP />
    </AdminSidebarLayout>
  );
}
