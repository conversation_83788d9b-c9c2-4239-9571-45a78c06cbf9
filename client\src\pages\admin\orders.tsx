import { useEffect, useState } from 'react';
import { useLocation } from 'wouter';
import AdminSidebarLayout from '@/components/admin/AdminSidebarLayout';
import OrderManagement from '@/components/admin/OrderManagement';
import { useTranslation } from '@/hooks/useTranslation';
import { useStore } from '@/context/StoreContext';

export default function OrdersPage() {
  const { t } = useTranslation();
  const [location, setLocation] = useLocation();
  const { store, isLoading } = useStore();
  // Estado para forçar a atualização do componente
  const [key, setKey] = useState(0);

  // Extrair parâmetros de query da URL
  const urlParams = new URLSearchParams(window.location.search);
  const initialStatusFilter = urlParams.get('status');

  // Redirect to settings if no store exists
  useEffect(() => {
    if (!isLoading && !store) {
      setLocation('/admin/settings');
    }
  }, [store, isLoading, setLocation]);

  // Forçar atualização do componente quando a página for carregada
  useEffect(() => {
    console.log('OrdersPage montada - forçando atualização do componente');
    // Forçar atualização após um curto período
    const timer = setTimeout(() => {
      setKey(prev => prev + 1);
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  return (
    <AdminSidebarLayout title={t('common.orders')}>
      <OrderManagement key={key} initialStatusFilter={initialStatusFilter} />
    </AdminSidebarLayout>
  );
}
