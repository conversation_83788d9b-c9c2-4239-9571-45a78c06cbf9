import { useState, useEffect } from 'react';
import { useLocation } from 'wouter';
import { useTranslation } from '@/hooks/useTranslation';
import { useAdminOrder } from '@/context/AdminOrderContext';
import { useStore } from '@/context/StoreContext';
import { useToast } from '@/hooks/use-toast';
import { useMutation } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { auth } from '@/lib/firebase';
import AdminSidebarLayout from '@/components/admin/AdminSidebarLayout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';
import { ArrowLeft, Plus, Minus, Save } from 'lucide-react';

// Interface for custom options
interface CustomOption {
  id: string;
  name: string;
  price: number;
  quantity: number;
}

// Interface for selected variation (used when adding to order)
interface SelectedVariation {
  variationId: string;
  variationName: string;
  optionId: string;
  optionName: string;
  price: number;
  quantity: number;
  isCustom: boolean;
}

// Interface for revision item
interface RevisionItem {
  id: number;
  productId: number;
  productName: string;
  productDescription?: string;
  productImage?: string;
  quantity: number;
  unitPrice: number;
  subtotal: number;
  selectedVariations: any[];
  observation?: string | null;
}

export default function CustomProductPage() {
  const { t } = useTranslation();
  const [, navigate] = useLocation();
  const { toast } = useToast();
  const { addItem, items, removeItem } = useAdminOrder();
  const { store } = useStore();

  // State for the custom product
  const [productName, setProductName] = useState('');
  const [productDescription, setProductDescription] = useState('');
  const [productPrice, setProductPrice] = useState(0);
  const [quantity, setQuantity] = useState(1);
  const [customOptions, setCustomOptions] = useState<CustomOption[]>([]);
  const [newCustomOption, setNewCustomOption] = useState<CustomOption>({
    id: '',
    name: '',
    price: 0,
    quantity: 1
  });

  // State to track if we're editing an existing product
  const [isEditing, setIsEditing] = useState(false);
  const [editingItemId, setEditingItemId] = useState<number | null>(null);

  // State to track if we're working with a revision or a new order
  const [isRevision, setIsRevision] = useState(false);
  const [revisionId, setRevisionId] = useState<string | null>(null);
  const [orderId, setOrderId] = useState<string | null>(null);
  const [revisionItem, setRevisionItem] = useState<RevisionItem | null>(null);

  // Mutation para atualizar item de revisão
  const updateRevisionItemMutation = useMutation({
    mutationFn: async (data: any) => {
      if (!revisionId) return null;

      // Função para obter o uid
      const getUid = async () => {
        if (auth.currentUser && auth.currentUser.uid) {
          return auth.currentUser.uid;
        }
        return '';
      };

      // Enviar o ID do item como parte dos dados
      const response = await fetch(`/api/orders/revisions/${revisionId}/items?uid=${await getUid()}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          items: [data]
        })
      });

      if (!response.ok) {
        throw new Error('Falha ao atualizar o item da revisão');
      }

      return await response.json();
    },
    onSuccess: () => {
      toast({
        title: t('products.itemUpdated') || 'Item atualizado',
        description: t('products.itemUpdatedDesc') || 'O item foi atualizado com sucesso',
        variant: "success",
      });

      // Navegar de volta para a página de detalhes do pedido
      if (orderId) {
        navigate(`/admin/orders/${orderId}`);
      } else {
        navigate('/admin/orders');
      }
    },
    onError: (error) => {
      toast({
        title: t('common.error') || 'Erro',
        description: error instanceof Error ? error.message : 'Erro ao atualizar o item',
        variant: "destructive",
      });
    }
  });

  // Check if we're editing an existing custom product or adding to a revision
  useEffect(() => {
    // Verificar se estamos trabalhando com uma revisão
    const currentRevisionId = localStorage.getItem('currentRevisionId');
    const currentOrderId = localStorage.getItem('currentOrderId');
    const currentItemId = localStorage.getItem('currentItemId');
    const isCustomProduct = localStorage.getItem('isCustomProduct');
    const editingOrderItem = localStorage.getItem('editingOrderItem');
    const isRevisionFlag = localStorage.getItem('isRevision');

    console.log('Contexto da página de produto personalizado:', {
      currentRevisionId,
      currentOrderId,
      currentItemId,
      isCustomProduct,
      editingOrderItem,
      isRevisionFlag
    });

    // Verificar se estamos adicionando um produto personalizado a uma revisão
    if (isRevisionFlag === 'true' && currentRevisionId) {
      console.log('Adicionando produto personalizado a uma revisão:', currentRevisionId);
      setIsRevision(true);
      setRevisionId(currentRevisionId);
      setOrderId(currentOrderId);
      setIsEditing(false);
    }
    // Se temos um ID de revisão e um ID de item, estamos editando um item existente em uma revisão
    else if (currentRevisionId && currentItemId) {
      console.log('Trabalhando com revisão:', currentRevisionId, 'item:', currentItemId);
      setIsRevision(true);
      setRevisionId(currentRevisionId);
      setOrderId(currentOrderId);
      setIsEditing(true);
      setEditingItemId(parseInt(currentItemId));

      // Buscar dados do item da revisão via API
      const fetchRevisionItem = async () => {
        try {
          const uid = auth.currentUser?.uid || '';
          const response = await fetch(`/api/orders/revisions/${currentRevisionId}/items/${currentItemId}?uid=${uid}`);

          if (!response.ok) {
            throw new Error('Falha ao buscar dados do item da revisão');
          }

          const itemData = await response.json();
          console.log('Dados do item da revisão:', itemData);

          // Verificar se é um produto customizado
          const isCustom = itemData.productId < 0 || isCustomProduct === 'true';

          if (isCustom) {
            // Configurar os dados do produto customizado
            setProductName(itemData.productName || '');
            setProductDescription(itemData.observation || itemData.productDescription || '');
            setProductPrice(itemData.unitPrice || 0);
            setQuantity(itemData.quantity || 1);
            setRevisionItem(itemData);

            // Processar opções personalizadas das variações
            if (itemData.selectedVariations && itemData.selectedVariations.length > 0) {
              console.log('Processando variações do produto personalizado:', itemData.selectedVariations);

              // Filtrar apenas as variações personalizadas
              const customVariations = itemData.selectedVariations.filter(
                (variation: any) => variation.isCustom || variation.variationId === 'custom'
              );

              console.log('Variações personalizadas filtradas:', customVariations);

              if (customVariations.length > 0) {
                const options: CustomOption[] = customVariations.map((variation: any) => ({
                  id: variation.optionId,
                  name: variation.optionName,
                  price: variation.price || 0,
                  quantity: variation.quantity || 1
                }));

                console.log('Opções personalizadas mapeadas:', options);
                setCustomOptions(options);
              }
            }
          } else {
            // Se não for um produto customizado, redirecionar para a página de detalhes do produto
            navigate(`/admin/orders/product-details/${currentItemId}`);
          }
        } catch (error) {
          console.error('Erro ao buscar item da revisão:', error);
          toast({
            title: t('common.error') || 'Erro',
            description: 'Não foi possível carregar os dados do item',
            variant: "destructive",
          });
        }
      };

      fetchRevisionItem();
    }
    // Se não estamos trabalhando com revisão, verificar se estamos editando um item de pedido novo
    else if (editingOrderItem === 'true' && isCustomProduct === 'true') {
      setIsEditing(true);

      const itemId = localStorage.getItem('editingOrderItemId');
      if (itemId) {
        const itemIdNumber = parseInt(itemId);
        setEditingItemId(itemIdNumber);

        // Find the item in the admin order context
        const itemToEdit = items.find(item => item.id === itemIdNumber);

        if (itemToEdit) {
          console.log('Carregando produto personalizado para edição:', itemToEdit);

          // Set the product details
          setProductName(itemToEdit.name || '');
          setProductDescription(itemToEdit.observation || '');
          setProductPrice(itemToEdit.price || 0);
          setQuantity(itemToEdit.quantity || 1);

          // Process custom options from selectedVariations
          if (itemToEdit.selectedVariations && itemToEdit.selectedVariations.length > 0) {
            console.log('Processando variações do produto personalizado:', itemToEdit.selectedVariations);

            // Filtrar apenas as variações personalizadas (com isCustom=true ou variationId='custom')
            const customVariations = itemToEdit.selectedVariations.filter(
              variation => variation.isCustom || variation.variationId === 'custom'
            );

            console.log('Variações personalizadas filtradas:', customVariations);

            if (customVariations.length > 0) {
              const options: CustomOption[] = customVariations.map(variation => ({
                id: variation.optionId,
                name: variation.optionName,
                price: variation.price || 0,
                quantity: variation.quantity || 1
              }));

              console.log('Opções personalizadas mapeadas:', options);
              setCustomOptions(options);
            } else {
              console.log('Nenhuma variação personalizada encontrada no item');
            }
          } else {
            console.log('Item não possui variações');
          }
        } else {
          console.log('Item não encontrado com ID:', itemIdNumber);
        }
      }
    }
  }, [items, navigate, t, toast]);

  // Calculate total price
  const calculateTotalPrice = () => {
    let total = productPrice * quantity;

    // Add prices from custom options
    customOptions.forEach(option => {
      total += option.price * option.quantity;
    });

    return total;
  };

  // Function to handle adding a new custom option
  const handleAddCustomOption = () => {
    if (!newCustomOption.name.trim()) {
      toast({
        title: t('common.error') || 'Erro',
        description: t('products.customOptionNameRequired') || 'O nome da opção é obrigatório',
        variant: "destructive",
      });
      return;
    }

    // Generate a unique ID for the new option
    const newOption = {
      ...newCustomOption,
      id: `custom-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`
    };

    setCustomOptions(prev => [...prev, newOption]);

    // Reset the form
    setNewCustomOption({
      id: '',
      name: '',
      price: 0,
      quantity: 1
    });
  };

  // Function to handle removing a custom option
  const handleRemoveCustomOption = (id: string) => {
    setCustomOptions(prev => prev.filter(option => option.id !== id));
  };

  // Function to handle updating a custom option quantity
  const handleUpdateCustomOptionQuantity = (id: string, newQuantity: number) => {
    setCustomOptions(prev => prev.map(option =>
      option.id === id ? { ...option, quantity: Math.max(1, newQuantity) } : option
    ));
  };

  // Function to save the custom product and add it to the order
  const handleSaveCustomProduct = () => {
    if (!productName.trim()) {
      toast({
        title: t('common.error') || 'Erro',
        description: t('products.productNameRequired') || 'O nome do produto é obrigatório',
        variant: "destructive",
      });
      return;
    }

    // Map custom options to selected variations format
    const selectedVariations: SelectedVariation[] = customOptions.map(option => ({
      variationId: 'custom',
      variationName: t('products.other') || 'Outros',
      optionId: option.id,
      optionName: option.name,
      price: option.price,
      quantity: option.quantity,
      isCustom: true
    }));

    // Verificar se estamos adicionando a uma revisão (sem editar um item existente)
    const isAddingToRevision = isRevision && !isEditing && revisionId;

    // Se estamos trabalhando com uma revisão e editando um item existente
    if (isRevision && isEditing && revisionId && revisionItem) {
      console.log('Atualizando produto personalizado em revisão:', {
        id: revisionItem.id,
        nome: productName,
        preco: productPrice,
        quantidade: quantity,
        variacoes: selectedVariations
      });

      // Calcular o subtotal
      const subtotal = productPrice * quantity;

      // Preparar dados para atualização
      const updateData = {
        id: revisionItem.id,
        productId: revisionItem.productId,
        productName: productName,
        productDescription: productDescription,
        quantity: quantity,
        unitPrice: productPrice,
        subtotal: subtotal,
        selectedVariations: selectedVariations,
        observation: productDescription.trim() || null
      };

      // Enviar atualização para o servidor
      updateRevisionItemMutation.mutate(updateData);
    }
    // Se estamos adicionando um novo produto personalizado a uma revisão existente
    else if (isAddingToRevision) {
      console.log('Adicionando novo produto personalizado a uma revisão:', {
        revisionId,
        nome: productName,
        preco: productPrice,
        quantidade: quantity,
        variacoes: selectedVariations
      });

      // Criar uma mutation para adicionar um novo item à revisão
      const addItemToRevisionMutation = async () => {
        try {
          // Função para obter o uid
          const getUid = async () => {
            if (auth.currentUser && auth.currentUser.uid) {
              return auth.currentUser.uid;
            }
            return '';
          };

          // Create a unique ID for the custom product (negative to avoid conflicts with real products)
          // Limitando o valor para estar dentro do intervalo do tipo integer do PostgreSQL (-2,147,483,648 a 2,147,483,647)
          // Usamos um número aleatório negativo entre -1 e -2 bilhões
          const customProductId = -Math.floor(Math.random() * 2000000000 + 1);

          // Calcular o subtotal
          const subtotal = productPrice * quantity;

          // Preparar os dados do item
          const itemData = {
            productId: customProductId,
            productName: productName,
            productDescription: productDescription,
            productImage: null,
            quantity: quantity,
            unitPrice: productPrice,
            subtotal: subtotal,
            selectedVariations: selectedVariations,
            observation: productDescription.trim() || null
          };

          // Enviar o novo item para a API
          const response = await fetch(`/api/orders/revisions/${revisionId}/items?uid=${await getUid()}`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              item: itemData
            })
          });

          if (!response.ok) {
            throw new Error(`Falha ao adicionar item: ${response.status} ${response.statusText}`);
          }

          const result = await response.json();
          console.log('Resposta da API ao adicionar item:', result);

          // Mostrar mensagem de sucesso
          toast({
            title: t('products.productAddedToast') || 'Produto adicionado',
            description: t('products.customProductAddedToRevision') || 'O produto personalizado foi adicionado à revisão do pedido',
            variant: "success",
          });

          // Limpar flags do localStorage
          localStorage.removeItem('isRevision');
          localStorage.removeItem('revisionId');
          localStorage.removeItem('orderId');

          // Navegar de volta para a página de detalhes do pedido
          if (orderId) {
            navigate(`/admin/orders/${orderId}`);
          } else {
            navigate('/admin/orders');
          }
        } catch (error) {
          console.error('Erro ao adicionar item à revisão:', error);
          toast({
            title: t('common.error') || 'Erro',
            description: error instanceof Error ? error.message : String(error),
            variant: "destructive",
          });
        }
      };

      // Executar a mutation
      addItemToRevisionMutation();
    }
    // Se estamos editando um produto existente em um novo pedido
    else if (isEditing && editingItemId !== null) {
      console.log('Atualizando produto personalizado existente em novo pedido:', {
        id: editingItemId,
        nome: productName,
        preco: productPrice,
        quantidade: quantity,
        variacoes: selectedVariations
      });

      // First remove the old item
      removeItem(editingItemId);

      // Then add the updated item with the same negative ID to maintain consistency
      const productId = parseInt(localStorage.getItem('currentProductId') || '0');

      addItem({
        productId: productId, // Use the same negative ID
        name: productName,
        price: productPrice,
        quantity,
        selectedVariations,
        observation: productDescription.trim() || undefined
      });

      toast({
        title: t('products.itemUpdated') || 'Item atualizado',
        description: t('products.itemUpdatedDesc') || 'O item foi atualizado com sucesso'
      });

      // Clear localStorage flags
      localStorage.removeItem('adminOrderCreationProduct');
      localStorage.removeItem('currentProductId');
      localStorage.removeItem('editingOrderItem');
      localStorage.removeItem('editingOrderItemId');
      localStorage.removeItem('isCustomProduct');

      // Navigate back to the new order page
      navigate('/admin/orders/new');
    }
    // Se estamos criando um novo produto personalizado em um novo pedido
    else {
      // Create a unique ID for the custom product (negative to avoid conflicts with real products)
      // Limitando o valor para estar dentro do intervalo do tipo integer do PostgreSQL (-2,147,483,648 a 2,147,483,647)
      // Usamos um número aleatório negativo entre -1 e -2 bilhões
      const customProductId = -Math.floor(Math.random() * 2000000000 + 1);

      console.log('Adicionando novo produto personalizado em novo pedido:', {
        id: customProductId,
        nome: productName,
        preco: productPrice,
        quantidade: quantity,
        variacoes: selectedVariations
      });

      // Add the custom product to the order
      addItem({
        productId: customProductId, // Use negative ID to indicate custom product
        name: productName,
        price: productPrice,
        quantity,
        selectedVariations,
        observation: productDescription.trim() || undefined
      });

      toast({
        title: t('products.productAddedToast') || 'Produto adicionado',
        description: t('products.customProductAddedToOrder') || 'O produto personalizado foi adicionado ao pedido'
      });

      // Navigate back to the new order page
      navigate('/admin/orders/new');
    }
  };

  // Function to go back to the product selection page or new order page
  const handleGoBack = () => {
    // Verificar se estamos adicionando a uma revisão
    const isAddingToRevision = localStorage.getItem('isRevision') === 'true';

    // Limpar flags do localStorage
    localStorage.removeItem('adminOrderCreationProduct');
    localStorage.removeItem('currentProductId');
    localStorage.removeItem('editingOrderItem');
    localStorage.removeItem('editingOrderItemId');
    localStorage.removeItem('isCustomProduct');
    localStorage.removeItem('currentItemId');
    localStorage.removeItem('isRevision');

    // Se estamos adicionando a uma revisão, voltar para a página de seleção de produtos
    if (isAddingToRevision) {
      // Manter o ID da revisão e do pedido para continuar o fluxo de adição
      // Voltar para a página de seleção de produtos
      navigate('/admin/orders/select-products');
    }
    // Se estamos trabalhando com uma revisão (editando um item existente)
    else if (isRevision) {
      // Limpar o ID da revisão
      localStorage.removeItem('currentRevisionId');

      // Voltar para a página de detalhes do pedido
      if (orderId) {
        navigate(`/admin/orders/${orderId}`);
      } else {
        navigate('/admin/orders');
      }
    }
    // Se estamos editando um item existente em um novo pedido
    else if (isEditing) {
      // Voltar para a página de novo pedido
      navigate('/admin/orders/new');
    }
    // Se estamos adicionando um novo produto personalizado
    else {
      // Voltar para a página de seleção de produtos
      navigate('/admin/orders/select-products');
    }
  };

  return (
    <AdminSidebarLayout title={isEditing
      ? (t('orders.editCustomProduct') || 'Editar Produto Personalizado')
      : (t('orders.createCustomProduct') || 'Criar Produto Personalizado')
    }>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-semibold">
            {isEditing
              ? (t('orders.editCustomProduct') || 'Editar Produto Personalizado')
              : (t('orders.createCustomProduct') || 'Criar Produto Personalizado')
            }
          </h2>
          <Button variant="outline" onClick={handleGoBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            {t('common.goBack') || "Voltar"}
          </Button>
        </div>

        <p className="text-muted-foreground mt-2">
          {t('orders.customProductDescription') || 'Crie um produto personalizado que existirá apenas neste pedido'}
        </p>

        {/* Formulário do produto */}
        <div className="space-y-6 mt-4">
          <h3 className="text-lg font-medium">{t('orders.customProductFormTitle') || 'Formulário do produto'}</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Nome do produto */}
            <div className="space-y-2">
              <Label htmlFor="productName">{t('products.productName') || 'Nome do produto'}</Label>
              <Input
                id="productName"
                value={productName}
                onChange={(e) => setProductName(e.target.value)}
                placeholder={t('orders.productNamePlaceholder') || 'Ex: Bolo personalizado'}
              />
            </div>

            {/* Preço do produto */}
            <div className="space-y-2">
              <Label htmlFor="productPrice">{t('products.price') || 'Preço'}</Label>
              <div className="relative">
                <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                  {store?.currency || 'R$'}
                </span>
                <Input
                  id="productPrice"
                  type="number"
                  min="0"
                  step="0.01"
                  value={productPrice}
                  onChange={(e) => {
                    const value = parseFloat(e.target.value);
                    setProductPrice(isNaN(value) ? 0 : value);
                  }}
                  className="pl-10"
                />
              </div>
            </div>
          </div>

          {/* Descrição do produto */}
          <div className="space-y-2">
            <Label htmlFor="productDescription">{t('products.productDescription') || 'Descrição'}</Label>
            <Textarea
              id="productDescription"
              value={productDescription}
              onChange={(e) => setProductDescription(e.target.value)}
              placeholder={t('orders.productDescriptionPlaceholder') || 'Ex: Bolo personalizado com decoração especial'}
              rows={3}
            />
          </div>

          {/* Quantidade */}
          <div className="space-y-2">
            <Label htmlFor="quantity">{t('products.quantity') || 'Quantidade'}</Label>
            <div className="flex items-center space-x-2">
              <Button
                type="button"
                variant="outline"
                size="icon"
                onClick={() => setQuantity(prev => Math.max(1, prev - 1))}
              >
                <Minus className="h-4 w-4" />
              </Button>
              <Input
                id="quantity"
                type="number"
                min="1"
                value={quantity}
                onChange={(e) => {
                  const value = parseInt(e.target.value);
                  setQuantity(isNaN(value) ? 1 : Math.max(1, value));
                }}
                className="w-20 text-center"
              />
              <Button
                type="button"
                variant="outline"
                size="icon"
                onClick={() => setQuantity(prev => prev + 1)}
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Opções personalizadas */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">{t('orders.customProductOptionsTitle') || 'Opções personalizadas'}</h3>
            <div className="border rounded-md p-4">
              <p className="text-sm text-muted-foreground mb-4">
                {t('products.customOptionsDescription') || 'Adicione opções personalizadas como texto livre, valor e quantidade.'}
              </p>

              {/* Form to add new custom option */}
              <div className="space-y-4 mb-6">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
                  {/* Descrição - ocupa mais espaço */}
                  <div className="col-span-1 md:col-span-2">
                    <Label htmlFor="customOptionName" className="mb-2 block text-sm font-medium">
                      {t('products.customOptionName') || 'Descrição'}
                    </Label>
                    <Input
                      id="customOptionName"
                      value={newCustomOption.name}
                      onChange={(e) => setNewCustomOption(prev => ({ ...prev, name: e.target.value }))}
                      placeholder={t('products.customOptionNamePlaceholder') || 'Ex: Decoração especial'}
                      className="w-full"
                    />
                  </div>

                  {/* Preço - colocado antes da quantidade */}
                  <div>
                    <Label htmlFor="customOptionPrice" className="mb-2 block text-sm font-medium">
                      {t('products.price') || 'Preço'}
                    </Label>
                    <div className="relative">
                      <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                        {store?.currency || 'R$'}
                      </span>
                      <Input
                        id="customOptionPrice"
                        type="number"
                        min="0"
                        step="0.01"
                        value={newCustomOption.price}
                        onChange={(e) => {
                          const value = parseFloat(e.target.value);
                          setNewCustomOption(prev => ({ ...prev, price: isNaN(value) ? 0 : value }));
                        }}
                        className="w-full pl-10"
                      />
                    </div>
                  </div>

                  {/* Botão de adicionar */}
                  <div className="flex items-end">
                    <Button
                      type="button"
                      onClick={handleAddCustomOption}
                      disabled={!newCustomOption.name.trim()}
                      className="w-full"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      {t('products.addCustomOption') || 'Adicionar'}
                    </Button>
                  </div>
                </div>
              </div>

              {/* Lista de opções personalizadas */}
              {customOptions.length > 0 ? (
                <div className="space-y-3">
                  {customOptions.map(option => (
                    <Card key={option.id} className="overflow-hidden">
                      <CardContent className="p-3">
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <p className="font-medium">{option.name}</p>
                            <p className="text-sm text-muted-foreground">
                              {store?.currency || 'R$'} {option.price.toFixed(2)} x {option.quantity} = {store?.currency || 'R$'} {(option.price * option.quantity).toFixed(2)}
                            </p>
                          </div>
                          <div className="flex items-center space-x-2">
                            <div className="flex items-center border rounded-md">
                              <Button
                                type="button"
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8 rounded-none"
                                onClick={() => handleUpdateCustomOptionQuantity(option.id, option.quantity - 1)}
                              >
                                <Minus className="h-3 w-3" />
                              </Button>
                              <span className="w-8 text-center">{option.quantity}</span>
                              <Button
                                type="button"
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8 rounded-none"
                                onClick={() => handleUpdateCustomOptionQuantity(option.id, option.quantity + 1)}
                              >
                                <Plus className="h-3 w-3" />
                              </Button>
                            </div>
                            <Button
                              type="button"
                              variant="destructive"
                              size="icon"
                              className="h-8 w-8"
                              onClick={() => handleRemoveCustomOption(option.id)}
                            >
                              <Minus className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <p className="text-center text-muted-foreground py-4">
                  {t('products.noCustomOptions') || 'Nenhuma opção personalizada adicionada'}
                </p>
              )}
            </div>
          </div>

          {/* Resumo e botão de salvar */}
          <div className="flex flex-col md:flex-row justify-between items-center pt-4 border-t">
            <div className="mb-4 md:mb-0">
              <p className="text-lg font-semibold">
                {t('products.total') || 'Total'}: {store?.currency || 'R$'} {calculateTotalPrice().toFixed(2)}
              </p>
            </div>
            <Button
              onClick={handleSaveCustomProduct}
              disabled={!productName.trim()}
              size="lg"
            >
              <Save className="h-4 w-4 mr-2" />
              {isEditing
                ? (t('common.update') || 'Atualizar')
                : (t('common.save') || 'Salvar')
              }
            </Button>
          </div>
        </div>
      </div>
    </AdminSidebarLayout>
  );
}
