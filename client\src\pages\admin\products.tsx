import { useEffect } from 'react';
import { useLocation } from 'wouter';
import AdminSidebarLayout from '@/components/admin/AdminSidebarLayout';
import ProductManagement from '@/components/admin/ProductManagement';
import { useTranslation } from '@/hooks/useTranslation';
import { useStore } from '@/context/StoreContext';

export default function ProductsPage() {
  const { t } = useTranslation();
  const [, setLocation] = useLocation();
  const { store, isLoading } = useStore();

  // Redirect to settings if no store exists
  useEffect(() => {
    if (!isLoading && !store) {
      setLocation('/admin/settings');
    }
  }, [store, isLoading, setLocation]);

  return (
    <AdminSidebarLayout title={t('common.products')}>
      <ProductManagement />
    </AdminSidebarLayout>
  );
}
