import { useRef, ReactNode } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useTranslation } from "@/hooks/useTranslation";
import { useStore } from "@/context/StoreContext";
import { uploadProductImage } from "@/lib/uploadUtils";
import { apiRequest } from "@/lib/queryClient";
import { useLocation } from "wouter";
import AdminSidebarLayout from "@/components/admin/AdminSidebarLayout";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage, FormDescription } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import { X, Plus, Trash2, ArrowLeft, ImagePlus, Image as ImageIcon } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { type Product } from "@shared/schema";

// Variation option schema
const variationOptionSchema = z.object({
  id: z.number().optional(),
  name: z.string().min(1, { message: "Option name is required" }),
  price: z.coerce.number().default(0),
});

// Variation schema
const variationSchema = z.object({
  id: z.number().optional(),
  name: z.string().min(1, { message: "Variation name is required" }),
  required: z.boolean().default(false),
  multipleChoice: z.boolean().default(false),
  minSelections: z.coerce.number().min(0).optional(),
  maxSelections: z.coerce.number().min(1).optional(),
  options: z.array(variationOptionSchema).default([]),
});

// Product form schema
const productSchema = z.object({
  name: z.string().min(1, { message: "Product name is required" }),
  description: z.string().optional(),
  price: z.coerce.number().positive({ message: "Price must be positive" }),
  images: z.array(z.string()).default([]),
  categoryId: z.coerce.number().optional(),
  inStock: z.boolean().default(true),
  hasVariations: z.boolean().default(false),
  variations: z.array(variationSchema).default([]),
  storeId: z.number().optional(), // Adicionando storeId ao schema
});

type ProductFormValues = z.infer<typeof productSchema>;

// Tipo estendido para a API
interface ProductApiData extends ProductFormValues {
  storeId: number;
}

// Componente de Variação para o formulário de adição que evita o problema do React Hook chamado em um loop
function AddVariationFieldset({ 
  variationIndex, 
  control, 
  onRemove, 
  t, 
  watch 
}: {
  variationIndex: number;
  control: any;
  onRemove: () => void;
  t: (key: string) => string | undefined;
  watch: any;
}) {
  // Criar field array separado para opções desta variação
  const optionsField = useFieldArray({
    control,
    name: `variations.${variationIndex}.options`
  });

  // Observar se é multiple choice
  const isMultipleChoice = watch(`variations.${variationIndex}.multipleChoice`);

  return (
    <div className="border rounded-lg p-3 space-y-3">
      <div className="flex justify-between items-center">
        <h4 className="font-medium">{t('products.variation') || "Variação"} {variationIndex + 1}</h4>
        <Button
          type="button"
          variant="ghost"
          size="icon"
          onClick={onRemove}
        >
          <Trash2 className="h-4 w-4 text-error" />
        </Button>
      </div>

      {/* Variation Name */}
      <div className="grid grid-cols-1 gap-3">
        <FormField
          control={control}
          name={`variations.${variationIndex}.name`}
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('products.variationName') || "Nome da variação"}</FormLabel>
              <FormControl>
                <Input {...field} placeholder={t('products.variationNamePlaceholder') || "Ex: Tamanho, Sabor, Cor"} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Variation Required */}
        <FormField
          control={control}
          name={`variations.${variationIndex}.required`}
          render={({ field }) => (
            <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-3">
              <FormControl>
                <Checkbox
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
              <div className="space-y-1 leading-none">
                <FormLabel>
                  {t('products.required') || "Obrigatório"}
                </FormLabel>
                <FormDescription>
                  {t('products.requiredDescription') || "O cliente precisa selecionar uma opção desta variação."}
                </FormDescription>
              </div>
            </FormItem>
          )}
        />

        {/* Multiple Choice */}
        <FormField
          control={control}
          name={`variations.${variationIndex}.multipleChoice`}
          render={({ field }) => (
            <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-3">
              <FormControl>
                <Checkbox
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
              <div className="space-y-1 leading-none">
                <FormLabel>
                  {t('products.multipleChoice') || "Múltipla escolha"}
                </FormLabel>
                <FormDescription>
                  {t('products.multipleChoiceDescription') || "O cliente pode escolher mais de uma opção."}
                </FormDescription>
              </div>
            </FormItem>
          )}
        />

        {/* Min/Max Selections */}
        {/* Only show min/max if multipleChoice is enabled */}
        {isMultipleChoice && (
          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={control}
              name={`variations.${variationIndex}.minSelections`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('products.minSelections') || "Mínimo de escolhas"}</FormLabel>
                  <FormControl>
                    <Input 
                      type="number" 
                      min={0}
                      {...field} 
                      onChange={(e) => field.onChange(e.target.valueAsNumber)}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={control}
              name={`variations.${variationIndex}.maxSelections`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('products.maxSelections') || "Máximo de escolhas"}</FormLabel>
                  <FormControl>
                    <Input 
                      type="number" 
                      min={1}
                      {...field} 
                      onChange={(e) => field.onChange(e.target.valueAsNumber)}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        )}
      </div>

      {/* Options Section */}
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <h5 className="text-sm font-medium">{t('products.options') || "Opções"}</h5>
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={() => optionsField.append({ name: '', price: 0 })}
          >
            <Plus className="h-3 w-3 mr-1" />
            {t('products.addOption') || "Adicionar opção"}
          </Button>
        </div>

        {optionsField.fields.length === 0 && (
          <div className="text-center py-3 text-xs text-muted-foreground border rounded-md">
            {t('products.noOptions') || "Adicione opções para esta variação."}
          </div>
        )}

        <div className="space-y-2">
          {optionsField.fields.map((option, optionIndex) => (
            <div key={option.id} className="flex items-center space-x-2">
              <FormField
                control={control}
                name={`variations.${variationIndex}.options.${optionIndex}.name`}
                render={({ field }) => (
                  <FormItem className="flex-1">
                    <FormControl>
                      <Input {...field} placeholder={t('products.optionNamePlaceholder') || "Ex: Pequeno, Médio, Grande"} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={control}
                name={`variations.${variationIndex}.options.${optionIndex}.price`}
                render={({ field }) => (
                  <FormItem className="w-24">
                    <FormControl>
                      <Input 
                        type="number" 
                        step="0.01"
                        min="0"
                        placeholder="+R$"
                        {...field} 
                        onChange={(e) => field.onChange(parseFloat(e.target.value))}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <Button
                type="button"
                variant="ghost"
                size="icon"
                onClick={() => optionsField.remove(optionIndex)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

export default function NewProductPage() {
  const { t } = useTranslation();
  const { store } = useStore();
  const [, navigate] = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const imageRef1 = useRef<HTMLInputElement>(null);
  const imageRef2 = useRef<HTMLInputElement>(null);
  const imageRef3 = useRef<HTMLInputElement>(null);

  // Fetch categories
  const { data: categories } = useQuery({
    queryKey: ['/api/categories'],
    enabled: !!store,
  });

  // Form setup
  const form = useForm<ProductFormValues>({
    resolver: zodResolver(productSchema),
    defaultValues: {
      name: '',
      description: '',
      price: 0,
      images: [],
      categoryId: undefined,
      inStock: true,
      hasVariations: false,
      variations: [],
    },
  });

  // Field array for variations
  const variationsArray = useFieldArray({
    control: form.control,
    name: "variations"
  });

  // Create mutation
  const createMutation = useMutation<Product, Error, ProductFormValues>({
    mutationFn: async (data: ProductFormValues) => {
      console.log('Mutation: sending data to API:', data);
      try {
        // Garantir que o storeId está presente antes de fazer a requisição
        if (!data.storeId && store) {
          data.storeId = store.id;
        }
        
        console.log('Mutation: final data being sent:', data);
        // Corrigir a ordem dos parâmetros: método, URL, dados
        const response = await apiRequest('POST', '/api/products', data);
        console.log('Mutation: received response:', response.status, response.statusText);
        const result = await response.json();
        console.log('Mutation: parsed response:', result);
        return result as Product;
      } catch (error) {
        console.error('Mutation: Error in mutation function:', error);
        throw error; // Re-throw para ser tratado pelo onError
      }
    },
    onSuccess: (data) => {
      console.log('Mutation: Success callback executed with data:', data);
      queryClient.invalidateQueries({ queryKey: ['/api/products'] });
      toast({
        title: t('products.createSuccess') || "Produto criado",
        description: t('products.createSuccessDescription') || "O produto foi criado com sucesso.",
      });
      navigate('/admin/products');
    },
    onError: (error) => {
      console.error('Mutation: Error callback executed:', error);
      toast({
        title: t('products.createError') || "Erro ao criar produto",
        description: (error as Error).message || t('products.createErrorDescription') || "Ocorreu um erro ao criar o produto. Tente novamente.",
        variant: "destructive",
      });
    },
  });

  // Handle form submission
  const onSubmit = async (values: ProductFormValues) => {
    console.log('Form submitted with values:', values);
    
    try {
      if (!store) {
        throw new Error('Loja não encontrada. É necessário estar autenticado.');
      }
      
      // Se o hasVariations for false, limpar o array de variações e garantir que o price seja um número
      const dataToSubmit = {
        ...values,
        price: Number(values.price), // Garantir que o preço seja um número
        storeId: store.id, // Garantimos que o storeId está presente
        variations: values.hasVariations ? values.variations.map(variation => ({
          ...variation,
          // Garantir que os valores numéricos sejam números
          minSelections: variation.minSelections !== undefined ? Number(variation.minSelections) : 0,
          maxSelections: variation.maxSelections !== undefined ? Number(variation.maxSelections) : 1,
          options: variation.options.map(option => ({
            ...option,
            price: Number(option.price) || 0
          }))
        })) : [],
      };
      
      // Garantir que categoryId seja null se for 0 ou undefined
      if (dataToSubmit.categoryId === 0 || dataToSubmit.categoryId === undefined) {
        dataToSubmit.categoryId = undefined;
      }
      
      console.log('Data to submit:', dataToSubmit);
      
      // Enviar para a API
      const result = await createMutation.mutateAsync(dataToSubmit);
      console.log('API response:', result);
      
      // Mostrar toast de sucesso 
      toast({
        title: t('products.createSuccess') || "Produto criado",
        description: t('products.createSuccessDescription') || "O produto foi criado com sucesso."
      });
      
      // Redirecionar após o sucesso
      navigate('/admin/products');
    } catch (error) {
      console.error('Error submitting form:', error);
      
      // Mostrar toast de erro com detalhes
      toast({
        title: t('products.createError') || "Erro ao criar produto",
        description: error instanceof Error ? error.message : 'Ocorreu um erro ao criar o produto',
        variant: "destructive"
      });
    }
  };

  // Handle image upload
  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>, index: number) => {
    const file = event.target.files?.[0];
    if (!file || !store) return;

    try {
      const imageUrl = await uploadProductImage(file, store.id);
      
      // Get current images array
      const currentImages = form.getValues('images') || [];
      
      // Create a new array with the new image at the specified index
      const newImages = [...currentImages];
      newImages[index] = imageUrl;
      
      // Update the form
      form.setValue('images', newImages);
    } catch (error) {
      console.error('Image upload error:', error);
      toast({
        title: t('products.imageUploadError') || "Erro ao enviar imagem",
        description: (error as Error).message || t('products.imageUploadErrorDescription') || "Ocorreu um erro ao enviar a imagem. Tente novamente.",
        variant: "destructive",
      });
    }
  };

  // Render image input
  const renderImageInput = (index: number, inputRef: React.RefObject<HTMLInputElement>) => {
    const images = form.watch('images') || [];
    const hasImage = images[index];

    return (
      <div className="relative">
        <input
          type="file"
          accept="image/*"
          id={`image-${index}`}
          className="hidden"
          ref={inputRef}
          onChange={(e) => handleImageUpload(e, index)}
        />
        <Button
          type="button"
          variant={hasImage ? "outline" : "secondary"}
          className="w-full"
          onClick={() => inputRef.current?.click()}
        >
          {hasImage ? (
            <>
              <ImageIcon className="h-4 w-4 mr-2" />
              {t('products.changeImage') || "Alterar imagem"}
            </>
          ) : (
            <>
              <ImagePlus className="h-4 w-4 mr-2" />
              {index === 0 
                ? (t('products.addMainImage') || "Adicionar imagem principal") 
                : (t('products.addExtraImage') || `Adicionar imagem ${index + 1}`)}
            </>
          )}
        </Button>
      </div>
    );
  };

  // Render image previews
  const renderImagePreviews = (images: string[]) => {
    if (!images || images.length === 0) return null;
    
    return (
      <div className="grid grid-cols-3 gap-2 mt-3">
        {images.map((url, idx) => {
          if (!url) return null;
          return (
            <div key={idx} className="relative aspect-square rounded-md overflow-hidden border">
              <img src={url} alt={`Preview ${idx + 1}`} className="w-full h-full object-cover" />
              <button
                type="button"
                className="absolute top-1 right-1 bg-error text-white p-1 rounded-full"
                onClick={() => {
                  const newImages = [...images];
                  newImages[idx] = '';
                  form.setValue('images', newImages);
                }}
              >
                <X className="h-3 w-3" />
              </button>
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <AdminSidebarLayout title={t('products.newProduct') || "Novo produto"}>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <Breadcrumb className="mb-2">
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink href="/admin">{t('common.dashboard') || "Dashboard"}</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbLink href="/admin/products">{t('common.products') || "Produtos"}</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbLink>{t('products.newProduct') || "Novo produto"}</BreadcrumbLink>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
          <Button variant="outline" onClick={() => navigate('/admin/products')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            {t('common.back') || "Voltar"}
          </Button>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>{t('products.productDetails') || "Detalhes do produto"}</CardTitle>
            <CardDescription>
              {t('products.productDetailsDescription') || "Preencha os detalhes do produto."}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={(event) => {
                console.log('Form submit event triggered');
                form.handleSubmit(onSubmit)(event);
              }} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('products.productName') || "Nome do produto"}</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="price"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('products.productPrice') || "Preço"}</FormLabel>
                        <FormControl>
                          <Input {...field} type="number" step="0.01" min="0" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('products.productDescription') || "Descrição"}</FormLabel>
                      <FormControl>
                        <Textarea {...field} rows={4} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Category Selection */}
                {categories && Array.isArray(categories) && categories.length > 0 ? (
                  <FormField
                    control={form.control}
                    name="categoryId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('products.productCategory') || "Categoria"}</FormLabel>
                        <Select
                          onValueChange={(value) => {
                            console.log('Category value selected:', value);
                            // Se o valor for "null" ou "0", definir como null
                            field.onChange(value === "null" || value === "0" ? null : parseInt(value));
                          }}
                          value={field.value?.toString() || ""}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder={t('products.selectCategory') || "Selecione uma categoria"} />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="null">{t('products.noCategory') || "Sem categoria"}</SelectItem>
                            {categories.map((category: any) => (
                              <SelectItem key={category.id} value={category.id.toString()}>
                                {category.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                ) : null}

                {/* Images Section */}
                <div>
                  <FormLabel>{t('products.productImages') || "Imagens do produto"}</FormLabel>
                  <FormDescription>
                    {t('products.imagesHelperText') || "Adicione até 3 imagens para o produto. A primeira será a imagem principal."}
                  </FormDescription>
                  <div className="space-y-2 mt-2">
                    {renderImageInput(0, imageRef1)}
                    {renderImageInput(1, imageRef2)}
                    {renderImageInput(2, imageRef3)}
                  </div>
                  {renderImagePreviews(form.watch('images') || [])}
                </div>

                <FormField
                  control={form.control}
                  name="hasVariations"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                      <div className="space-y-0.5">
                        <FormLabel>{t('products.hasVariations') || "Tem variações"}</FormLabel>
                        <FormDescription>
                          {t('products.hasVariationsDescription') || "Produto tem opções como tamanho, sabor, etc."}
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="inStock"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                      <div className="space-y-0.5">
                        <FormLabel>{t('products.inStock') || "Disponível em estoque"}</FormLabel>
                        <FormDescription>
                          {field.value ? t('products.available') || "Produto disponível para venda" : t('products.unavailable') || "Produto indisponível para venda"}
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                {/* Product Variations Section */}
                {form.watch("hasVariations") && (
                  <div className="space-y-4 border rounded-lg p-4">
                    <div className="flex justify-between items-center">
                      <h3 className="text-lg font-medium">{t('products.variations') || "Variações"}</h3>
                      <Button
                        type="button"
                        onClick={() => {
                          variationsArray.append({
                            name: "",
                            required: true,
                            multipleChoice: false,
                            options: [{ name: "", price: 0 }]
                          });
                        }}
                        variant="outline"
                        size="sm"
                      >
                        <Plus className="mr-1 h-4 w-4" />
                        {t('products.addVariation') || "Adicionar variação"}
                      </Button>
                    </div>

                    {variationsArray.fields.length === 0 && (
                      <div className="text-center py-4 text-muted-foreground">
                        {t('products.noVariations') || "Sem variações cadastradas. Clique em 'Adicionar variação' para começar."}
                      </div>
                    )}

                    {variationsArray.fields.map((variation, variationIndex) => (
                      <AddVariationFieldset 
                        key={variation.id}
                        variationIndex={variationIndex}
                        control={form.control}
                        onRemove={() => variationsArray.remove(variationIndex)}
                        t={t}
                        watch={form.watch}
                      />
                    ))}
                  </div>
                )}

                <CardFooter className="flex justify-between px-0">
                  <Button type="button" variant="outline" onClick={() => navigate('/admin/products')}>
                    {t('common.cancel') || "Cancelar"}
                  </Button>
                  <Button 
                    type="button" 
                    disabled={createMutation.isPending}
                    onClick={(e) => {
                      e.preventDefault();
                      console.log('Submit button clicked manually');
                      // Chamar manualmente o onSubmit com os valores atuais do form
                      const values = form.getValues();
                      onSubmit(values);
                    }}
                  >
                    {createMutation.isPending ? t('common.saving') || "Salvando..." : t('common.save') || "Salvar"}
                  </Button>
                </CardFooter>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
    </AdminSidebarLayout>
  );
}
