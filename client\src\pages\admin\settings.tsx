import AdminSidebarLayout from '@/components/admin/AdminSidebarLayout';
import StoreSettings from '@/components/admin/StoreSettings';
import { useTranslation } from '@/hooks/useTranslation';

export default function SettingsPage() {
  const { t } = useTranslation();

  return (
    <AdminSidebarLayout
      title={t('settings.title')}
      description={t('settings.subtitle')}
    >
      <StoreSettings />
    </AdminSidebarLayout>
  );
}
