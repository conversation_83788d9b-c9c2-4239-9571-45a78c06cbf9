import { useEffect } from 'react';
import { useLocation } from 'wouter';
import AuthForm from '@/components/auth/AuthForm';
import { useAuth } from '@/context/FirebaseAuthContext';

export default function Register() {
  const [, setLocation] = useLocation();
  const { isAuthenticated } = useAuth();

  // Redirect to admin dashboard if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      setLocation('/admin');
    }
  }, [isAuthenticated, setLocation]);

  return (
    <div className="min-h-screen flex items-center justify-center p-4 bg-gradient-to-br from-pink-50 via-white to-yellow-50">
      {/* Background decorativo */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-pink-200 to-yellow-200 rounded-full opacity-20 blur-3xl" />
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-tr from-yellow-200 to-pink-200 rounded-full opacity-20 blur-3xl" />
      </div>

      {/* Conteúdo principal */}
      <div className="relative z-10 w-full max-w-md">
        <AuthForm mode="register" />
      </div>
    </div>
  );
}
