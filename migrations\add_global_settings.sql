-- Criar tabela de configurações globais
CREATE TABLE IF NOT EXISTS global_settings (
  id SERIAL PRIMARY KEY,
  key TEXT NOT NULL UNIQUE,
  value JSONB NOT NULL,
  description TEXT,
  updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
  updated_by INTEGER REFERENCES users(id)
);

-- Inserir configurações padrão
INSERT INTO global_settings (key, value, description) VALUES
(
  'plan_limits',
  '{
    "freeMaxProducts": 10,
    "freeMaxOrdersPerMonth": -1,
    "premiumMaxProducts": 50,
    "premiumMaxOrdersPerMonth": -1,
    "freeAllowPdfGeneration": false,
    "freeAllowAnalytics": false,
    "freeAllowWhatsappIntegration": false,
    "freeAllowCoupons": false,
    "freeAllowCustomization": false,
    "premiumAllowPdfGeneration": true,
    "premiumAllowAnalytics": true,
    "premiumAllowWhatsappIntegration": true,
    "premiumAllowCoupons": true,
    "premiumAllowCustomization": true
  }',
  'Limites de produtos, pedidos e funcionalidades para cada tipo de plano'
)
ON CONFLICT (key) DO NOTHING;

-- Adicionar índice para melhor performance
CREATE INDEX IF NOT EXISTS idx_global_settings_key ON global_settings(key);

-- Comentário para documentação
COMMENT ON TABLE global_settings IS 'Configurações globais editáveis do sistema';
COMMENT ON COLUMN global_settings.key IS 'Chave única da configuração';
COMMENT ON COLUMN global_settings.value IS 'Valor da configuração em formato JSON';
COMMENT ON COLUMN global_settings.description IS 'Descrição da configuração';
COMMENT ON COLUMN global_settings.updated_at IS 'Data da última atualização';
COMMENT ON COLUMN global_settings.updated_by IS 'ID do usuário que fez a última atualização';
