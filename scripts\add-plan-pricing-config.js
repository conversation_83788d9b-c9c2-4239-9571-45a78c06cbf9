#!/usr/bin/env node

/**
 * Script para adicionar configuração de preços dos planos na tabela global_settings
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Carregar variáveis de ambiente
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Variáveis de ambiente SUPABASE_URL e SUPABASE_SERVICE_ROLE_KEY são obrigatórias');
  process.exit(1);
}

async function addPlanPricingConfig() {
  try {
    console.log('🔄 Adicionando configuração de preços dos planos...');

    const supabase = createClient(supabaseUrl, supabaseKey);

    // Verificar se a configuração já existe
    const { data: existingSetting, error: selectError } = await supabase
      .from('global_settings')
      .select('*')
      .eq('key', 'plan_pricing')
      .single();

    if (selectError && selectError.code !== 'PGRST116') {
      console.error('❌ Erro ao verificar configuração existente:', selectError);
      process.exit(1);
    }

    // Configuração de preços padrão
    const planPricingConfig = {
      premiumMonthlyPrice: 29.90,
      premiumYearlyPrice: 299.00,
    };

    if (existingSetting) {
      // Atualizar configuração existente
      console.log('📝 Atualizando configuração de preços existente...');
      
      const { error: updateError } = await supabase
        .from('global_settings')
        .update({
          value: planPricingConfig,
          description: 'Configuração de preços dos planos de assinatura Premium',
          updated_at: new Date().toISOString()
        })
        .eq('key', 'plan_pricing');

      if (updateError) {
        console.error('❌ Erro ao atualizar configuração:', updateError);
        process.exit(1);
      }

      console.log('✅ Configuração de preços atualizada com sucesso!');
    } else {
      // Criar nova configuração
      console.log('📝 Criando nova configuração de preços...');
      
      const { error: insertError } = await supabase
        .from('global_settings')
        .insert({
          key: 'plan_pricing',
          value: planPricingConfig,
          description: 'Configuração de preços dos planos de assinatura Premium'
        });

      if (insertError) {
        console.error('❌ Erro ao criar configuração:', insertError);
        process.exit(1);
      }

      console.log('✅ Configuração de preços criada com sucesso!');
    }

    // Verificar a configuração final
    const { data: finalSetting, error: finalError } = await supabase
      .from('global_settings')
      .select('*')
      .eq('key', 'plan_pricing')
      .single();

    if (finalError) {
      console.error('❌ Erro ao verificar configuração final:', finalError);
      process.exit(1);
    }

    console.log('📋 Configuração final:');
    console.log('   Key:', finalSetting.key);
    console.log('   Value:', JSON.stringify(finalSetting.value, null, 2));
    console.log('   Description:', finalSetting.description);
    console.log('   Updated At:', finalSetting.updated_at);

    console.log('\n🎉 Script executado com sucesso!');
    console.log('💡 Agora você pode acessar /admin/global/settings para editar os preços dos planos.');

  } catch (error) {
    console.error('❌ Erro inesperado:', error);
    process.exit(1);
  }
}

// Executar o script
addPlanPricingConfig();
