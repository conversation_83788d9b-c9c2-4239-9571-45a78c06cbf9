import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

async function createGlobalSettingsTable() {
  try {
    console.log('🔧 Criando tabela de configurações globais...');
    
    const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!supabaseUrl || !supabaseKey) {
      console.error('❌ Variáveis de ambiente do Supabase não encontradas');
      console.error('Certifique-se de que SUPABASE_URL e SUPABASE_SERVICE_ROLE_KEY estão definidas no .env');
      process.exit(1);
    }
    
    const supabase = createClient(supabaseUrl, supabaseKey);
    
    // Criar tabela global_settings
    console.log('📝 Criando tabela global_settings...');
    
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS global_settings (
        id SERIAL PRIMARY KEY,
        key TEXT NOT NULL UNIQUE,
        value JSONB NOT NULL,
        description TEXT,
        updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
        updated_by INTEGER REFERENCES users(id)
      );
    `;
    
    const { error: createError } = await supabase.rpc('exec_sql', {
      sql: createTableSQL
    });
    
    if (createError) {
      console.error('❌ Erro ao criar tabela:', createError);
      process.exit(1);
    }
    
    console.log('✅ Tabela global_settings criada com sucesso!');
    
    // Criar índice
    console.log('📝 Criando índice...');
    
    const createIndexSQL = `
      CREATE INDEX IF NOT EXISTS idx_global_settings_key ON global_settings(key);
    `;
    
    const { error: indexError } = await supabase.rpc('exec_sql', {
      sql: createIndexSQL
    });
    
    if (indexError) {
      console.error('⚠️ Erro ao criar índice (não crítico):', indexError);
    } else {
      console.log('✅ Índice criado com sucesso!');
    }
    
    // Inserir configurações padrão
    console.log('📝 Inserindo configurações padrão...');
    
    const { error: insertError } = await supabase
      .from('global_settings')
      .upsert({
        key: 'plan_limits',
        value: {
          // Limitações numéricas
          freeMaxProducts: 10,
          freeMaxOrdersPerMonth: -1,
          premiumMaxProducts: 50,
          premiumMaxOrdersPerMonth: -1,

          // Funcionalidades do plano gratuito
          freeAllowPdfGeneration: false,
          freeAllowAnalytics: false,
          freeAllowWhatsappIntegration: false,
          freeAllowCoupons: false,
          freeAllowCustomization: false,

          // Funcionalidades do plano premium
          premiumAllowPdfGeneration: true,
          premiumAllowAnalytics: true,
          premiumAllowWhatsappIntegration: true,
          premiumAllowCoupons: true,
          premiumAllowCustomization: true,
        },
        description: 'Limites de produtos, pedidos e funcionalidades para cada tipo de plano'
      }, {
        onConflict: 'key',
        ignoreDuplicates: false
      });
    
    if (insertError) {
      console.error('❌ Erro ao inserir configurações padrão:', insertError);
      process.exit(1);
    }
    
    console.log('✅ Configurações padrão inseridas com sucesso!');
    
    // Verificar se os dados foram inseridos
    console.log('🔍 Verificando dados inseridos...');
    
    const { data: settings, error: selectError } = await supabase
      .from('global_settings')
      .select('*');
    
    if (selectError) {
      console.error('❌ Erro ao verificar dados:', selectError);
    } else {
      console.log('📊 Configurações encontradas:', settings.length);
      settings.forEach(setting => {
        console.log(`  - ${setting.key}: ${JSON.stringify(setting.value)}`);
      });
    }
    
    console.log('🎉 Migration concluída com sucesso!');
    console.log('');
    console.log('Agora você pode:');
    console.log('1. Acessar /admin/global/settings para configurar os limites');
    console.log('2. Os limites serão aplicados automaticamente aos usuários');
    
  } catch (error) {
    console.error('❌ Erro inesperado:', error);
    process.exit(1);
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  createGlobalSettingsTable();
}

module.exports = { createGlobalSettingsTable };
