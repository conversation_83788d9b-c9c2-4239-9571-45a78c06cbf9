import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Carregar variáveis de ambiente
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Erro: SUPABASE_URL ou SUPABASE_SERVICE_ROLE_KEY não definidos.');
  console.error('Por favor, defina essas variáveis no arquivo .env');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function promoteGlobalAdmin(userId, action = 'promote') {
  try {
    if (!userId) {
      console.error('❌ Erro: ID do usuário é obrigatório');
      console.log('Uso: node scripts/promote-global-admin.js <user_id> [promote|demote]');
      return;
    }

    const isPromoting = action === 'promote';
    
    console.log(`🔧 ${isPromoting ? 'Promovendo' : 'Removendo'} usuário ${userId} ${isPromoting ? 'a' : 'de'} super-administrador global...`);

    // Verificar se o usuário existe
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, email, full_name, is_global_admin')
      .eq('id', userId)
      .single();

    if (userError || !user) {
      console.error('❌ Usuário não encontrado:', userError?.message || 'ID inválido');
      return;
    }

    console.log(`👤 Usuário encontrado: ${user.email} (${user.full_name || 'Sem nome'})`);
    console.log(`📊 Status atual: ${user.is_global_admin ? 'Super Admin' : 'Usuário normal'}`);

    if (user.is_global_admin === isPromoting) {
      console.log(`⚠️ Usuário já ${isPromoting ? 'é' : 'não é'} um super-administrador global`);
      return;
    }

    // Atualizar o status do usuário
    const { error: updateError } = await supabase
      .from('users')
      .update({ is_global_admin: isPromoting })
      .eq('id', userId);

    if (updateError) {
      console.error('❌ Erro ao atualizar usuário:', updateError);
      return;
    }

    console.log(`✅ Usuário ${isPromoting ? 'promovido' : 'removido'} com sucesso!`);
    console.log(`📧 ${user.email} agora ${isPromoting ? 'tem' : 'não tem'} permissões de super-administrador global`);

    if (isPromoting) {
      console.log('\n🎉 O usuário agora pode acessar:');
      console.log('   • Dashboard Global: /admin/global');
      console.log('   • Gerenciamento de Lojas: /admin/global/stores');
      console.log('   • Analytics Globais');
      console.log('   • Controle de Assinaturas');
      console.log('   • Gerenciamento de Usuários');
    }

  } catch (error) {
    console.error('❌ Erro inesperado:', error);
  }
}

async function listUsers() {
  try {
    console.log('📋 Listando usuários...\n');

    const { data: users, error } = await supabase
      .from('users')
      .select('id, email, full_name, is_global_admin, created_at')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('❌ Erro ao buscar usuários:', error);
      return;
    }

    if (!users || users.length === 0) {
      console.log('📭 Nenhum usuário encontrado');
      return;
    }

    console.log('ID\t| Email\t\t\t| Nome\t\t| Admin Global\t| Criado em');
    console.log('─'.repeat(80));

    users.forEach(user => {
      const id = user.id.toString().padEnd(8);
      const email = (user.email || '').padEnd(25);
      const name = (user.full_name || 'Sem nome').padEnd(15);
      const isAdmin = user.is_global_admin ? '✅ Sim' : '❌ Não';
      const createdAt = new Date(user.created_at).toLocaleDateString('pt-BR');
      
      console.log(`${id}| ${email}| ${name}| ${isAdmin}\t| ${createdAt}`);
    });

    console.log('\n💡 Para promover um usuário: node scripts/promote-global-admin.js <user_id> promote');
    console.log('💡 Para remover permissões: node scripts/promote-global-admin.js <user_id> demote');

  } catch (error) {
    console.error('❌ Erro inesperado:', error);
  }
}

// Processar argumentos da linha de comando
const args = process.argv.slice(2);
const command = args[0];

if (command === 'list') {
  listUsers();
} else if (command && !isNaN(parseInt(command))) {
  const userId = parseInt(command);
  const action = args[1] || 'promote';
  promoteGlobalAdmin(userId, action);
} else {
  console.log('🔧 Script de Gerenciamento de Super-Administradores Globais\n');
  console.log('Uso:');
  console.log('  node scripts/promote-global-admin.js list                    # Listar usuários');
  console.log('  node scripts/promote-global-admin.js <user_id>               # Promover usuário');
  console.log('  node scripts/promote-global-admin.js <user_id> promote       # Promover usuário');
  console.log('  node scripts/promote-global-admin.js <user_id> demote        # Remover permissões');
  console.log('\nExemplos:');
  console.log('  node scripts/promote-global-admin.js list');
  console.log('  node scripts/promote-global-admin.js 1 promote');
  console.log('  node scripts/promote-global-admin.js 1 demote');
}

export { promoteGlobalAdmin, listUsers };
