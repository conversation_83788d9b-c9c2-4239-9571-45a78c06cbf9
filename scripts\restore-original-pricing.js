#!/usr/bin/env node

/**
 * Script para restaurar os preços originais dos planos
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Carregar variáveis de ambiente
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Variáveis de ambiente SUPABASE_URL e SUPABASE_SERVICE_ROLE_KEY são obrigatórias');
  process.exit(1);
}

async function restoreOriginalPricing() {
  try {
    console.log('🔄 Restaurando preços originais...');

    const supabase = createClient(supabaseUrl, supabaseKey);

    // Valores originais
    const originalValues = {
      premiumMonthlyPrice: 29.90,
      premiumYearlyPrice: 299.00,
    };

    console.log('📝 Restaurando valores originais:', JSON.stringify(originalValues, null, 2));

    const { error: updateError } = await supabase
      .from('global_settings')
      .update({
        value: originalValues,
        updated_at: new Date().toISOString()
      })
      .eq('key', 'plan_pricing');

    if (updateError) {
      console.error('❌ Erro ao restaurar configuração:', updateError);
      process.exit(1);
    }

    console.log('✅ Preços originais restaurados com sucesso!');

  } catch (error) {
    console.error('❌ Erro inesperado:', error);
    process.exit(1);
  }
}

// Executar o script
restoreOriginalPricing();
