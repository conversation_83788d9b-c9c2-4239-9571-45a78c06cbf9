#!/usr/bin/env node

/**
 * Script para testar a configuração de preços dos planos
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Carregar variáveis de ambiente
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Variáveis de ambiente SUPABASE_URL e SUPABASE_SERVICE_ROLE_KEY são obrigatórias');
  process.exit(1);
}

async function testPricingConfig() {
  try {
    console.log('🔄 Testando configuração de preços dos planos...');

    const supabase = createClient(supabaseUrl, supabaseKey);

    // Buscar a configuração atual
    const { data: currentSetting, error: selectError } = await supabase
      .from('global_settings')
      .select('*')
      .eq('key', 'plan_pricing')
      .single();

    if (selectError) {
      console.error('❌ Erro ao buscar configuração:', selectError);
      process.exit(1);
    }

    console.log('📋 Configuração atual:');
    console.log('   Key:', currentSetting.key);
    console.log('   Value:', JSON.stringify(currentSetting.value, null, 2));
    console.log('   Description:', currentSetting.description);

    // Testar atualização
    const testValues = {
      premiumMonthlyPrice: 39.90,
      premiumYearlyPrice: 399.00,
    };

    console.log('\n🔄 Testando atualização com valores de teste...');
    console.log('   Novos valores:', JSON.stringify(testValues, null, 2));

    const { error: updateError } = await supabase
      .from('global_settings')
      .update({
        value: testValues,
        updated_at: new Date().toISOString()
      })
      .eq('key', 'plan_pricing');

    if (updateError) {
      console.error('❌ Erro ao atualizar configuração:', updateError);
      process.exit(1);
    }

    console.log('✅ Configuração atualizada com sucesso!');

    // Verificar a atualização
    const { data: updatedSetting, error: verifyError } = await supabase
      .from('global_settings')
      .select('*')
      .eq('key', 'plan_pricing')
      .single();

    if (verifyError) {
      console.error('❌ Erro ao verificar atualização:', verifyError);
      process.exit(1);
    }

    console.log('\n📋 Configuração após atualização:');
    console.log('   Value:', JSON.stringify(updatedSetting.value, null, 2));
    console.log('   Updated At:', updatedSetting.updated_at);

    // Restaurar valores originais
    console.log('\n🔄 Restaurando valores originais...');
    
    const { error: restoreError } = await supabase
      .from('global_settings')
      .update({
        value: currentSetting.value,
        updated_at: new Date().toISOString()
      })
      .eq('key', 'plan_pricing');

    if (restoreError) {
      console.error('❌ Erro ao restaurar configuração:', restoreError);
      process.exit(1);
    }

    console.log('✅ Valores originais restaurados!');
    console.log('\n🎉 Teste concluído com sucesso!');
    console.log('💡 A configuração de preços está funcionando corretamente no banco de dados.');

  } catch (error) {
    console.error('❌ Erro inesperado:', error);
    process.exit(1);
  }
}

// Executar o teste
testPricingConfig();
