#!/usr/bin/env node

/**
 * Script para atualizar as configurações globais com as novas funcionalidades
 * Este script atualiza a configuração 'plan_limits' para incluir todas as funcionalidades
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Carregar variáveis de ambiente
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Variáveis de ambiente SUPABASE_URL e SUPABASE_SERVICE_ROLE_KEY são obrigatórias');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function updateGlobalSettings() {
  try {
    console.log('🔄 Atualizando configurações globais...');

    // Configurações atualizadas com todas as funcionalidades
    const updatedPlanLimits = {
      // Limitações numéricas
      freeMaxProducts: 10,
      freeMaxOrdersPerMonth: -1, // Agora ilimitado para plano gratuito
      premiumMaxProducts: 50,
      premiumMaxOrdersPerMonth: -1, // Ilimitado
      
      // Funcionalidades do plano gratuito
      freeAllowPdfGeneration: false,
      freeAllowAnalytics: false,
      freeAllowWhatsappIntegration: false,
      freeAllowCoupons: false,
      freeAllowCustomization: false,
      
      // Funcionalidades do plano premium
      premiumAllowPdfGeneration: true,
      premiumAllowAnalytics: true,
      premiumAllowWhatsappIntegration: true,
      premiumAllowCoupons: true,
      premiumAllowCustomization: true,
    };

    // Verificar se a configuração já existe
    const { data: existingSetting, error: fetchError } = await supabase
      .from('global_settings')
      .select('*')
      .eq('key', 'plan_limits')
      .single();

    if (fetchError && fetchError.code !== 'PGRST116') {
      console.error('❌ Erro ao buscar configuração existente:', fetchError);
      process.exit(1);
    }

    if (existingSetting) {
      // Atualizar configuração existente
      console.log('📝 Atualizando configuração existente...');
      
      const { error: updateError } = await supabase
        .from('global_settings')
        .update({
          value: updatedPlanLimits,
          description: 'Limites de produtos, pedidos e funcionalidades para cada tipo de plano',
          updated_at: new Date().toISOString()
        })
        .eq('key', 'plan_limits');

      if (updateError) {
        console.error('❌ Erro ao atualizar configuração:', updateError);
        process.exit(1);
      }

      console.log('✅ Configuração atualizada com sucesso!');
    } else {
      // Criar nova configuração
      console.log('📝 Criando nova configuração...');
      
      const { error: insertError } = await supabase
        .from('global_settings')
        .insert({
          key: 'plan_limits',
          value: updatedPlanLimits,
          description: 'Limites de produtos, pedidos e funcionalidades para cada tipo de plano'
        });

      if (insertError) {
        console.error('❌ Erro ao criar configuração:', insertError);
        process.exit(1);
      }

      console.log('✅ Configuração criada com sucesso!');
    }

    // Verificar a configuração final
    const { data: finalSetting, error: finalError } = await supabase
      .from('global_settings')
      .select('*')
      .eq('key', 'plan_limits')
      .single();

    if (finalError) {
      console.error('❌ Erro ao verificar configuração final:', finalError);
      process.exit(1);
    }

    console.log('📊 Configuração final:');
    console.log(JSON.stringify(finalSetting.value, null, 2));

    console.log('\n🎉 Atualização concluída com sucesso!');
    console.log('\n📋 Resumo das mudanças:');
    console.log('• Pedidos mensais do plano gratuito: 5 → ilimitado (-1)');
    console.log('• Adicionadas configurações de funcionalidades para ambos os planos');
    console.log('• Sistema agora suporta configuração dinâmica de todas as limitações');

  } catch (error) {
    console.error('❌ Erro inesperado:', error);
    process.exit(1);
  }
}

// Executar o script
updateGlobalSettings();
