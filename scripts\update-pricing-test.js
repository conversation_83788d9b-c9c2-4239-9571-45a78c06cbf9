#!/usr/bin/env node

/**
 * Script para atualizar os preços dos planos para teste
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Carregar variáveis de ambiente
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Variáveis de ambiente SUPABASE_URL e SUPABASE_SERVICE_ROLE_KEY são obrigatórias');
  process.exit(1);
}

async function updatePricingForTest() {
  try {
    console.log('🔄 Atualizando preços para teste...');

    const supabase = createClient(supabaseUrl, supabaseKey);

    // Novos valores de teste
    const testValues = {
      premiumMonthlyPrice: 49.90,
      premiumYearlyPrice: 499.00,
    };

    console.log('📝 Atualizando com valores de teste:', JSON.stringify(testValues, null, 2));

    const { error: updateError } = await supabase
      .from('global_settings')
      .update({
        value: testValues,
        updated_at: new Date().toISOString()
      })
      .eq('key', 'plan_pricing');

    if (updateError) {
      console.error('❌ Erro ao atualizar configuração:', updateError);
      process.exit(1);
    }

    console.log('✅ Preços atualizados com sucesso!');
    console.log('💡 Agora recarregue a página http://localhost:3000/admin/global/settings para ver se os valores aparecem corretamente.');

  } catch (error) {
    console.error('❌ Erro inesperado:', error);
    process.exit(1);
  }
}

// Executar o script
updatePricingForTest();
