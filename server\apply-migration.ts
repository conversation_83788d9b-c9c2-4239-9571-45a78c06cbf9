import { pool } from './db';
import * as fs from 'fs';
import { join } from 'path';

// Obter o diretório atual no formato CommonJS
const __dirname = __dirname;

async function applyMigration() {
  try {
    const migrationPath = join(__dirname, '../migrations/add_country_code.sql');
    const sql = fs.readFileSync(migrationPath, 'utf8');
    
    console.log('Executando migração para adicionar coluna country_code à tabela customers...');
    await pool.query(sql);
    console.log('Migração executada com sucesso!');
    process.exit(0);
  } catch (error) {
    console.error('Erro ao executar migração:', error);
    process.exit(1);
  }
}

applyMigration();