import { Request, Response, NextFunction } from "express";
import { storage } from "./storage";

// NOTA: Em ambiente de desenvolvimento, estamos usando uma versão simplificada da autenticação
// Em produção, você deve usar o Firebase Admin SDK com as credenciais completas

/**
 * Middleware simplificado para desenvolvimento
 * Em produção, deve verificar o token JWT do Firebase
 */
export const isAuthenticated = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    console.log('=== MIDDLEWARE DE AUTENTICAÇÃO ===');
    console.log('Método:', req.method);
    console.log('URL:', req.url);
    console.log('Query params:', req.query);
    console.log('Body:', req.body);
    console.log('Headers Authorization:', req.headers.authorization);

    // Extrair o uid direto do body para desenvolvimento
    // Em produção, isso deve ser obtido pela verificação do token Firebase
    const uid = req.body.uid || req.query.uid;

    console.log('UID extraído:', uid);

    if (!uid) {
      console.log('❌ Autenticação falhou: UID não encontrado');
      res.status(401).json({ message: "Not authenticated - missing uid" });
      return;
    }

    // Armazenar informações do usuário no objeto req para uso nas rotas protegidas
    (req as any).user = {
      uid: uid,
      email: null,
      displayName: null
    };

    console.log('✅ UID válido, usuário adicionado ao req:', uid);

    // Sincronizar usuário com o banco de dados, se necessário
    const user = await storage.getUserByFirebaseUid(uid);

    if (!user) {
      console.log('⚠️ Usuário não encontrado no banco, criando novo usuário');
      // Se o usuário não existir no banco de dados, crie-o
      await storage.createUserWithFirebaseUid({
        firebaseUid: uid,
        username: `user-${uid}`,
        email: null,
        profileImageUrl: null,
        fullName: `User ${uid.substring(0, 6)}`
      });
    } else {
      console.log('✅ Usuário encontrado no banco:', user.id);
    }

    console.log('✅ Autenticação bem-sucedida, prosseguindo...');
    next();
  } catch (error) {
    console.error("Authentication middleware error:", error);
    res.status(500).json({ message: "Server error" });
    return;
  }
};

/**
 * Endpoint para sincronizar os dados do usuário Firebase com o banco de dados
 */
export const syncUserData = async (req: Request, res: Response): Promise<void> => {
  console.log('=== SYNC USER DATA ===');
  console.log('Method:', req.method);
  console.log('URL:', req.url);
  console.log('Body:', req.body);
  console.log('Headers:', req.headers);

  try {
    const { id, email, username, profileImageUrl, displayName } = req.body;

    console.log('Extracted data:', { id, email, username, profileImageUrl, displayName });

    if (!id) {
      console.log('Error: Firebase UID is required');
      res.status(400).json({ message: "Firebase UID is required" });
      return;
    }

    // Buscar o usuário pelo firebase_uid
    let existingUser = await storage.getUserByFirebaseUid(id);

    // Determinar o nome completo para o usuário
    const fullName = displayName || username || `User ${id.substring(0, 6)}`;

    if (existingUser) {
      // Atualizar o usuário existente
      const updatedUser = await storage.updateUserByFirebaseUid(id, {
        email: email || existingUser.email,
        username: username || existingUser.username || `user-${id}`,
        profileImageUrl: profileImageUrl || existingUser.profileImageUrl,
        fullName: fullName
      });

      res.status(200).json(updatedUser);
      return;
    } else {
      // Criar um novo usuário
      const newUser = await storage.createUserWithFirebaseUid({
        email: email || null,
        username: username || `user-${id}`,
        profileImageUrl: profileImageUrl || null,
        firebaseUid: id,
        fullName: fullName
      });

      res.status(200).json(newUser);
      return;
    }
  } catch (error) {
    console.error("Error syncing user data:", error);
    res.status(500).json({ message: "Failed to sync user data" });
  }
};

/**
 * Função para verificar se um usuário é admin global
 * Em desenvolvimento, considera todos os usuários como admin global
 */
export const isGlobalAdmin = async (firebaseUid: string): Promise<boolean> => {
  try {
    // Em desenvolvimento, todos os usuários são considerados admin global
    // Em produção, você deve implementar uma verificação real baseada em roles/permissions
    console.log(`🔍 Verificando se usuário ${firebaseUid} é admin global`);

    const user = await storage.getUserByFirebaseUid(firebaseUid);
    if (!user) {
      console.log(`❌ Usuário ${firebaseUid} não encontrado`);
      return false;
    }

    // Para desenvolvimento, retornar true para todos os usuários
    // Em produção, verificar uma propriedade como user.isGlobalAdmin
    console.log(`✅ Usuário ${firebaseUid} é admin global (desenvolvimento)`);
    return true;
  } catch (error) {
    console.error('Erro ao verificar admin global:', error);
    return false;
  }
};

/**
 * Endpoint para obter informações do usuário atualmente autenticado
 */
export const getCurrentUser = async (req: Request, res: Response): Promise<void> => {
  try {
    const firebaseUid = (req as any).user.uid;

    if (!firebaseUid) {
      res.status(401).json({ message: "Not authenticated" });
      return;
    }

    const user = await storage.getUserByFirebaseUid(firebaseUid);

    if (!user) {
      res.status(404).json({ message: "User not found" });
      return;
    }

    res.status(200).json(user);
  } catch (error) {
    console.error("Error getting current user:", error);
    res.status(500).json({ message: "Failed to get user information" });
  }
};