import { drizzle } from 'drizzle-orm/postgres-js';
import { migrate } from 'drizzle-orm/postgres-js/migrator';
import postgres from 'postgres';
import * as path from 'path';

// Initialize a Postgres client for Drizzle to use with the Supabase database
const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

if (!supabaseUrl || !supabaseServiceKey) {
  throw new Error(
    "SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY must be set. Did you forget to configure Supabase?",
  );
}

// Create a connection string for Supabase PostgreSQL
const connectionString = `postgres://postgres:${supabaseServiceKey}@db.${supabaseUrl.replace('https://', '')}.supabase.co:5432/postgres`;

// Create a Postgres client for migrations
const migrationClient = postgres(connectionString, { max: 1 });

// Initialize a Drizzle instance
const db = drizzle(migrationClient);

// Run migrations
async function runMigration() {
  try {
    console.log('Starting migration to Supabase...');
    
    // Run the migration
    await migrate(db, { migrationsFolder: path.resolve('migrations') });
    
    console.log('Migration to Supabase completed successfully!');
  } catch (error) {
    console.error('Migration to Supabase failed:', error);
  } finally {
    // Close the client
    await migrationClient.end();
  }
}

// Run the migration function
runMigration();