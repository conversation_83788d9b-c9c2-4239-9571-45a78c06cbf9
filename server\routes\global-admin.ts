import { Router } from 'express';
import { storage } from '../storage';
import { requireGlobalAdmin } from '../middleware/global-admin';
import { isAuthenticated } from '../firebaseAuth';
import { subscriptionService, SubscriptionService } from '../subscription-service';
import { DEFAULT_GLOBAL_SETTINGS } from '@shared/schema';

const router = Router();

// Aplicar middlewares de autenticação em todas as rotas
router.use(isAuthenticated);
router.use(requireGlobalAdmin);

/**
 * GET /api/admin/global/analytics
 * Retorna métricas globais da plataforma
 */
router.get('/analytics', async (req, res) => {
  try {
    console.log('Buscando analytics globais...');

    // Buscar todas as lojas
    const stores = await storage.getAllStores();
    
    // Buscar todas as assinaturas
    const subscriptions = await storage.getAllSubscriptions();
    
    // Buscar pedidos dos últimos 30 dias
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const recentOrders = await storage.getOrdersByDateRange(thirtyDaysAgo, new Date());
    
    // Calcular métricas
    const totalStores = stores.length;
    const activeStores = stores.filter(store => store.isActive !== false).length;

    // Calcular métricas de assinatura com status de trial
    const now = new Date();

    // Categorizar assinaturas por status de trial
    const subscriptionMetrics = subscriptions.reduce((acc, sub) => {
      const isTrialActive = sub.trialEnd && new Date(sub.trialEnd) > now;
      const isTrialExpired = sub.trialEnd && new Date(sub.trialEnd) <= now;

      if (sub.planType === 'free') {
        acc.free++;
      } else if (sub.planType === 'premium') {
        if (sub.status === 'active') {
          if (isTrialActive) {
            acc.activeTrial++;
          } else {
            acc.activePaid++;
          }
        } else if (sub.status === 'canceled') {
          if (isTrialExpired) {
            acc.expiredTrialCanceled++;
          } else {
            acc.canceled++;
          }
        } else if (sub.status === 'past_due') {
          acc.pastDue++;
        }
      }

      return acc;
    }, {
      free: 0,
      activePaid: 0,
      activeTrial: 0,
      expiredTrialCanceled: 0,
      canceled: 0,
      pastDue: 0
    });



    const premiumStores = subscriptionMetrics.activePaid + subscriptionMetrics.activeTrial;
    
    const totalRevenue = recentOrders.reduce((sum, order) => {
      const orderTotal = order.activeRevision?.total || order.total || 0;
      return sum + orderTotal;
    }, 0);
    
    const totalOrders = recentOrders.length;
    const avgOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;
    
    // Top 10 lojas por número de pedidos
    const storeOrderCounts = recentOrders.reduce((acc, order) => {
      acc[order.storeId] = (acc[order.storeId] || 0) + 1;
      return acc;
    }, {} as Record<number, number>);
    
    const topStoresByOrders = Object.entries(storeOrderCounts)
      .map(([storeId, orderCount]) => {
        const store = stores.find(s => s.id === parseInt(storeId));
        return {
          storeId: parseInt(storeId),
          storeName: store?.name || 'Loja não encontrada',
          storeSlug: store?.slug || '',
          orderCount,
          isActive: store?.isActive !== false
        };
      })
      .sort((a, b) => b.orderCount - a.orderCount)
      .slice(0, 10);

    // Top 10 lojas por receita
    const storeRevenues = recentOrders.reduce((acc, order) => {
      const orderTotal = order.activeRevision?.total || order.total || 0;
      acc[order.storeId] = (acc[order.storeId] || 0) + orderTotal;
      return acc;
    }, {} as Record<number, number>);
    
    const topStoresByRevenue = Object.entries(storeRevenues)
      .map(([storeId, revenue]) => {
        const store = stores.find(s => s.id === parseInt(storeId));
        return {
          storeId: parseInt(storeId),
          storeName: store?.name || 'Loja não encontrada',
          storeSlug: store?.slug || '',
          revenue,
          isActive: store?.isActive !== false
        };
      })
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 10);

    // Crescimento mensal (comparar com mês anterior)
    const sixtyDaysAgo = new Date();
    sixtyDaysAgo.setDate(sixtyDaysAgo.getDate() - 60);
    
    const previousMonthOrders = await storage.getOrdersByDateRange(sixtyDaysAgo, thirtyDaysAgo);
    const previousMonthRevenue = previousMonthOrders.reduce((sum, order) => {
      const orderTotal = order.activeRevision?.total || order.total || 0;
      return sum + orderTotal;
    }, 0);
    
    const revenueGrowth = previousMonthRevenue > 0 
      ? ((totalRevenue - previousMonthRevenue) / previousMonthRevenue) * 100 
      : 0;
    
    const orderGrowth = previousMonthOrders.length > 0 
      ? ((totalOrders - previousMonthOrders.length) / previousMonthOrders.length) * 100 
      : 0;

    // Calcular métricas de conversão de trial
    const totalTrialsStarted = subscriptionMetrics.activeTrial + subscriptionMetrics.activePaid + subscriptionMetrics.expiredTrialCanceled;
    const trialsConverted = subscriptionMetrics.activePaid;
    const trialConversionRate = totalTrialsStarted > 0 ? (trialsConverted / totalTrialsStarted) * 100 : 0;

    // Calcular dias restantes de trial para assinaturas ativas em trial
    const activeTrialSubscriptions = subscriptions
      .filter(sub => {
        const isTrialActive = sub.trialEnd && new Date(sub.trialEnd) > now;
        return sub.planType === 'premium' && sub.status === 'active' && isTrialActive;
      })
      .map(sub => {
        const daysRemaining = sub.trialEnd ? Math.ceil((new Date(sub.trialEnd).getTime() - now.getTime()) / (1000 * 60 * 60 * 24)) : 0;
        return {
          subscriptionId: sub.id,
          storeId: sub.storeId,
          trialEnd: sub.trialEnd,
          daysRemaining
        };
      }) || [];

    const analytics = {
      summary: {
        totalStores,
        activeStores,
        premiumStores,
        totalRevenue,
        totalOrders,
        avgOrderValue,
        revenueGrowth,
        orderGrowth,
        trialConversionRate,
        activeTrials: subscriptionMetrics.activeTrial
      },
      topStoresByOrders,
      topStoresByRevenue,
      subscriptionDistribution: {
        free: subscriptionMetrics.free,
        activePaid: subscriptionMetrics.activePaid,
        activeTrial: subscriptionMetrics.activeTrial,
        expiredTrialCanceled: subscriptionMetrics.expiredTrialCanceled,
        canceled: subscriptionMetrics.canceled,
        pastDue: subscriptionMetrics.pastDue
      },
      trialMetrics: {
        totalTrialsStarted,
        trialsConverted,
        trialConversionRate,
        activeTrialSubscriptions,
        avgTrialDaysRemaining: activeTrialSubscriptions.length > 0
          ? activeTrialSubscriptions.reduce((sum, trial) => sum + trial.daysRemaining, 0) / activeTrialSubscriptions.length
          : 0
      }
    };

    res.json(analytics);
  } catch (error) {
    console.error('Erro ao buscar analytics globais:', error);
    res.status(500).json({ 
      message: 'Erro ao buscar analytics globais',
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

/**
 * GET /api/admin/global/stores
 * Lista todas as lojas com filtros e paginação
 */
router.get('/stores', async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 20, 
      status, 
      planType, 
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    console.log('Buscando lojas globais com filtros:', { page, limit, status, planType, search, sortBy, sortOrder });

    // Buscar todas as lojas
    const allStores = await storage.getAllStores();
    
    // Buscar assinaturas para cada loja
    const storesWithSubscriptions = await Promise.all(
      allStores.map(async (store) => {
        const subscription = await storage.getActiveSubscription(store.id);
        const user = await storage.getUserById(store.userId);
        
        return {
          ...store,
          subscription,
          user: user ? {
            id: user.id,
            email: user.email,
            fullName: user.fullName,
            createdAt: user.createdAt
          } : null,
          isActive: store.isActive !== false // Default para true se não definido
        };
      })
    );

    // Aplicar filtros
    let filteredStores = storesWithSubscriptions;

    if (status) {
      const isActive = status === 'active';
      filteredStores = filteredStores.filter(store => store.isActive === isActive);
    }

    if (planType) {
      filteredStores = filteredStores.filter(store => 
        store.subscription?.planType === planType
      );
    }

    if (search) {
      const searchLower = (search as string).toLowerCase();
      filteredStores = filteredStores.filter(store =>
        store.name.toLowerCase().includes(searchLower) ||
        store.slug.toLowerCase().includes(searchLower) ||
        store.user?.email?.toLowerCase().includes(searchLower)
      );
    }

    // Aplicar ordenação
    filteredStores.sort((a, b) => {
      let aValue, bValue;
      
      switch (sortBy) {
        case 'name':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case 'createdAt':
          aValue = new Date(a.createdAt).getTime();
          bValue = new Date(b.createdAt).getTime();
          break;
        case 'lastActivity':
          // Por enquanto usar createdAt, depois implementar lastActivity real
          aValue = new Date(a.updatedAt || a.createdAt).getTime();
          bValue = new Date(b.updatedAt || b.createdAt).getTime();
          break;
        default:
          aValue = a.id;
          bValue = b.id;
      }

      if (sortOrder === 'desc') {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      } else {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      }
    });

    // Aplicar paginação
    const pageNum = parseInt(page as string);
    const limitNum = parseInt(limit as string);
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = startIndex + limitNum;

    const paginatedStores = filteredStores.slice(startIndex, endIndex);
    const totalStores = filteredStores.length;
    const totalPages = Math.ceil(totalStores / limitNum);

    res.json({
      stores: paginatedStores,
      pagination: {
        currentPage: pageNum,
        totalPages,
        totalStores,
        hasNextPage: pageNum < totalPages,
        hasPrevPage: pageNum > 1
      }
    });
  } catch (error) {
    console.error('Erro ao buscar lojas globais:', error);
    res.status(500).json({ 
      message: 'Erro ao buscar lojas globais',
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

/**
 * GET /api/admin/global/stores/:id
 * Retorna detalhes específicos de uma loja
 */
router.get('/stores/:id', async (req, res) => {
  try {
    const storeId = parseInt(req.params.id);

    if (isNaN(storeId)) {
      return res.status(400).json({ message: 'ID da loja inválido' });
    }

    console.log('Buscando detalhes da loja:', storeId);

    const store = await storage.getStoreById(storeId);

    if (!store) {
      return res.status(404).json({ message: 'Loja não encontrada' });
    }

    // Buscar dados adicionais
    const [subscriptions, user, products, orders, customers] = await Promise.all([
      storage.getSubscriptionsByStoreId(storeId),
      storage.getUserById(store.userId),
      storage.getProductsByStoreId(storeId),
      storage.getOrdersByStoreId(storeId),
      storage.getCustomersByStoreId(storeId)
    ]);

    // Pegar a assinatura mais recente (independente do status)
    const subscription = subscriptions.length > 0 ? subscriptions[0] : null;

    // Calcular métricas dos últimos 30 dias
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const recentOrders = orders.filter(order =>
      new Date(order.createdAt) >= thirtyDaysAgo
    );

    const totalRevenue = recentOrders.reduce((sum, order) => {
      const orderTotal = order.activeRevision?.total || order.total || 0;
      return sum + orderTotal;
    }, 0);

    const storeDetails = {
      ...store,
      isActive: store.isActive !== false,
      subscription,
      user: user ? {
        id: user.id,
        email: user.email,
        fullName: user.fullName,
        createdAt: user.createdAt
      } : null,
      metrics: {
        totalProducts: products.length,
        totalOrders: orders.length,
        totalCustomers: customers.length,
        recentOrders: recentOrders.length,
        recentRevenue: totalRevenue,
        avgOrderValue: recentOrders.length > 0 ? totalRevenue / recentOrders.length : 0
      }
    };

    res.json(storeDetails);
  } catch (error) {
    console.error('Erro ao buscar detalhes da loja:', error);
    res.status(500).json({
      message: 'Erro ao buscar detalhes da loja',
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

/**
 * PATCH /api/admin/global/stores/:id/toggle-status
 * Ativa ou desativa uma loja
 */
router.patch('/stores/:id/toggle-status', async (req, res) => {
  try {
    const storeId = parseInt(req.params.id);
    const { isActive } = req.body;

    if (isNaN(storeId)) {
      return res.status(400).json({ message: 'ID da loja inválido' });
    }

    if (typeof isActive !== 'boolean') {
      return res.status(400).json({ message: 'Status deve ser um valor booleano' });
    }

    console.log('Alterando status da loja:', storeId, 'para:', isActive);

    const store = await storage.getStoreById(storeId);

    if (!store) {
      return res.status(404).json({ message: 'Loja não encontrada' });
    }

    // Atualizar status da loja
    const updatedStore = await storage.updateStore(storeId, { isActive });

    res.json({
      message: `Loja ${isActive ? 'ativada' : 'desativada'} com sucesso`,
      store: updatedStore
    });
  } catch (error) {
    console.error('Erro ao alterar status da loja:', error);
    res.status(500).json({
      message: 'Erro ao alterar status da loja',
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

/**
 * GET /api/admin/global/subscriptions
 * Retorna todas as assinaturas da plataforma com filtros
 */
router.get('/subscriptions', async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      status,
      planType,
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    console.log('Buscando assinaturas globais com filtros:', { page, limit, status, planType, search, sortBy, sortOrder });

    // Buscar todas as assinaturas
    const allSubscriptions = await storage.getAllSubscriptions();

    // Buscar dados das lojas e usuários para cada assinatura
    const subscriptionsWithDetails = await Promise.all(
      allSubscriptions.map(async (subscription) => {
        const store = await storage.getStoreById(subscription.storeId);
        const user = store ? await storage.getUserById(store.userId) : null;

        return {
          ...subscription,
          store: store ? {
            id: store.id,
            name: store.name,
            slug: store.slug,
            isActive: store.isActive !== false
          } : null,
          user: user ? {
            id: user.id,
            email: user.email,
            fullName: user.fullName,
            createdAt: user.createdAt
          } : null
        };
      })
    );

    // Aplicar filtros
    let filteredSubscriptions = subscriptionsWithDetails;

    if (status) {
      filteredSubscriptions = filteredSubscriptions.filter(sub => sub.status === status);
    }

    if (planType) {
      filteredSubscriptions = filteredSubscriptions.filter(sub => sub.planType === planType);
    }

    if (search) {
      const searchLower = (search as string).toLowerCase();
      filteredSubscriptions = filteredSubscriptions.filter(sub =>
        sub.store?.name.toLowerCase().includes(searchLower) ||
        sub.store?.slug.toLowerCase().includes(searchLower) ||
        sub.user?.email?.toLowerCase().includes(searchLower) ||
        sub.stripeCustomerId?.toLowerCase().includes(searchLower)
      );
    }

    // Aplicar ordenação
    filteredSubscriptions.sort((a, b) => {
      let aValue, bValue;

      switch (sortBy) {
        case 'storeName':
          aValue = a.store?.name || '';
          bValue = b.store?.name || '';
          break;
        case 'userEmail':
          aValue = a.user?.email || '';
          bValue = b.user?.email || '';
          break;
        case 'planType':
          aValue = a.planType;
          bValue = b.planType;
          break;
        case 'status':
          aValue = a.status;
          bValue = b.status;
          break;
        case 'createdAt':
        default:
          aValue = new Date(a.createdAt).getTime();
          bValue = new Date(b.createdAt).getTime();
          break;
      }

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortOrder === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
      }

      return sortOrder === 'asc' ? (aValue as number) - (bValue as number) : (bValue as number) - (aValue as number);
    });

    // Aplicar paginação
    const pageNum = parseInt(page as string);
    const limitNum = parseInt(limit as string);
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = startIndex + limitNum;

    const paginatedSubscriptions = filteredSubscriptions.slice(startIndex, endIndex);
    const totalSubscriptions = filteredSubscriptions.length;
    const totalPages = Math.ceil(totalSubscriptions / limitNum);

    res.json({
      subscriptions: paginatedSubscriptions,
      pagination: {
        currentPage: pageNum,
        totalPages,
        totalSubscriptions,
        hasNextPage: pageNum < totalPages,
        hasPrevPage: pageNum > 1
      }
    });
  } catch (error) {
    console.error('Erro ao buscar assinaturas globais:', error);
    res.status(500).json({
      message: 'Erro ao buscar assinaturas globais',
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

/**
 * GET /api/admin/global/subscriptions/:id
 * Retorna detalhes específicos de uma assinatura
 */
router.get('/subscriptions/:id', async (req, res) => {
  try {
    const subscriptionId = parseInt(req.params.id);

    if (isNaN(subscriptionId)) {
      return res.status(400).json({ message: 'ID da assinatura inválido' });
    }

    console.log('Buscando detalhes da assinatura:', subscriptionId);

    const subscription = await storage.getSubscription(subscriptionId);

    if (!subscription) {
      return res.status(404).json({ message: 'Assinatura não encontrada' });
    }

    // Buscar dados adicionais
    const store = await storage.getStoreById(subscription.storeId);
    const user = store ? await storage.getUserById(store.userId) : null;

    // Buscar dados do Stripe se disponível
    let stripeData = null;
    if (subscription.stripeSubscriptionId) {
      try {
        stripeData = await subscriptionService.getStripeSubscriptionDetails(subscription.stripeSubscriptionId);
      } catch (error) {
        console.warn('Erro ao buscar dados do Stripe:', error);
      }
    }

    const subscriptionDetails = {
      ...subscription,
      store: store ? {
        id: store.id,
        name: store.name,
        slug: store.slug,
        isActive: store.isActive !== false,
        createdAt: store.createdAt
      } : null,
      user: user ? {
        id: user.id,
        email: user.email,
        fullName: user.fullName,
        createdAt: user.createdAt
      } : null,
      stripeData
    };

    res.json(subscriptionDetails);
  } catch (error) {
    console.error('Erro ao buscar detalhes da assinatura:', error);
    res.status(500).json({
      message: 'Erro ao buscar detalhes da assinatura',
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

// Função auxiliar para calcular datas de assinatura
function calculateSubscriptionDates(planType: 'free' | 'premium') {
  const now = new Date();
  const currentPeriodStart = new Date(now);

  let currentPeriodEnd: Date | null = null;

  if (planType === 'premium') {
    // Premium: 30 dias de validade
    currentPeriodEnd = new Date(now);
    currentPeriodEnd.setDate(currentPeriodEnd.getDate() + 30);
  }
  // Free: sem data de expiração (null)

  return {
    currentPeriodStart,
    currentPeriodEnd
  };
}

/**
 * PATCH /api/admin/global/subscriptions/:id/update-plan
 * Atualiza o plano de uma assinatura
 */
router.patch('/subscriptions/:id/update-plan', async (req, res) => {
  try {
    const subscriptionId = parseInt(req.params.id);
    const { planType } = req.body;

    if (isNaN(subscriptionId)) {
      return res.status(400).json({ message: 'ID da assinatura inválido' });
    }

    if (!['free', 'premium'].includes(planType)) {
      return res.status(400).json({ message: 'Tipo de plano inválido' });
    }

    console.log('Atualizando plano da assinatura:', subscriptionId, 'para:', planType);

    const subscription = await storage.getSubscription(subscriptionId);

    if (!subscription) {
      return res.status(404).json({ message: 'Assinatura não encontrada' });
    }

    // Calcular novas datas baseadas no plano
    const { currentPeriodStart, currentPeriodEnd } = calculateSubscriptionDates(planType);

    // Atualizar assinatura com novo plano e datas
    const updatedSubscription = await storage.updateSubscription(subscriptionId, {
      planType,
      currentPeriodStart,
      currentPeriodEnd,
      updatedAt: new Date()
    });

    if (!updatedSubscription) {
      return res.status(500).json({ message: 'Erro ao atualizar assinatura' });
    }

    res.json({
      message: 'Plano da assinatura atualizado com sucesso',
      subscription: updatedSubscription
    });
  } catch (error) {
    console.error('Erro ao atualizar plano da assinatura:', error);
    res.status(500).json({
      message: 'Erro ao atualizar plano da assinatura',
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

/**
 * PATCH /api/admin/global/subscriptions/:id/update-status
 * Atualiza o status de uma assinatura
 */
router.patch('/subscriptions/:id/update-status', async (req, res) => {
  try {
    const subscriptionId = parseInt(req.params.id);
    const { status } = req.body;

    if (isNaN(subscriptionId)) {
      return res.status(400).json({ message: 'ID da assinatura inválido' });
    }

    if (!['active', 'past_due', 'canceled', 'unpaid', 'incomplete'].includes(status)) {
      return res.status(400).json({ message: 'Status inválido' });
    }

    console.log('Atualizando status da assinatura:', subscriptionId, 'para:', status);

    const subscription = await storage.getSubscription(subscriptionId);

    if (!subscription) {
      return res.status(404).json({ message: 'Assinatura não encontrada' });
    }

    // Atualizar assinatura
    const updatedSubscription = await storage.updateSubscription(subscriptionId, {
      status,
      updatedAt: new Date()
    });

    if (!updatedSubscription) {
      return res.status(500).json({ message: 'Erro ao atualizar assinatura' });
    }

    res.json({
      message: 'Status da assinatura atualizado com sucesso',
      subscription: updatedSubscription
    });
  } catch (error) {
    console.error('Erro ao atualizar status da assinatura:', error);
    res.status(500).json({
      message: 'Erro ao atualizar status da assinatura',
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

/**
 * PATCH /api/admin/global/users/:id/toggle-admin
 * Promove ou remove permissões de super-administrador
 */
router.patch('/users/:id/toggle-admin', async (req, res) => {
  try {
    const userId = parseInt(req.params.id);
    const { isGlobalAdmin } = req.body;

    if (isNaN(userId)) {
      return res.status(400).json({ message: 'ID do usuário inválido' });
    }

    if (typeof isGlobalAdmin !== 'boolean') {
      return res.status(400).json({ message: 'Status de admin deve ser um valor booleano' });
    }

    console.log('Alterando permissões de admin global do usuário:', userId, 'para:', isGlobalAdmin);

    const user = await storage.getUserById(userId);

    if (!user) {
      return res.status(404).json({ message: 'Usuário não encontrado' });
    }

    // Não permitir que o usuário remova suas próprias permissões
    if (req.user && req.user.id === userId && !isGlobalAdmin) {
      return res.status(400).json({
        message: 'Você não pode remover suas próprias permissões de super-administrador'
      });
    }

    // Atualizar permissões do usuário
    const updatedUser = await storage.updateUser(userId, { isGlobalAdmin });

    res.json({
      message: `Usuário ${isGlobalAdmin ? 'promovido a' : 'removido de'} super-administrador com sucesso`,
      user: {
        id: updatedUser.id,
        email: updatedUser.email,
        fullName: updatedUser.fullName,
        isGlobalAdmin: updatedUser.isGlobalAdmin
      }
    });
  } catch (error) {
    console.error('Erro ao alterar permissões de admin:', error);
    res.status(500).json({
      message: 'Erro ao alterar permissões de admin',
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

/**
 * GET /api/admin/global/settings
 * Retorna todas as configurações globais
 */
router.get('/settings', async (req, res) => {
  try {
    console.log('Buscando configurações globais...');

    const settings = await storage.getAllGlobalSettings();

    res.json({
      settings: settings
    });
  } catch (error) {
    console.error('Erro ao buscar configurações globais:', error);
    res.status(500).json({
      message: 'Erro ao buscar configurações globais',
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

/**
 * GET /api/admin/global/settings/:key
 * Retorna uma configuração específica
 */
router.get('/settings/:key', async (req, res) => {
  try {
    const { key } = req.params;

    console.log('Buscando configuração global:', key);

    const setting = await storage.getGlobalSetting(key);

    if (!setting) {
      return res.status(404).json({ message: 'Configuração não encontrada' });
    }

    res.json(setting);
  } catch (error) {
    console.error('Erro ao buscar configuração global:', error);
    res.status(500).json({
      message: 'Erro ao buscar configuração global',
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

/**
 * PUT /api/admin/global/settings/:key
 * Atualiza uma configuração global
 */
router.put('/settings/:key', async (req, res) => {
  try {
    const { key } = req.params;
    const { value } = req.body;
    const firebaseUid = (req as any).user?.uid;

    if (!value) {
      return res.status(400).json({ message: 'Valor da configuração é obrigatório' });
    }

    console.log('Atualizando configuração global:', key, 'com valor:', value);

    // Buscar usuário para obter o ID
    const user = await storage.getUserByFirebaseUid(firebaseUid);
    const updatedBy = user?.id;

    // Tentar atualizar primeiro
    let setting = await storage.updateGlobalSetting(key, value, updatedBy);

    // Se não existir, criar nova configuração
    if (!setting) {
      setting = await storage.createGlobalSetting(key, value, undefined, updatedBy);
    }

    res.json({
      message: 'Configuração atualizada com sucesso',
      setting
    });
  } catch (error) {
    console.error('Erro ao atualizar configuração global:', error);
    res.status(500).json({
      message: 'Erro ao atualizar configuração global',
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

/**
 * POST /api/admin/global/settings/restore-defaults
 * Restaura as configurações padrão do sistema
 */
router.post('/settings/restore-defaults', async (req, res) => {
  try {
    const firebaseUid = (req as any).user?.uid;

    if (!firebaseUid) {
      return res.status(401).json({ message: 'Usuário não autenticado' });
    }

    console.log('Restaurando configurações padrão...');

    // Buscar usuário para obter o ID
    const user = await storage.getUserByFirebaseUid(firebaseUid);
    const updatedBy = user?.id;

    // Restaurar todas as configurações padrão
    const restoredSettings = [];

    for (const [key, value] of Object.entries(DEFAULT_GLOBAL_SETTINGS)) {
      console.log(`Restaurando configuração: ${key}`);

      // Tentar atualizar primeiro
      let setting = await storage.updateGlobalSetting(key, value, updatedBy);

      // Se não existir, criar nova configuração
      if (!setting) {
        setting = await storage.createGlobalSetting(key, value, undefined, updatedBy);
      }

      if (setting) {
        restoredSettings.push(setting);
      }
    }

    console.log(`${restoredSettings.length} configurações restauradas com sucesso`);

    res.json({
      message: 'Configurações padrão restauradas com sucesso',
      restoredSettings: restoredSettings.length,
      settings: restoredSettings
    });
  } catch (error) {
    console.error('Erro ao restaurar configurações padrão:', error);
    res.status(500).json({
      message: 'Erro ao restaurar configurações padrão',
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

// Iniciar teste grátis para uma loja específica
router.post("/stores/:storeId/start-trial", async (req: Request, res: Response) => {
  console.log('🚀 POST /stores/:storeId/start-trial iniciado');
  console.log('📦 Parâmetros:', req.params);
  console.log('📝 Body:', req.body);

  try {
    const storeId = parseInt(req.params.storeId);
    console.log('🔢 Store ID parseado:', storeId);

    if (isNaN(storeId)) {
      console.log('❌ ID da loja inválido');
      return res.status(400).json({ message: 'ID da loja inválido' });
    }

    console.log('🔍 Verificando se a loja existe...');
    // Verificar se a loja existe
    const store = await storage.getStoreById(storeId);
    if (!store) {
      console.log('❌ Loja não encontrada');
      return res.status(404).json({ message: 'Loja não encontrada' });
    }
    console.log('✅ Loja encontrada:', store.name);

    console.log('👤 Verificando usuário da loja...');
    // Verificar se a loja tem usuário associado
    const user = await storage.getUserById(store.userId);
    if (!user) {
      console.log('❌ Usuário da loja não encontrado');
      return res.status(404).json({ message: 'Usuário da loja não encontrado' });
    }
    console.log('✅ Usuário encontrado:', user.email);

    console.log('📋 Verificando assinatura atual...');
    // Verificar assinatura atual
    const currentSubscription = await storage.getActiveSubscription(storeId);
    if (!currentSubscription) {
      console.log('❌ Assinatura não encontrada');
      return res.status(404).json({ message: 'Assinatura não encontrada' });
    }
    console.log('✅ Assinatura encontrada:', {
      id: currentSubscription.id,
      planType: currentSubscription.planType,
      status: currentSubscription.status,
      trialEnd: currentSubscription.trialEnd
    });

    // Verificar se já é premium
    if (currentSubscription.planType === 'premium') {
      console.log('❌ Loja já possui plano premium');
      return res.status(400).json({ message: 'Loja já possui plano premium' });
    }

    // Verificar se já teve trial
    if (currentSubscription.trialEnd) {
      console.log('❌ Loja já utilizou o período de teste');
      return res.status(400).json({ message: 'Loja já utilizou o período de teste' });
    }

    console.log('💳 Criando sessão de checkout...');
    // Criar sessão de checkout com trial
    const subscriptionService = new SubscriptionService();
    const checkoutUrl = await subscriptionService.createPremiumCheckoutSession(
      storeId,
      user.email || '',
      user.fullName || user.username || 'Usuário',
      'month' // Trial apenas para planos mensais
    );
    console.log('🔗 URL de checkout criada:', checkoutUrl);

    if (!checkoutUrl) {
      console.log('❌ Erro ao criar sessão de checkout');
      return res.status(500).json({ message: 'Erro ao criar sessão de checkout para teste grátis' });
    }

    console.log('✅ Sucesso! Retornando URL de checkout');
    res.json({
      message: 'Sessão de teste grátis criada com sucesso',
      checkoutUrl
    });
  } catch (error) {
    console.error('Erro ao iniciar teste grátis:', error);
    res.status(500).json({
      message: 'Erro ao iniciar teste grátis',
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

/**
 * GET /api/admin/global/debug/subscriptions
 * Debug endpoint para verificar categorização de assinaturas
 */
router.get('/debug/subscriptions', async (req, res) => {
  try {
    console.log('🔍 Iniciando debug de assinaturas...');

    // Buscar todas as assinaturas
    const subscriptions = await storage.getAllSubscriptions();
    const now = new Date();

    console.log(`📋 Total de assinaturas encontradas: ${subscriptions.length}`);

    const debugInfo = subscriptions.map(sub => {
      const isTrialActive = sub.trialEnd && new Date(sub.trialEnd) > now;
      const isTrialExpired = sub.trialEnd && new Date(sub.trialEnd) <= now;

      let category = 'unknown';

      if (sub.planType === 'free') {
        category = 'free';
      } else if (sub.planType === 'premium') {
        if (sub.status === 'active') {
          category = isTrialActive ? 'activeTrial' : 'activePaid';
        } else if (sub.status === 'canceled') {
          category = isTrialExpired ? 'expiredTrialCanceled' : 'canceled';
        } else if (sub.status === 'past_due') {
          category = 'pastDue';
        }
      }

      return {
        id: sub.id,
        storeId: sub.storeId,
        planType: sub.planType,
        status: sub.status,
        trialEnd: sub.trialEnd,
        trialEndFormatted: sub.trialEnd ? new Date(sub.trialEnd).toISOString() : null,
        isTrialActive,
        isTrialExpired,
        category,
        stripeSubscriptionId: sub.stripeSubscriptionId,
        createdAt: sub.createdAt
      };
    });

    // Contar por categoria
    const categoryCounts = debugInfo.reduce((acc, sub) => {
      acc[sub.category] = (acc[sub.category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    res.json({
      totalSubscriptions: subscriptions.length,
      categoryCounts,
      subscriptions: debugInfo,
      currentTime: now.toISOString()
    });

  } catch (error) {
    console.error('Erro no debug de assinaturas:', error);
    res.status(500).json({
      message: 'Erro no debug de assinaturas',
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

export { router as globalAdminRouter };
