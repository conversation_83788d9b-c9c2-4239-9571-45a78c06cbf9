import type { Express, Request, Response } from "express";
import { subscriptionSyncService } from "../subscription-sync-service";
import { runStripeDataRetrievalTest } from "../test-stripe-sync";
import { isAuthenticated, isGlobalAdmin } from "../firebaseAuth";
import { storage } from "../storage";

export function setupSubscriptionSyncRoutes(app: Express) {
  
  // Sincronização manual de uma assinatura específica
  app.post("/api/subscriptions/sync/:subscriptionId", isAuthenticated, async (req: Request, res: Response) => {
    try {
      const { subscriptionId } = req.params;
      const { forceUpdate = false } = req.body;
      const firebaseUid = (req as any).user?.uid;

      console.log(`🔄 Solicitação de sincronização manual: ${subscriptionId}`);

      // Verificar se o usuário tem permissão (deve ser admin global ou dono da loja)
      const isGlobalAdminUser = await isGlobalAdmin(firebaseUid);
      
      if (!isGlobalAdminUser) {
        // Verificar se é dono da loja relacionada à assinatura
        const subscription = await storage.getSubscriptionByStripeId(subscriptionId);
        if (!subscription) {
          return res.status(404).json({ message: "Assinatura não encontrada" });
        }

        const store = await storage.getStore(subscription.storeId);
        if (!store) {
          return res.status(404).json({ message: "Loja não encontrada" });
        }

        const user = await storage.getUserByFirebaseUid(firebaseUid);
        if (!user || store.userId !== user.id) {
          return res.status(403).json({ message: "Acesso negado" });
        }
      }

      const result = await subscriptionSyncService.manualSyncSubscription(subscriptionId, forceUpdate);

      if (result.success) {
        res.json({
          message: "Sincronização concluída com sucesso",
          result
        });
      } else {
        res.status(400).json({
          message: "Falha na sincronização",
          error: result.error
        });
      }

    } catch (error) {
      console.error('Erro na sincronização manual:', error);
      res.status(500).json({
        message: "Erro interno do servidor",
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    }
  });

  // Sincronização em lote para uma loja
  app.post("/api/subscriptions/bulk-sync/:storeId", isAuthenticated, async (req: Request, res: Response) => {
    try {
      const { storeId } = req.params;
      const { forceUpdate = false, maxConcurrent = 3 } = req.body;
      const firebaseUid = (req as any).user?.uid;

      console.log(`🔄 Solicitação de sincronização em lote para loja: ${storeId}`);

      // Verificar permissões
      const isGlobalAdminUser = await isGlobalAdmin(firebaseUid);
      
      if (!isGlobalAdminUser) {
        const store = await storage.getStore(parseInt(storeId));
        if (!store) {
          return res.status(404).json({ message: "Loja não encontrada" });
        }

        const user = await storage.getUserByFirebaseUid(firebaseUid);
        if (!user || store.userId !== user.id) {
          return res.status(403).json({ message: "Acesso negado" });
        }
      }

      const result = await subscriptionSyncService.bulkSyncStore(
        parseInt(storeId), 
        { forceUpdate, maxConcurrent }
      );

      res.json({
        message: "Sincronização em lote concluída",
        result
      });

    } catch (error) {
      console.error('Erro na sincronização em lote:', error);
      res.status(500).json({
        message: "Erro interno do servidor",
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    }
  });

  // Validar e corrigir dados de assinatura
  app.post("/api/subscriptions/validate/:subscriptionId", isAuthenticated, async (req: Request, res: Response) => {
    try {
      const { subscriptionId } = req.params;
      const firebaseUid = (req as any).user?.uid;

      console.log(`🔍 Solicitação de validação: ${subscriptionId}`);

      // Verificar permissões
      const isGlobalAdminUser = await isGlobalAdmin(firebaseUid);
      
      if (!isGlobalAdminUser) {
        const subscription = await storage.getSubscription(parseInt(subscriptionId));
        if (!subscription) {
          return res.status(404).json({ message: "Assinatura não encontrada" });
        }

        const store = await storage.getStore(subscription.storeId);
        if (!store) {
          return res.status(404).json({ message: "Loja não encontrada" });
        }

        const user = await storage.getUserByFirebaseUid(firebaseUid);
        if (!user || store.userId !== user.id) {
          return res.status(403).json({ message: "Acesso negado" });
        }
      }

      const result = await subscriptionSyncService.validateAndFixSubscription(parseInt(subscriptionId));

      if (result.success) {
        res.json({
          message: "Validação concluída",
          result
        });
      } else {
        res.status(400).json({
          message: "Falha na validação",
          error: result.error
        });
      }

    } catch (error) {
      console.error('Erro na validação:', error);
      res.status(500).json({
        message: "Erro interno do servidor",
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    }
  });

  // Obter histórico de sincronização
  app.get("/api/subscriptions/sync-history", isAuthenticated, async (req: Request, res: Response) => {
    try {
      const { limit = 50 } = req.query;
      const firebaseUid = (req as any).user?.uid;

      // Apenas admins globais podem ver todo o histórico
      const isGlobalAdminUser = await isGlobalAdmin(firebaseUid);
      
      if (!isGlobalAdminUser) {
        return res.status(403).json({ message: "Acesso negado" });
      }

      const history = subscriptionSyncService.getSyncHistory(parseInt(limit as string));

      res.json({
        history,
        total: history.length
      });

    } catch (error) {
      console.error('Erro ao obter histórico:', error);
      res.status(500).json({
        message: "Erro interno do servidor",
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    }
  });

  // Obter estatísticas de sincronização
  app.get("/api/subscriptions/sync-stats", isAuthenticated, async (req: Request, res: Response) => {
    try {
      const firebaseUid = (req as any).user?.uid;

      // Apenas admins globais podem ver estatísticas
      const isGlobalAdminUser = await isGlobalAdmin(firebaseUid);
      
      if (!isGlobalAdminUser) {
        return res.status(403).json({ message: "Acesso negado" });
      }

      const stats = subscriptionSyncService.getSyncStats();

      res.json(stats);

    } catch (error) {
      console.error('Erro ao obter estatísticas:', error);
      res.status(500).json({
        message: "Erro interno do servidor",
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    }
  });

  // Endpoint para verificar status de sincronização de uma loja específica
  app.get("/api/subscriptions/sync-status/:storeId", isAuthenticated, async (req: Request, res: Response) => {
    try {
      const { storeId } = req.params;
      const firebaseUid = (req as any).user?.uid;

      // Verificar permissões
      const isGlobalAdminUser = await isGlobalAdmin(firebaseUid);
      
      if (!isGlobalAdminUser) {
        const store = await storage.getStore(parseInt(storeId));
        if (!store) {
          return res.status(404).json({ message: "Loja não encontrada" });
        }

        const user = await storage.getUserByFirebaseUid(firebaseUid);
        if (!user || store.userId !== user.id) {
          return res.status(403).json({ message: "Acesso negado" });
        }
      }

      // Buscar assinaturas da loja
      const subscriptions = await storage.getSubscriptionsByStoreId(parseInt(storeId));
      
      // Validar cada assinatura
      const validationResults = await Promise.all(
        subscriptions.map(async (sub) => {
          const validation = await subscriptionSyncService.validateSubscriptionData(sub);
          return {
            subscriptionId: sub.id,
            stripeSubscriptionId: sub.stripeSubscriptionId,
            planType: sub.planType,
            status: sub.status,
            validation
          };
        })
      );

      const totalSubscriptions = subscriptions.length;
      const validSubscriptions = validationResults.filter(r => r.validation.isValid).length;
      const invalidSubscriptions = totalSubscriptions - validSubscriptions;

      res.json({
        storeId: parseInt(storeId),
        totalSubscriptions,
        validSubscriptions,
        invalidSubscriptions,
        subscriptions: validationResults
      });

    } catch (error) {
      console.error('Erro ao verificar status:', error);
      res.status(500).json({
        message: "Erro interno do servidor",
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    }
  });

  // Endpoint para executar testes de recuperação de dados do Stripe
  app.post("/api/subscriptions/test-stripe-data", isAuthenticated, async (req: Request, res: Response) => {
    try {
      const { subscriptionId } = req.body;
      const firebaseUid = (req as any).user?.uid;

      console.log(`🧪 Solicitação de teste de dados do Stripe`);

      // Verificar se é admin global (apenas admins podem executar testes)
      const isGlobalAdminUser = await isGlobalAdmin(firebaseUid);

      if (!isGlobalAdminUser) {
        return res.status(403).json({ message: "Acesso negado. Apenas administradores globais podem executar testes." });
      }

      console.log(`🚀 Iniciando testes de recuperação de dados do Stripe...`);

      // Executar os testes
      const testResults = await runStripeDataRetrievalTest(subscriptionId);

      // Calcular estatísticas dos testes
      const totalTests = testResults.length;
      const passedTests = testResults.filter(r => r.success).length;
      const failedTests = totalTests - passedTests;
      const successRate = ((passedTests / totalTests) * 100).toFixed(1);

      console.log(`✅ Testes concluídos: ${passedTests}/${totalTests} sucessos (${successRate}%)`);

      res.json({
        message: "Testes de recuperação de dados do Stripe concluídos",
        summary: {
          totalTests,
          passedTests,
          failedTests,
          successRate: parseFloat(successRate)
        },
        results: testResults
      });

    } catch (error) {
      console.error('Erro ao executar testes do Stripe:', error);
      res.status(500).json({
        message: "Erro ao executar testes",
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    }
  });

  // Endpoint para testar sincronização de uma assinatura específica do Stripe
  app.post("/api/subscriptions/test-sync-specific", isAuthenticated, async (req: Request, res: Response) => {
    try {
      const { subscriptionId } = req.body;
      const firebaseUid = (req as any).user?.uid;

      if (!subscriptionId) {
        return res.status(400).json({ message: "ID da assinatura é obrigatório" });
      }

      console.log(`🧪 Teste de sincronização específica: ${subscriptionId}`);

      // Verificar permissões (admin global ou dono da loja)
      const isGlobalAdminUser = await isGlobalAdmin(firebaseUid);

      if (!isGlobalAdminUser) {
        // Verificar se é dono da loja relacionada à assinatura
        const subscription = await storage.getSubscriptionByStripeId(subscriptionId);
        if (subscription) {
          const store = await storage.getStore(subscription.storeId);
          if (store) {
            const user = await storage.getUserByFirebaseUid(firebaseUid);
            if (!user || store.userId !== user.id) {
              return res.status(403).json({ message: "Acesso negado" });
            }
          }
        }
      }

      // Executar sincronização manual com força
      const syncResult = await subscriptionSyncService.manualSyncSubscription(subscriptionId, true);

      res.json({
        message: "Teste de sincronização específica concluído",
        subscriptionId,
        result: syncResult
      });

    } catch (error) {
      console.error('Erro no teste de sincronização específica:', error);
      res.status(500).json({
        message: "Erro no teste de sincronização",
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    }
  });
}
