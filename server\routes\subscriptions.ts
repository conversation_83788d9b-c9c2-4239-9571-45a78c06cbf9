import type { Express, Request, Response } from "express";
import { subscriptionService } from "../subscription-service";
import { subscriptionSyncService } from "../subscription-sync-service";
import { isAuthenticated } from "../firebaseAuth";
import { storage } from "../storage";
import { verifyWebhookSignature, STRIPE_CONFIG } from "../stripe-config";

// Cache para idempotência de webhooks (em produção, usar Redis)
const webhookEventCache = new Map<string, { timestamp: number; processed: boolean }>();
const WEBHOOK_CACHE_TTL = 24 * 60 * 60 * 1000; // 24 horas

// Fila de eventos para retry
interface WebhookEvent {
  id: string;
  type: string;
  data: any;
  attempts: number;
  lastAttempt: Date;
  maxAttempts: number;
}

const eventQueue: WebhookEvent[] = [];
const MAX_RETRY_ATTEMPTS = 3;
const RETRY_DELAY = 5000; // 5 segundos

// Limpar cache antigo periodicamente
setInterval(() => {
  const now = Date.now();
  for (const [key, value] of webhookEventCache.entries()) {
    if (now - value.timestamp > WEBHOOK_CACHE_TTL) {
      webhookEventCache.delete(key);
    }
  }
}, 60 * 60 * 1000); // Limpar a cada hora

export function registerSubscriptionRoutes(app: Express) {
  // Obter informações de uso e limites da loja
  app.get("/api/subscriptions/usage", isAuthenticated, async (req: Request, res: Response) => {
    try {
      const firebaseUid = (req as any).user?.uid;
      console.log('=== ROTA SUBSCRIPTION USAGE ===');
      console.log('firebaseUid recebido:', firebaseUid);
      console.log('req.user:', (req as any).user);

      if (!firebaseUid) {
        console.log('❌ firebaseUid está undefined na rota de subscription');
        return res.status(401).json({ message: "UID do Firebase não encontrado" });
      }

      // Obter loja do usuário
      const store = await storage.getStoreByFirebaseUid(firebaseUid);
      if (!store) {
        console.log('❌ Loja não encontrada para UID:', firebaseUid);
        return res.status(404).json({ message: "Loja não encontrada" });
      }

      const usageInfo = await subscriptionService.getUsageInfo(store.id);
      res.json(usageInfo);
    } catch (error) {
      console.error('Erro ao obter informações de uso:', error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  });

  // Criar sessão de checkout para upgrade para premium
  app.post("/api/subscriptions/create-checkout", isAuthenticated, async (req: Request, res: Response) => {
    try {
      const firebaseUid = (req as any).user?.uid;
      const { interval = 'month' } = req.body; // 'month' ou 'year'

      // Validar intervalo
      if (!['month', 'year'].includes(interval)) {
        return res.status(400).json({ message: "Intervalo inválido. Use 'month' ou 'year'" });
      }

      // Obter usuário e loja
      const user = await storage.getUserByFirebaseUid(firebaseUid);
      const store = await storage.getStoreByFirebaseUid(firebaseUid);

      if (!user || !store) {
        return res.status(404).json({ message: "Usuário ou loja não encontrados" });
      }

      const checkoutUrl = await subscriptionService.createPremiumCheckoutSession(
        store.id,
        user.email || '',
        user.fullName || user.username || 'Usuário',
        interval
      );

      if (!checkoutUrl) {
        return res.status(500).json({ message: "Erro ao criar sessão de checkout" });
      }

      res.json({ checkoutUrl });
    } catch (error) {
      console.error('Erro ao criar checkout:', error);
      res.status(500).json({ 
        message: error instanceof Error ? error.message : "Erro interno do servidor" 
      });
    }
  });

  // Obter URL do Customer Portal
  app.get("/api/subscriptions/portal", isAuthenticated, async (req: Request, res: Response) => {
    try {
      const firebaseUid = (req as any).user?.uid;
      
      // Obter loja do usuário
      const store = await storage.getStoreByFirebaseUid(firebaseUid);
      if (!store) {
        return res.status(404).json({ message: "Loja não encontrada" });
      }

      const portalUrl = await subscriptionService.getCustomerPortalUrl(store.id);
      
      if (!portalUrl) {
        return res.status(400).json({ message: "Customer Portal não disponível" });
      }

      res.json({ portalUrl });
    } catch (error) {
      console.error('Erro ao obter URL do Customer Portal:', error);
      res.status(500).json({ 
        message: error instanceof Error ? error.message : "Erro interno do servidor" 
      });
    }
  });

  // Verificar se uma funcionalidade está disponível
  app.get("/api/subscriptions/feature/:feature", isAuthenticated, async (req: Request, res: Response) => {
    try {
      const firebaseUid = (req as any).user?.uid;
      const { feature } = req.params;
      
      // Obter loja do usuário
      const store = await storage.getStoreByFirebaseUid(firebaseUid);
      if (!store) {
        return res.status(404).json({ message: "Loja não encontrada" });
      }

      const isAvailable = await subscriptionService.isFeatureAvailableForStore(
        store.id, 
        feature as any
      );

      res.json({ available: isAvailable });
    } catch (error) {
      console.error('Erro ao verificar funcionalidade:', error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  });

  // Função para processar eventos de webhook com retry
  async function processWebhookEvent(event: any): Promise<boolean> {
    try {
      switch (event.type) {
        case 'checkout.session.completed':
          const session = event.data.object as any;
          console.log('🛒 Checkout session completed:', session.id);

          if (session.subscription) {
            console.log('🔄 Sincronizando subscription após checkout:', session.subscription);
            await subscriptionSyncService.manualSyncSubscription(session.subscription, true);
          } else {
            console.log('ℹ️ Checkout session sem subscription associada');
          }
          break;

        case 'customer.subscription.created':
        case 'customer.subscription.updated':
        case 'customer.subscription.deleted':
          const subscription = event.data.object as any;
          console.log(`🔄 Sincronizando subscription ${event.type}:`, subscription.id);
          await subscriptionSyncService.manualSyncSubscription(subscription.id, true);
          break;

        case 'invoice.payment_succeeded':
          const invoice = event.data.object as any;
          if (invoice.subscription) {
            console.log('💰 Sincronizando subscription após pagamento bem-sucedido:', invoice.subscription);
            await subscriptionSyncService.manualSyncSubscription(invoice.subscription, true);
          }
          break;

        case 'invoice.payment_failed':
          const failedInvoice = event.data.object as any;
          if (failedInvoice.subscription) {
            console.log('❌ Sincronizando subscription após falha no pagamento:', failedInvoice.subscription);
            await subscriptionSyncService.manualSyncSubscription(failedInvoice.subscription, true);
          }
          break;

        default:
          console.log(`ℹ️ Evento não processado: ${event.type}`);
          return true; // Não é erro, apenas não processamos este tipo
      }

      return true;
    } catch (error) {
      console.error(`❌ Erro ao processar evento ${event.type}:`, error);
      return false;
    }
  }

  // Função para processar fila de retry
  async function processEventQueue() {
    const now = new Date();
    const eventsToRetry = eventQueue.filter(e =>
      e.attempts < e.maxAttempts &&
      (now.getTime() - e.lastAttempt.getTime()) > RETRY_DELAY
    );

    for (const event of eventsToRetry) {
      console.log(`🔄 Tentativa ${event.attempts + 1}/${event.maxAttempts} para evento ${event.id}`);

      const success = await processWebhookEvent(event);
      event.attempts++;
      event.lastAttempt = now;

      if (success) {
        // Remover da fila se bem-sucedido
        const index = eventQueue.indexOf(event);
        if (index > -1) {
          eventQueue.splice(index, 1);
        }
        console.log(`✅ Evento ${event.id} processado com sucesso na tentativa ${event.attempts}`);
      } else if (event.attempts >= event.maxAttempts) {
        // Remover da fila se excedeu tentativas
        const index = eventQueue.indexOf(event);
        if (index > -1) {
          eventQueue.splice(index, 1);
        }
        console.error(`❌ Evento ${event.id} falhou após ${event.maxAttempts} tentativas`);
      }
    }
  }

  // Processar fila de retry a cada 30 segundos
  setInterval(processEventQueue, 30000);

  // Webhook do Stripe para sincronizar eventos
  app.post("/api/subscriptions/webhook", async (req: Request, res: Response) => {
    try {
      const signature = req.headers['stripe-signature'] as string;

      if (!signature) {
        console.error('❌ Assinatura do webhook ausente');
        return res.status(400).json({ message: "Assinatura do webhook ausente" });
      }

      // Verificar assinatura do webhook
      const event = verifyWebhookSignature(req.body, signature);

      if (!event) {
        console.error('❌ Assinatura do webhook inválida');
        return res.status(400).json({ message: "Assinatura do webhook inválida" });
      }

      console.log(`📨 Webhook recebido: ${event.type} (ID: ${event.id})`);

      // Verificar idempotência
      const cacheKey = `webhook_${event.id}`;
      const cached = webhookEventCache.get(cacheKey);

      if (cached && cached.processed) {
        console.log(`ℹ️ Evento ${event.id} já foi processado (idempotência)`);
        return res.json({ received: true, cached: true });
      }

      // Marcar como recebido no cache
      webhookEventCache.set(cacheKey, { timestamp: Date.now(), processed: false });

      // Tentar processar imediatamente
      const success = await processWebhookEvent(event);

      if (success) {
        // Marcar como processado no cache
        webhookEventCache.set(cacheKey, { timestamp: Date.now(), processed: true });
        console.log(`✅ Evento ${event.id} processado com sucesso`);
        res.json({ received: true, processed: true });
      } else {
        // Adicionar à fila de retry
        const queueEvent: WebhookEvent = {
          id: event.id,
          type: event.type,
          data: event.data,
          attempts: 0,
          lastAttempt: new Date(),
          maxAttempts: MAX_RETRY_ATTEMPTS
        };

        eventQueue.push(queueEvent);
        console.log(`⏳ Evento ${event.id} adicionado à fila de retry`);

        // Retornar sucesso para o Stripe (evitar reenvios desnecessários)
        res.json({ received: true, queued: true });
      }

    } catch (error) {
      console.error('❌ Erro no webhook:', error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  });

  // Verificar se um limite foi excedido
  app.get("/api/subscriptions/limit/:feature/:count", isAuthenticated, async (req: Request, res: Response) => {
    try {
      const firebaseUid = (req as any).user?.uid;
      const { feature, count } = req.params;
      
      // Obter loja do usuário
      const store = await storage.getStoreByFirebaseUid(firebaseUid);
      if (!store) {
        return res.status(404).json({ message: "Loja não encontrada" });
      }

      const isExceeded = await subscriptionService.isLimitExceededForStore(
        store.id, 
        feature as any,
        parseInt(count)
      );

      res.json({ exceeded: isExceeded });
    } catch (error) {
      console.error('Erro ao verificar limite:', error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  });

  // Obter status da assinatura
  app.get("/api/subscriptions/status", isAuthenticated, async (req: Request, res: Response) => {
    try {
      const firebaseUid = (req as any).user?.uid;
      
      // Obter loja do usuário
      const store = await storage.getStoreByFirebaseUid(firebaseUid);
      if (!store) {
        return res.status(404).json({ message: "Loja não encontrada" });
      }

      const subscription = await subscriptionService.getActiveSubscription(store.id);
      
      if (!subscription) {
        // Criar assinatura gratuita se não existir
        const newSubscription = await subscriptionService.createFreeSubscription(store.id);
        return res.json(newSubscription);
      }

      res.json(subscription);
    } catch (error) {
      console.error('Erro ao obter status da assinatura:', error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  });

  // Processar trials expirados (endpoint para admin global)
  app.post("/api/subscriptions/process-expired-trials", isAuthenticated, async (req: Request, res: Response) => {
    try {
      const firebaseUid = (req as any).user?.uid;

      // Verificar se é admin global
      const user = await storage.getUserByFirebaseUid(firebaseUid);
      if (!user?.isGlobalAdmin) {
        return res.status(403).json({ message: "Acesso negado - apenas admins globais" });
      }

      console.log(`🔄 Iniciando processamento de trials expirados por admin ${user.username}`);
      const processedCount = await subscriptionService.processExpiredTrials();

      res.json({
        message: "Processamento de trials expirados concluído",
        processedCount,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Erro ao processar trials expirados:', error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  });

  // Verificar saúde da sincronização com Stripe (endpoint para admin global)
  app.get("/api/subscriptions/stripe-health", isAuthenticated, async (req: Request, res: Response) => {
    try {
      const firebaseUid = (req as any).user?.uid;

      // Verificar se é admin global
      const user = await storage.getUserByFirebaseUid(firebaseUid);
      if (!user?.isGlobalAdmin) {
        return res.status(403).json({ message: "Acesso negado - apenas admins globais" });
      }

      const healthStatus = await subscriptionService.checkStripeSyncHealth();

      res.json({
        ...healthStatus,
        message: healthStatus.isHealthy ? "Sistema saudável" : "Inconsistências detectadas"
      });
    } catch (error) {
      console.error('Erro ao verificar saúde do Stripe:', error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  });
}
