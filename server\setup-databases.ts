import { exec } from 'child_process';
import { promisify } from 'util';
import fs from 'fs';
import path from 'path';
import 'dotenv/config';

const execAsync = promisify(exec);

async function setupDatabases() {
  console.log('Iniciando configuração dos bancos de dados...');
  
  // Verificar variáveis de ambiente do Supabase
  const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL;
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseKey) {
    console.warn('\n\nATENÇÃO: Variáveis de ambiente do Supabase não encontradas.');
    console.warn('SUPABASE_URL e SUPABASE_SERVICE_ROLE_KEY devem estar definidas no arquivo .env');
    console.warn('Continuando apenas com a configuração do banco de dados local.\n\n');
  }
  
  // Verificar variável DATABASE_URL
  const databaseUrl = process.env.DATABASE_URL;
  if (!databaseUrl) {
    console.error('\n\nERRO: Variável de ambiente DATABASE_URL não encontrada.');
    console.error('Execute `replit:create-postgresql-database` para criar um banco de dados PostgreSQL local.');
    process.exit(1);
  }
  
  try {
    console.log('\n\n1. Configurando banco de dados PostgreSQL local...');
    
    // Executar script para criar tabelas localmente
    console.log('Executando script create-local-tables.ts...');
    try {
      const { stdout, stderr } = await execAsync('npx tsx server/create-local-tables.ts');
      console.log('Saída do script create-local-tables.ts:');
      console.log(stdout);
      if (stderr) console.error('Erros:', stderr);
      console.log('\nTabelas criadas com sucesso no banco de dados local!\n');
    } catch (localError: any) {
      console.error('\nERRO ao criar tabelas localmente:', localError.message);
      if (localError.stdout) console.log('Saída:', localError.stdout);
      if (localError.stderr) console.error('Erros:', localError.stderr);
      console.error('Continuando para a próxima etapa...\n');
    }
    
    // Se as variáveis do Supabase estiverem definidas, configurar no Supabase também
    if (supabaseUrl && supabaseKey) {
      console.log('\n\n2. Configurando banco de dados no Supabase...');
      
      // Executar script para criar tabelas no Supabase
      console.log('Executando script create-supabase-tables.ts...');
      try {
        const { stdout, stderr } = await execAsync('npx tsx server/create-supabase-tables.ts');
        console.log('Saída do script create-supabase-tables.ts:');
        console.log(stdout);
        if (stderr) console.error('Erros:', stderr);
        console.log('\nConfiguração básica do Supabase concluída!\n');
      } catch (supabaseError: any) {
        console.error('\nERRO ao configurar Supabase:', supabaseError.message);
        if (supabaseError.stdout) console.log('Saída:', supabaseError.stdout);
        if (supabaseError.stderr) console.error('Erros:', supabaseError.stderr);
      }
    } else {
      console.log('\n\n2. Configuração do Supabase ignorada devido à falta de variáveis de ambiente.');
    }
    
    console.log('\n\nConfiguração de banco de dados concluída!\n');
    
  } catch (error: any) {
    console.error('\n\nERRO durante o processo de configuração dos bancos de dados:', error.message);
    process.exit(1);
  }
}

// Executar a função principal
setupDatabases().then(() => {
  console.log('Script finalizado.');
}).catch(error => {
  console.error('Erro fatal:', error);
  process.exit(1);
});
