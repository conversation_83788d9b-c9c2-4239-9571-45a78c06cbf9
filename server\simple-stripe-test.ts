import dotenv from 'dotenv';
import { stripe, isStripeConfigured, getSubscription, STRIPE_CONFIG } from './stripe-config';

// Load environment variables
dotenv.config();

async function testStripeDataRetrieval() {
  console.log('🚀 Iniciando teste simples de recuperação de dados do Stripe...\n');
  console.log('🔧 Verificando variáveis de ambiente...');
  console.log(`STRIPE_SECRET_KEY: ${process.env.STRIPE_SECRET_KEY ? 'Configurada' : 'Não configurada'}`);
  console.log(`STRIPE_WEBHOOK_SECRET: ${process.env.STRIPE_WEBHOOK_SECRET ? 'Configurada' : 'Não configurada'}`);
  console.log('');

  try {
    // Teste 1: Verificar configuração do Stripe
    console.log('🧪 Teste 1: Verificando configuração do Stripe...');
    
    if (!isStripeConfigured()) {
      console.error('❌ Stripe não está configurado corretamente');
      console.log('Verifique as seguintes variáveis de ambiente:');
      console.log('- STRIPE_SECRET_KEY');
      console.log('- STRIPE_WEBHOOK_SECRET');
      console.log('- STRIPE_PREMIUM_MONTHLY_PRICE_ID');
      console.log('- STRIPE_PREMIUM_YEARLY_PRICE_ID');
      return;
    }

    console.log('✅ Stripe configurado corretamente');
    console.log('📋 Configuração:');
    console.log(`  - Success URL: ${STRIPE_CONFIG.successUrl}`);
    console.log(`  - Cancel URL: ${STRIPE_CONFIG.cancelUrl}`);
    console.log(`  - Premium Monthly Price ID: ${STRIPE_CONFIG.premiumMonthlyPriceId}`);
    console.log(`  - Premium Yearly Price ID: ${STRIPE_CONFIG.premiumYearlyPriceId}`);

    // Teste 2: Testar conexão com Stripe API
    console.log('\n🧪 Teste 2: Testando conexão com Stripe API...');
    
    if (!stripe) {
      console.error('❌ Cliente Stripe não inicializado');
      return;
    }

    const products = await stripe.products.list({ limit: 3 });
    console.log(`✅ Conexão estabelecida com sucesso`);
    console.log(`📦 Produtos encontrados: ${products.data.length}`);
    
    if (products.data.length > 0) {
      console.log('📋 Primeiros produtos:');
      products.data.forEach((product, index) => {
        console.log(`  ${index + 1}. ${product.name} (${product.id})`);
      });
    }

    // Teste 3: Listar assinaturas
    console.log('\n🧪 Teste 3: Listando assinaturas do Stripe...');
    
    const subscriptions = await stripe.subscriptions.list({
      limit: 5,
      expand: ['data.customer', 'data.items.data.price']
    });

    console.log(`📋 Assinaturas encontradas: ${subscriptions.data.length}`);
    
    if (subscriptions.data.length === 0) {
      console.log('ℹ️ Nenhuma assinatura encontrada no Stripe');
      console.log('💡 Para testar completamente, crie uma assinatura de teste no Stripe Dashboard');
    } else {
      console.log('📊 Detalhes das assinaturas:');
      
      subscriptions.data.forEach((sub, index) => {
        console.log(`\n  ${index + 1}. Assinatura: ${sub.id}`);
        console.log(`     Status: ${sub.status}`);
        console.log(`     Cliente: ${typeof sub.customer === 'string' ? sub.customer : sub.customer?.id}`);
        console.log(`     Trial End: ${sub.trial_end ? new Date(sub.trial_end * 1000).toISOString() : 'Não'}`);
        console.log(`     Período Atual: ${new Date(sub.current_period_start * 1000).toISOString()} - ${new Date(sub.current_period_end * 1000).toISOString()}`);
        console.log(`     Cancelar no fim: ${sub.cancel_at_period_end ? 'Sim' : 'Não'}`);
        
        if (sub.items.data.length > 0) {
          console.log(`     Plano: ${sub.items.data[0].price.id}`);
        }
        
        if (Object.keys(sub.metadata).length > 0) {
          console.log(`     Metadata:`, sub.metadata);
        }
      });

      // Teste 4: Testar recuperação de assinatura específica
      console.log('\n🧪 Teste 4: Testando recuperação de assinatura específica...');
      
      const firstSubscriptionId = subscriptions.data[0].id;
      console.log(`🔍 Recuperando assinatura: ${firstSubscriptionId}`);
      
      const subscription = await getSubscription(firstSubscriptionId);
      
      if (subscription) {
        console.log('✅ Assinatura recuperada com sucesso');
        console.log('📄 Dados detalhados:');
        console.log(`  ID: ${subscription.id}`);
        console.log(`  Status: ${subscription.status}`);
        console.log(`  Cliente: ${subscription.customer}`);
        console.log(`  Trial Start: ${subscription.trial_start ? new Date(subscription.trial_start * 1000).toISOString() : 'Não'}`);
        console.log(`  Trial End: ${subscription.trial_end ? new Date(subscription.trial_end * 1000).toISOString() : 'Não'}`);
        console.log(`  Período Atual: ${new Date(subscription.current_period_start * 1000).toISOString()} - ${new Date(subscription.current_period_end * 1000).toISOString()}`);
        console.log(`  Cancelar no fim: ${subscription.cancel_at_period_end ? 'Sim' : 'Não'}`);
        
        if (subscription.items.data.length > 0) {
          console.log('  Itens da assinatura:');
          subscription.items.data.forEach((item, idx) => {
            console.log(`    ${idx + 1}. Price ID: ${item.price.id}`);
            console.log(`       Product ID: ${item.price.product}`);
            console.log(`       Quantidade: ${item.quantity}`);
          });
        }
        
        if (Object.keys(subscription.metadata).length > 0) {
          console.log('  Metadata:', subscription.metadata);
        }
      } else {
        console.error('❌ Falha ao recuperar assinatura específica');
      }
    }

    // Teste 5: Listar clientes
    console.log('\n🧪 Teste 5: Listando clientes do Stripe...');
    
    const customers = await stripe.customers.list({ limit: 3 });
    console.log(`👥 Clientes encontrados: ${customers.data.length}`);
    
    if (customers.data.length > 0) {
      console.log('📋 Primeiros clientes:');
      customers.data.forEach((customer, index) => {
        console.log(`  ${index + 1}. ${customer.email || customer.id}`);
        console.log(`     Nome: ${customer.name || 'Não informado'}`);
        console.log(`     Criado: ${new Date(customer.created * 1000).toISOString()}`);
        if (Object.keys(customer.metadata).length > 0) {
          console.log(`     Metadata:`, customer.metadata);
        }
      });
    }

    // Teste 6: Verificar preços configurados
    console.log('\n🧪 Teste 6: Verificando preços configurados...');
    
    const monthlyPriceId = STRIPE_CONFIG.premiumMonthlyPriceId;
    const yearlyPriceId = STRIPE_CONFIG.premiumYearlyPriceId;
    
    if (monthlyPriceId) {
      try {
        const monthlyPrice = await stripe.prices.retrieve(monthlyPriceId);
        console.log(`✅ Preço mensal encontrado: ${monthlyPrice.id}`);
        console.log(`   Valor: ${monthlyPrice.unit_amount ? (monthlyPrice.unit_amount / 100) : 'N/A'} ${monthlyPrice.currency?.toUpperCase()}`);
        console.log(`   Intervalo: ${monthlyPrice.recurring?.interval}`);
      } catch (error) {
        console.error(`❌ Erro ao buscar preço mensal: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
      }
    }
    
    if (yearlyPriceId) {
      try {
        const yearlyPrice = await stripe.prices.retrieve(yearlyPriceId);
        console.log(`✅ Preço anual encontrado: ${yearlyPrice.id}`);
        console.log(`   Valor: ${yearlyPrice.unit_amount ? (yearlyPrice.unit_amount / 100) : 'N/A'} ${yearlyPrice.currency?.toUpperCase()}`);
        console.log(`   Intervalo: ${yearlyPrice.recurring?.interval}`);
      } catch (error) {
        console.error(`❌ Erro ao buscar preço anual: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
      }
    }

    console.log('\n🎉 Todos os testes de recuperação de dados do Stripe foram concluídos!');
    console.log('✅ A integração com o Stripe está funcionando corretamente.');
    
  } catch (error) {
    console.error('\n💥 Erro durante os testes:', error);
    
    if (error instanceof Error) {
      console.error('Mensagem:', error.message);
      if (error.stack) {
        console.error('Stack trace:', error.stack);
      }
    }
    
    console.log('\n🔧 Possíveis soluções:');
    console.log('1. Verifique se as variáveis de ambiente do Stripe estão configuradas');
    console.log('2. Confirme se a chave secreta do Stripe é válida');
    console.log('3. Verifique se há conectividade com a internet');
    console.log('4. Confirme se a conta Stripe está ativa');
  }
}

// Executar o teste automaticamente
console.log('🎯 Executando teste de dados do Stripe...');
testStripeDataRetrieval()
  .then(() => {
    console.log('\n✅ Teste concluído com sucesso!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ Teste falhou:', error);
    process.exit(1);
  });

export { testStripeDataRetrieval };
