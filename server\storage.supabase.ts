import { supabase } from './db';
import { IStorage } from './storage';
import {
  type User, type InsertUser, type UpsertUser, type Store, type InsertStore, type Category, type InsertCategory,
  type Product, type InsertProduct, type Customer, type InsertCustomer, type Order, type InsertOrder,
  type OrderItem, type InsertOrderItem, type StoreVisit, type InsertStoreVisit,
  type ProductVariation, type InsertProductVariation, type VariationOption, type InsertVariationOption,
  type CartItem, type InsertCartItem, type OrderRevision, type InsertOrderRevision,
  type OrderRevisionItem, type InsertOrderRevisionItem, type Coupon, type InsertCoupon,
  type CouponUsage, type InsertCouponUsage, type OrderPayment, type InsertOrderPayment,
  type Subscription, type InsertSubscription, type GlobalSetting, type GlobalSettingsValue
} from '@shared/schema';

export class SupabaseStorage implements IStorage {

  // User methods - updated for Firebase Auth
  async getUser(id: number): Promise<User | undefined> {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', id)
      .single();

    if (error || !data) return undefined;
    return this.mapUserFromSupabase(data);
  }

  // Alias para getUser
  async getUserById(id: number): Promise<User | undefined> {
    return this.getUser(id);
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    if (!email) return undefined;

    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('email', email)
      .single();

    if (error || !data) return undefined;
    return this.mapUserFromSupabase(data);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('username', username)
      .single();

    if (error || !data) return undefined;
    return data as User;
  }

  async getUserByFirebaseUid(firebaseUid: string): Promise<User | undefined> {
    console.log('DEBUG - getUserByFirebaseUid chamado com:', firebaseUid);
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('firebase_uid', firebaseUid)
      .single();

    console.log('DEBUG - Dados brutos do Supabase:', data);
    console.log('DEBUG - Erro do Supabase:', error);

    if (error || !data) return undefined;

    const mappedUser = this.mapUserFromSupabase(data);
    console.log('DEBUG - Usuário mapeado:', mappedUser);

    return mappedUser;
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const { data, error } = await supabase
      .from('users')
      .insert({
        username: insertUser.username,
        email: insertUser.email,
        password: insertUser.password,
        full_name: insertUser.fullName,
        first_name: insertUser.firstName,
        last_name: insertUser.lastName,
        bio: insertUser.bio,
        profile_image_url: insertUser.profileImageUrl,
        firebase_uid: insertUser.firebaseUid,
        created_at: new Date(),
        updated_at: new Date()
      })
      .select('*')
      .single();

    if (error) {
      console.error('Erro ao criar usuário:', error);
      throw error;
    }

    return this.mapUserFromSupabase(data);
  }

  async createUserWithFirebaseUid(userData: Partial<User> & { fullName?: string }): Promise<User> {
    // Gera uma sequência numérica para id se não for fornecido
    const fullName = userData.fullName || userData.username || `Usuario`;

    const { data, error } = await supabase
      .from('users')
      .insert({
        username: userData.username || `user-${Date.now()}`,
        email: userData.email,
        first_name: userData.firstName,
        last_name: userData.lastName,
        bio: userData.bio,
        profile_image_url: userData.profileImageUrl,
        firebase_uid: userData.firebaseUid,
        full_name: fullName, // Campo obrigatório
        created_at: new Date(),
        updated_at: new Date()
      })
      .select('*')
      .single();

    if (error) {
      console.error('Erro ao criar usuário com Firebase UID:', error);
      throw error;
    }

    return this.mapUserFromSupabase(data);
  }

  async upsertUser(userData: UpsertUser): Promise<User> {
    const { data, error } = await supabase
      .from('users')
      .upsert({
        id: userData.id,
        username: userData.username,
        email: userData.email,
        password: userData.password,
        full_name: userData.fullName,
        first_name: userData.firstName,
        last_name: userData.lastName,
        bio: userData.bio,
        profile_image_url: userData.profileImageUrl,
        firebase_uid: userData.firebaseUid,
        updated_at: new Date()
      })
      .select('*')
      .single();

    if (error) {
      console.error('Erro ao upsert usuário:', error);
      throw error;
    }

    return this.mapUserFromSupabase(data);
  }

  async updateUser(id: number, userData: Partial<User>): Promise<User | undefined> {
    const updateData: any = {};

    // Mapear campos para o formato snake_case do Supabase
    if (userData.username) updateData.username = userData.username;
    if (userData.email) updateData.email = userData.email;
    if (userData.firstName) updateData.first_name = userData.firstName;
    if (userData.lastName) updateData.last_name = userData.lastName;
    if (userData.fullName) updateData.full_name = userData.fullName;
    if (userData.bio) updateData.bio = userData.bio;
    if (userData.profileImageUrl) updateData.profile_image_url = userData.profileImageUrl;
    if (userData.isGlobalAdmin !== undefined) updateData.is_global_admin = userData.isGlobalAdmin;

    updateData.updated_at = new Date();

    const { data, error } = await supabase
      .from('users')
      .update(updateData)
      .eq('id', id)
      .select('*')
      .single();

    if (error || !data) {
      console.error('Erro ao atualizar usuário por ID:', error);
      return undefined;
    }

    return this.mapUserFromSupabase(data);
  }

  async updateUserByFirebaseUid(firebaseUid: string, userData: Partial<User>): Promise<User | undefined> {
    const updateData: any = {};

    // Mapear campos para o formato snake_case do Supabase
    if (userData.username) updateData.username = userData.username;
    if (userData.email) updateData.email = userData.email;
    if (userData.firstName) updateData.first_name = userData.firstName;
    if (userData.lastName) updateData.last_name = userData.lastName;
    if (userData.fullName) updateData.full_name = userData.fullName;
    if (userData.bio) updateData.bio = userData.bio;
    if (userData.profileImageUrl) updateData.profile_image_url = userData.profileImageUrl;

    updateData.updated_at = new Date();

    const { data, error } = await supabase
      .from('users')
      .update(updateData)
      .eq('firebase_uid', firebaseUid)
      .select('*')
      .single();

    if (error || !data) {
      console.error('Erro ao atualizar usuário por Firebase UID:', error);
      return undefined;
    }

    return this.mapUserFromSupabase(data);
  }

  // Helper para mapear dados do formato do Supabase para o nosso schema
  private mapUserFromSupabase(data: any): User {
    return {
      id: data.id,
      username: data.username,
      email: data.email,
      password: data.password,
      fullName: data.full_name,
      firstName: data.first_name,
      lastName: data.last_name,
      bio: data.bio,
      profileImageUrl: data.profile_image_url,
      firebaseUid: data.firebase_uid,
      isGlobalAdmin: data.is_global_admin || false,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at)
    };
  }

  // Store methods
  async getStore(id: number): Promise<Store | undefined> {
    const { data, error } = await supabase
      .from('stores')
      .select('*')
      .eq('id', id)
      .single();

    if (error || !data) return undefined;
    return this.mapStoreFromSupabase(data);
  }

  async getStoreBySlug(slug: string): Promise<Store | undefined> {
    const { data, error } = await supabase
      .from('stores')
      .select('*')
      .eq('slug', slug)
      .single();

    if (error || !data) return undefined;
    return this.mapStoreFromSupabase(data);
  }

  async getStoreByUserId(userId: number): Promise<Store | undefined> {
    const { data, error } = await supabase
      .from('stores')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error || !data) return undefined;
    return this.mapStoreFromSupabase(data);
  }

  async getStoreByFirebaseUid(firebaseUid: string): Promise<Store | undefined> {
    try {
      console.log(`Looking for store with Firebase UID: ${firebaseUid}`);

      // Primeiro obtemos o ID do usuário com base no Firebase UID
      const user = await this.getUserByFirebaseUid(firebaseUid);

      if (!user) {
        console.log(`Usuário não encontrado para Firebase UID: ${firebaseUid}`);
        return undefined;
      }

      console.log(`Encontrado usuário com ID: ${user.id} para Firebase UID: ${firebaseUid}`);

      // Agora buscamos a loja pelo ID do usuário
      const store = await this.getStoreByUserId(user.id);

      if (!store) {
        console.log(`Loja não encontrada para usuário com ID: ${user.id}`);
      } else {
        console.log(`Encontrada loja com ID: ${store.id} para usuário com ID: ${user.id}`);
      }

      return store;
    } catch (error) {
      console.error('Erro ao buscar loja por Firebase UID:', error);
      throw error;
    }
  }

  // Alias para getStore
  async getStoreById(id: number): Promise<Store | undefined> {
    return this.getStore(id);
  }

  async getAllStores(): Promise<Store[]> {
    const { data, error } = await supabase
      .from('stores')
      .select('*')
      .order('created_at', { ascending: false });

    if (error || !data) {
      console.error('Erro ao buscar todas as lojas:', error);
      return [];
    }

    return data.map(this.mapStoreFromSupabase.bind(this));
  }

  async createStore(insertStore: InsertStore): Promise<Store> {
    const { data, error } = await supabase
      .from('stores')
      .insert({
        name: insertStore.name,
        slug: insertStore.slug,
        description: insertStore.description,
        logo: insertStore.logo,
        header_image: insertStore.headerImage,
        country_code: insertStore.countryCode,
        whatsapp: insertStore.whatsapp,
        instagram: insertStore.instagram,
        currency: insertStore.currency,
        user_id: insertStore.userId,
        colors: insertStore.colors,
        payment_methods: insertStore.paymentMethods,
        delivery_settings: insertStore.deliverySettings,
        layout_type: insertStore.layout,
        layout_settings: insertStore.layoutSettings,
        created_at: new Date(),
        updated_at: new Date()
      })
      .select('*')
      .single();

    if (error) {
      console.error('Erro ao criar loja:', error);
      throw error;
    }

    return this.mapStoreFromSupabase(data);
  }

  async updateStore(id: number, storeUpdate: Partial<Store>): Promise<Store | undefined> {
    const updateData: any = {};

    // Mapear campos para o formato snake_case do Supabase
    if (storeUpdate.name !== undefined) updateData.name = storeUpdate.name;
    if (storeUpdate.slug !== undefined) updateData.slug = storeUpdate.slug;
    if (storeUpdate.description !== undefined) updateData.description = storeUpdate.description;
    if (storeUpdate.logo !== undefined) updateData.logo = storeUpdate.logo;
    if (storeUpdate.headerImage !== undefined) updateData.header_image = storeUpdate.headerImage;
    if (storeUpdate.countryCode !== undefined) updateData.country_code = storeUpdate.countryCode;
    if (storeUpdate.whatsapp !== undefined) updateData.whatsapp = storeUpdate.whatsapp;
    if (storeUpdate.instagram !== undefined) updateData.instagram = storeUpdate.instagram;
    if (storeUpdate.currency !== undefined) updateData.currency = storeUpdate.currency;
    if (storeUpdate.colors !== undefined) updateData.colors = storeUpdate.colors;
    if (storeUpdate.paymentMethods !== undefined) updateData.payment_methods = storeUpdate.paymentMethods;
    if (storeUpdate.deliverySettings !== undefined) updateData.delivery_settings = storeUpdate.deliverySettings;
    if (storeUpdate.layout !== undefined) updateData.layout_type = storeUpdate.layout;
    if (storeUpdate.layoutSettings !== undefined) updateData.layout_settings = storeUpdate.layoutSettings;

    // Campos de endereço da loja e email de contato
    if (storeUpdate.addressStreet !== undefined) updateData.address_street = storeUpdate.addressStreet;
    if (storeUpdate.addressNumber !== undefined) updateData.address_number = storeUpdate.addressNumber;
    if (storeUpdate.addressComplement !== undefined) updateData.address_complement = storeUpdate.addressComplement;
    if (storeUpdate.addressNeighborhood !== undefined) updateData.address_neighborhood = storeUpdate.addressNeighborhood;
    if (storeUpdate.addressCity !== undefined) updateData.address_city = storeUpdate.addressCity;
    if (storeUpdate.addressState !== undefined) updateData.address_state = storeUpdate.addressState;
    if (storeUpdate.contactEmail !== undefined) updateData.contact_email = storeUpdate.contactEmail;

    updateData.updated_at = new Date();

    const { data, error } = await supabase
      .from('stores')
      .update(updateData)
      .eq('id', id)
      .select('*')
      .single();

    if (error || !data) {
      console.error('Erro ao atualizar loja:', error);
      return undefined;
    }

    return this.mapStoreFromSupabase(data);
  }

  private mapStoreFromSupabase(data: any): Store {
    return {
      id: data.id,
      name: data.name,
      slug: data.slug,
      description: data.description,
      logo: data.logo,
      headerImage: data.header_image,
      countryCode: data.country_code,
      whatsapp: data.whatsapp,
      instagram: data.instagram,
      currency: data.currency,
      colors: data.colors,
      paymentMethods: data.payment_methods,
      userId: data.user_id,
      layout: data.layout_type,
      layoutSettings: data.layout_settings,
      deliverySettings: data.delivery_settings,
      // Campos de endereço da loja e email de contato
      addressStreet: data.address_street,
      addressNumber: data.address_number,
      addressComplement: data.address_complement,
      addressNeighborhood: data.address_neighborhood,
      addressCity: data.address_city,
      addressState: data.address_state,
      contactEmail: data.contact_email,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at)
    };
  }

  // Implementação dos demais métodos.
  // Como temos uma grande quantidade de métodos, a implementação completa seria muito extensa.
  // Vou implementar os métodos básicos mais utilizados e adicionar stubs para o restante.

  // Product methods
  async getProduct(id: number): Promise<Product | undefined> {
    const { data, error } = await supabase
      .from('products')
      .select('*')
      .eq('id', id)
      .single();

    if (error || !data) return undefined;
    return this.mapProductFromSupabase(data);
  }

  async getProductsByStoreId(storeId: number): Promise<Product[]> {
    const { data, error } = await supabase
      .from('products')
      .select('*')
      .eq('store_id', storeId);

    if (error || !data) return [];
    return data.map(this.mapProductFromSupabase);
  }

  async getProductsByCategoryId(categoryId: number): Promise<Product[]> {
    const { data, error } = await supabase
      .from('products')
      .select('*')
      .eq('category_id', categoryId);

    if (error || !data) return [];
    return data.map(this.mapProductFromSupabase);
  }

  async createProduct(insertProduct: InsertProduct): Promise<Product> {
    const { data, error } = await supabase
      .from('products')
      .insert({
        name: insertProduct.name,
        description: insertProduct.description,
        price: insertProduct.price,
        images: insertProduct.images,
        in_stock: insertProduct.inStock,
        has_variations: insertProduct.hasVariations,
        variations: insertProduct.variations,
        store_id: insertProduct.storeId,
        category_id: insertProduct.categoryId,
        visible: insertProduct.visible,
        created_at: new Date()
      })
      .select('*')
      .single();

    if (error) {
      console.error('Erro ao criar produto:', error);
      throw error;
    }

    return this.mapProductFromSupabase(data);
  }

  async updateProduct(id: number, productUpdate: Partial<Product>): Promise<Product | undefined> {
    const updateData: any = {};

    // Mapear campos para o formato snake_case do Supabase
    if (productUpdate.name !== undefined) updateData.name = productUpdate.name;
    if (productUpdate.description !== undefined) updateData.description = productUpdate.description;
    if (productUpdate.price !== undefined) updateData.price = productUpdate.price;
    if (productUpdate.images !== undefined) updateData.images = productUpdate.images;
    if (productUpdate.inStock !== undefined) updateData.in_stock = productUpdate.inStock;
    if (productUpdate.hasVariations !== undefined) updateData.has_variations = productUpdate.hasVariations;
    if (productUpdate.variations !== undefined) updateData.variations = productUpdate.variations;
    if (productUpdate.categoryId !== undefined) updateData.category_id = productUpdate.categoryId;
    if (productUpdate.visible !== undefined) updateData.visible = productUpdate.visible;

    const { data, error } = await supabase
      .from('products')
      .update(updateData)
      .eq('id', id)
      .select('*')
      .single();

    if (error || !data) {
      console.error('Erro ao atualizar produto:', error);
      return undefined;
    }

    return this.mapProductFromSupabase(data);
  }

  async deleteProduct(id: number): Promise<boolean> {
    const { error } = await supabase
      .from('products')
      .delete()
      .eq('id', id);

    return !error;
  }

  private mapProductFromSupabase(data: any): Product {
    return {
      id: data.id,
      name: data.name,
      description: data.description,
      price: data.price,
      images: data.images,
      inStock: data.in_stock,
      hasVariations: data.has_variations,
      variations: data.variations,
      storeId: data.store_id,
      categoryId: data.category_id,
      visible: data.visible,
      createdAt: new Date(data.created_at)
    };
  }

  // Category methods
  async getCategory(id: number): Promise<Category | undefined> {
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .eq('id', id)
      .single();

    if (error || !data) return undefined;
    return this.mapCategoryFromSupabase(data);
  }

  async getCategoriesByStoreId(storeId: number): Promise<Category[]> {
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .eq('store_id', storeId)
      .order('display_order', { ascending: true });

    if (error || !data) return [];
    return data.map(this.mapCategoryFromSupabase);
  }

  async createCategory(insertCategory: InsertCategory): Promise<Category> {
    const { data, error } = await supabase
      .from('categories')
      .insert({
        name: insertCategory.name,
        description: insertCategory.description,
        logo: insertCategory.logo,
        store_id: insertCategory.storeId,
        display_order: insertCategory.displayOrder,
        visible: insertCategory.visible
      })
      .select('*')
      .single();

    if (error) {
      console.error('Erro ao criar categoria:', error);
      throw error;
    }

    return this.mapCategoryFromSupabase(data);
  }

  async updateCategory(id: number, categoryUpdate: Partial<Category>): Promise<Category | undefined> {
    const updateData: any = {};

    if (categoryUpdate.name !== undefined) updateData.name = categoryUpdate.name;
    if (categoryUpdate.description !== undefined) updateData.description = categoryUpdate.description;
    if (categoryUpdate.logo !== undefined) updateData.logo = categoryUpdate.logo;
    if (categoryUpdate.displayOrder !== undefined) updateData.display_order = categoryUpdate.displayOrder;
    if (categoryUpdate.visible !== undefined) updateData.visible = categoryUpdate.visible;

    const { data, error } = await supabase
      .from('categories')
      .update(updateData)
      .eq('id', id)
      .select('*')
      .single();

    if (error || !data) {
      console.error('Erro ao atualizar categoria:', error);
      return undefined;
    }

    return this.mapCategoryFromSupabase(data);
  }

  async deleteCategory(id: number): Promise<boolean> {
    const { error } = await supabase
      .from('categories')
      .delete()
      .eq('id', id);

    return !error;
  }

  private mapCategoryFromSupabase(data: any): Category {
    return {
      id: data.id,
      name: data.name,
      description: data.description,
      logo: data.logo,
      storeId: data.store_id,
      displayOrder: data.display_order,
      visible: data.visible
    };
  }

  // Product Variation methods
  async getProductVariationsByProductId(productId: number): Promise<ProductVariation[]> {
    const { data, error } = await supabase
      .from('product_variations')
      .select('*')
      .eq('product_id', productId);

    if (error || !data) return [];
    return data.map(this.mapProductVariationFromSupabase);
  }

  async createProductVariation(insertVariation: InsertProductVariation): Promise<ProductVariation> {
    const { data, error } = await supabase
      .from('product_variations')
      .insert({
        name: insertVariation.name,
        description: insertVariation.description,
        product_id: insertVariation.productId,
        required: insertVariation.required,
        multiple_choice: insertVariation.multipleChoice,
        min_selections: insertVariation.minSelections,
        max_selections: insertVariation.maxSelections,
        created_at: new Date()
      })
      .select('*')
      .single();

    if (error) {
      console.error('Erro ao criar variação de produto:', error);
      throw error;
    }

    return this.mapProductVariationFromSupabase(data);
  }

  async updateProductVariation(id: number, variationUpdate: Partial<ProductVariation>): Promise<ProductVariation | undefined> {
    console.log('DEBUG - updateProductVariation - ID:', id);
    console.log('DEBUG - updateProductVariation - Dados recebidos:', JSON.stringify(variationUpdate));

    const updateData: any = {};

    if (variationUpdate.name !== undefined) updateData.name = variationUpdate.name;
    if (variationUpdate.description !== undefined) updateData.description = variationUpdate.description;
    if (variationUpdate.required !== undefined) updateData.required = variationUpdate.required;
    if (variationUpdate.multipleChoice !== undefined) updateData.multiple_choice = variationUpdate.multipleChoice;

    // Garantir conversão explícita para números para os campos de seleções
    if (variationUpdate.minSelections !== undefined) {
      const minValue = Number(variationUpdate.minSelections);
      updateData.min_selections = isNaN(minValue) ? 0 : minValue;
      console.log('DEBUG - minSelections convertido:', updateData.min_selections);
    }

    if (variationUpdate.maxSelections !== undefined) {
      const maxValue = Number(variationUpdate.maxSelections);
      updateData.max_selections = isNaN(maxValue) ? 1 : maxValue;
      console.log('DEBUG - maxSelections convertido:', updateData.max_selections);
    }

    console.log('DEBUG - updateProductVariation - Dados formatados para DB:', JSON.stringify(updateData));

    const { data, error } = await supabase
      .from('product_variations')
      .update(updateData)
      .eq('id', id)
      .select('*')
      .single();

    if (error || !data) {
      console.error('Erro ao atualizar variação de produto:', error);
      return undefined;
    }

    console.log('DEBUG - updateProductVariation - Resultado:', JSON.stringify(data));

    return this.mapProductVariationFromSupabase(data);
  }

  async deleteProductVariation(id: number): Promise<boolean> {
    // Primeiro deletar todas as opções associadas a esta variação
    await supabase
      .from('variation_options')
      .delete()
      .eq('variation_id', id);

    // Depois deletar a própria variação
    const { error } = await supabase
      .from('product_variations')
      .delete()
      .eq('id', id);

    return !error;
  }

  private mapProductVariationFromSupabase(data: any): ProductVariation {
    return {
      id: data.id,
      name: data.name,
      description: data.description,
      productId: data.product_id,
      required: data.required,
      multipleChoice: data.multiple_choice,
      minSelections: data.min_selections,
      maxSelections: data.max_selections,
      createdAt: new Date(data.created_at)
    };
  }

  // Para os demais métodos, implementaremos conforme necessário
  // Estes são os métodos mais frequentemente utilizados

  // Métodos não implementados completamente ainda
  async getVariationOptionsByVariationId(variationId: number): Promise<VariationOption[]> {
    // Buscar opções de variação pelo ID da variação
    const { data, error } = await supabase
      .from('variation_options')
      .select('*')
      .eq('variation_id', variationId);

    if (error) {
      console.error('Erro ao buscar opções de variação:', error);
      return [];
    }

    // Mapear para o formato esperado pela aplicação (compatibilidade)
    return data.map(option => ({
      id: String(option.id),
      name: option.name,
      precoAdicional: option.additional_price,
      price: option.additional_price // Adicionando price para compatibilidade com o frontend
    }));
  }

  async createVariationOption(option: InsertVariationOption): Promise<VariationOption> {
    console.log('Criando opção de variação no Supabase:', option);

    const { data, error } = await supabase
      .from('variation_options')
      .insert({
        variation_id: option.variationId,
        name: option.name,
        additional_price: option.price
      })
      .select()
      .single();

    if (error) {
      console.error('Erro ao criar opção de variação:', error);
      throw new Error(`Erro ao criar opção de variação: ${error.message}`);
    }

    console.log('Opção de variação criada:', data);

    // Mapear para o formato esperado pela aplicação
    return {
      id: String(data.id),
      name: data.name,
      precoAdicional: data.additional_price,
      price: data.additional_price // Adicionando price para compatibilidade com o frontend
    };
  }

  async updateVariationOption(id: number, option: Partial<VariationOption>): Promise<VariationOption | undefined> {
    console.log('Atualizando opção de variação no Supabase, ID:', id, 'Dados:', option);

    // Preparar os dados para atualização
    const updateData: any = {};
    if (option.name !== undefined) updateData.name = option.name;
    if (option.precoAdicional !== undefined) updateData.additional_price = option.precoAdicional;

    const { data, error } = await supabase
      .from('variation_options')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Erro ao atualizar opção de variação:', error);
      return undefined;
    }

    if (!data) {
      console.log('Nenhuma opção de variação encontrada com ID:', id);
      return undefined;
    }

    console.log('Opção de variação atualizada:', data);

    // Mapear para o formato esperado pela aplicação
    return {
      id: String(data.id),
      name: data.name,
      precoAdicional: data.additional_price,
      price: data.additional_price // Adicionando price para compatibilidade com o frontend
    };
  }

  async deleteVariationOption(id: number): Promise<boolean> {
    console.log('Excluindo opção de variação no Supabase, ID:', id);

    const { error } = await supabase
      .from('variation_options')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Erro ao excluir opção de variação:', error);
      return false;
    }

    console.log('Opção de variação excluída com sucesso, ID:', id);
    return true;
  }

  async getCustomer(id: number): Promise<Customer | undefined> {
    const { data, error } = await supabase
      .from('customers')
      .select('*')
      .eq('id', id)
      .single();

    if (error || !data) {
      console.error('Erro ao obter cliente:', error);
      return undefined;
    }

    return {
      id: data.id,
      storeId: data.store_id,
      name: data.name,
      email: data.email,
      phone: data.whatsapp,
      countryCode: data.country_code,
      createdAt: new Date(data.created_at)
    };
  }

  async getCustomersByStoreId(storeId: number): Promise<Customer[]> {
    const { data, error } = await supabase
      .from('customers')
      .select('*')
      .eq('store_id', storeId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Erro ao obter clientes da loja:', error);
      return [];
    }

    return data.map(customer => ({
      id: customer.id,
      storeId: customer.store_id,
      name: customer.name,
      email: customer.email,
      phone: customer.whatsapp,
      countryCode: customer.country_code,
      createdAt: new Date(customer.created_at)
    }));
  }

  async getCustomerByEmail(storeId: number, email: string): Promise<Customer | undefined> {
    if (!email) return undefined;

    console.log(`Buscando cliente para loja ${storeId} com email: "${email}"`);

    // Normalizando o email para minúsculas para garantir que a comparação seja case-insensitive
    const normalizedEmail = email.toLowerCase().trim();
    console.log(`Email normalizado para busca: "${normalizedEmail}"`);

    // Primeiro buscar todos os clientes da loja para debug
    const { data: allCustomers, error: allError } = await supabase
      .from('customers')
      .select('*')
      .eq('store_id', storeId);

    if (allCustomers) {
      console.log(`Total de clientes na loja ${storeId}: ${allCustomers.length}`);
      console.log('Emails existentes:', allCustomers.map(c => `"${c.email?.toLowerCase()?.trim() || ''}"`).join(', '));

      // Verificar manualmente se há correspondência
      const matchingCustomers = allCustomers.filter(c =>
        c.email && c.email.toLowerCase().trim() === normalizedEmail
      );

      if (matchingCustomers.length > 0) {
        console.log(`Encontradas ${matchingCustomers.length} correspondências exatas de email:`,
          matchingCustomers.map(c => `${c.id}: ${c.email}`));
      }
    }

    // Agora buscar o cliente específico
    const { data, error } = await supabase
      .from('customers')
      .select('*')
      .eq('store_id', storeId)
      .ilike('email', normalizedEmail) // Usando ilike para comparação case-insensitive
      .maybeSingle();

    if (error) {
      console.error('Erro ao buscar cliente por email:', error);
      return undefined;
    }

    if (!data) {
      console.log(`Nenhum cliente encontrado com email "${normalizedEmail}"`);

      // Tentar uma busca mais flexível
      try {
        const { data: fuzzyResults } = await supabase
          .from('customers')
          .select('*')
          .eq('store_id', storeId);

        if (fuzzyResults && fuzzyResults.length > 0) {
          // Verificar manualmente se há correspondência aproximada
          const potentialMatches = fuzzyResults.filter(c =>
            c.email && normalizedEmail &&
            (c.email.toLowerCase().includes(normalizedEmail) ||
             normalizedEmail.includes(c.email.toLowerCase()))
          );

          if (potentialMatches.length > 0) {
            console.log('Possíveis correspondências aproximadas por email:',
              potentialMatches.map(c => `${c.id}: ${c.email}`));

            // Usar a primeira correspondência aproximada
            const bestMatch = potentialMatches[0];
            console.log('Usando a melhor correspondência aproximada:', bestMatch);

            return {
              id: bestMatch.id,
              storeId: bestMatch.store_id,
              name: bestMatch.name,
              email: bestMatch.email,
              phone: bestMatch.whatsapp,
              countryCode: bestMatch.country_code,
              createdAt: new Date(bestMatch.created_at)
            };
          }
        }
      } catch (fuzzyError) {
        console.error('Erro na busca aproximada por email:', fuzzyError);
      }

      return undefined;
    }

    console.log('Cliente encontrado pelo email:', data);

    return {
      id: data.id,
      storeId: data.store_id,
      name: data.name,
      email: data.email,
      phone: data.whatsapp,
      countryCode: data.country_code,
      createdAt: new Date(data.created_at)
    };
  }

  async getCustomerByPhone(storeId: number, phone: string): Promise<Customer | undefined> {
    if (!phone) return undefined;

    console.log(`Buscando cliente para loja ${storeId} com telefone: "${phone}"`);

    // Limpar o número de telefone para comparação (remover espaços, traços, parênteses)
    const cleanPhone = phone.replace(/[\s\-\(\)\+]/g, '').trim();
    console.log(`Telefone normalizado para busca: "${cleanPhone}"`);

    // Primeiro buscar todos os clientes da loja para debug
    const { data: allCustomers, error: allError } = await supabase
      .from('customers')
      .select('*')
      .eq('store_id', storeId);

    if (allCustomers) {
      console.log(`Total de clientes na loja ${storeId}: ${allCustomers.length}`);

      // Mostrar telefones existentes com formatação para debug
      const formattedPhones = allCustomers.map(c => {
        const cleanStoredPhone = c.whatsapp ? c.whatsapp.replace(/[\s\-\(\)\+]/g, '').trim() : '';
        return `"${cleanStoredPhone}" (original: "${c.whatsapp || ''}")`;
      });
      console.log('Telefones existentes:', formattedPhones.join(', '));

      // Verificar manualmente se há correspondência exata
      const matchingCustomers = allCustomers.filter(c => {
        if (!c.whatsapp) return false;
        const cleanStoredPhone = c.whatsapp.replace(/[\s\-\(\)\+]/g, '').trim();
        return cleanStoredPhone === cleanPhone;
      });

      if (matchingCustomers.length > 0) {
        console.log(`Encontradas ${matchingCustomers.length} correspondências exatas de telefone:`,
          matchingCustomers.map(c => `${c.id}: ${c.whatsapp}`));
      }
    }

    // Agora buscar o cliente específico
    const { data, error } = await supabase
      .from('customers')
      .select('*')
      .eq('store_id', storeId)
      .eq('whatsapp', cleanPhone)  // Busca exata com telefone limpo
      .maybeSingle();

    if (error) {
      console.error('Erro ao buscar cliente por telefone:', error);
      return undefined;
    }

    if (!data) {
      console.log(`Nenhum cliente encontrado com telefone exato "${cleanPhone}"`);

      // Tentar uma busca mais flexível (verificar se o telefone contém)
      try {
        const { data: fuzzyResults, error: fuzzyError } = await supabase
          .from('customers')
          .select('*')
          .eq('store_id', storeId);

        if (!fuzzyError && fuzzyResults) {
          // Verificar se algum dos telefones contém o número procurado ou vice-versa
          const potentialMatches = fuzzyResults.filter(c => {
            if (!c.whatsapp) return false;
            const cleanStoredPhone = c.whatsapp.replace(/[\s\-\(\)\+]/g, '').trim();

            // Verificar se um é substring do outro (para lidar com códigos de país, etc)
            const isMatch = cleanStoredPhone.includes(cleanPhone) ||
                           cleanPhone.includes(cleanStoredPhone);

            // Para números muito curtos, exigir correspondência exata para evitar falsos positivos
            if (cleanPhone.length < 8 || cleanStoredPhone.length < 8) {
              return cleanStoredPhone === cleanPhone;
            }

            return isMatch;
          });

          if (potentialMatches.length > 0) {
            console.log('Possíveis correspondências encontradas por telefone:',
              potentialMatches.map(c => `${c.id}: ${c.whatsapp} (limpo: ${c.whatsapp.replace(/[\s\-\(\)\+]/g, '').trim()})`));

            // Usar a primeira correspondência
            const bestMatch = potentialMatches[0];
            console.log('Usando a melhor correspondência de telefone:', bestMatch);

            return {
              id: bestMatch.id,
              storeId: bestMatch.store_id,
              name: bestMatch.name,
              email: bestMatch.email,
              phone: bestMatch.whatsapp,
              countryCode: bestMatch.country_code,
              createdAt: new Date(bestMatch.created_at)
            };
          }
        }
      } catch (fuzzyError) {
        console.error('Erro na busca aproximada por telefone:', fuzzyError);
      }

      return undefined;
    }

    console.log('Cliente encontrado pelo telefone exato:', data);

    return {
      id: data.id,
      storeId: data.store_id,
      name: data.name,
      email: data.email,
      phone: data.whatsapp,
      countryCode: data.country_code,
      createdAt: new Date(data.created_at)
    };
  }

  async createCustomer(customer: InsertCustomer): Promise<Customer> {
    console.log('Dados do cliente recebidos:', customer);

    // Normalizar os dados antes de salvar
    const normalizedEmail = customer.email ? customer.email.toLowerCase().trim() : '';
    const normalizedPhone = customer.phone ? customer.phone.replace(/[\s\-\(\)\+]/g, '').trim() : '';

    console.log('Dados normalizados para criação:');
    console.log('- Email original:', customer.email, '-> normalizado:', normalizedEmail);
    console.log('- Telefone original:', customer.phone, '-> normalizado:', normalizedPhone);

    // Verificar novamente se o cliente já existe com os dados normalizados
    if (normalizedEmail) {
      const existingByEmail = await this.getCustomerByEmail(customer.storeId, normalizedEmail);
      if (existingByEmail) {
        console.log('Cliente já existe com este email normalizado. Retornando cliente existente:', existingByEmail);
        return existingByEmail;
      }
    }

    if (normalizedPhone) {
      const existingByPhone = await this.getCustomerByPhone(customer.storeId, normalizedPhone);
      if (existingByPhone) {
        console.log('Cliente já existe com este telefone normalizado. Retornando cliente existente:', existingByPhone);
        return existingByPhone;
      }
    }

    const { data, error } = await supabase
      .from('customers')
      .insert({
        store_id: customer.storeId,
        name: customer.name,
        email: normalizedEmail, // Usar email normalizado
        whatsapp: normalizedPhone, // Usar telefone normalizado
        country_code: customer.countryCode || '+55', // Padrão para Brasil
        created_at: new Date()
      })
      .select('*')
      .single();

    if (error) {
      console.error('Erro ao criar cliente:', error);
      throw error;
    }

    console.log('Cliente criado com sucesso:', data);

    // Mapear os dados do Supabase para o formato do nosso schema
    return {
      id: data.id,
      storeId: data.store_id,
      name: data.name,
      email: data.email,
      phone: data.whatsapp,
      countryCode: data.country_code,
      createdAt: new Date(data.created_at)
    };
  }

  async updateCustomer(id: number, customerUpdate: Partial<Customer>): Promise<Customer | undefined> {
    console.log('Atualizando cliente:', id, customerUpdate);

    const updateData: any = {};

    // Normalizar os dados antes de salvar
    if (customerUpdate.name !== undefined) {
      updateData.name = customerUpdate.name;
    }

    if (customerUpdate.email !== undefined) {
      // Normalizar email
      const normalizedEmail = customerUpdate.email.toLowerCase().trim();
      updateData.email = normalizedEmail;
      console.log(`Email normalizado para atualização: "${normalizedEmail}" (original: "${customerUpdate.email}")`);
    }

    if (customerUpdate.phone !== undefined) {
      // Normalizar telefone
      const normalizedPhone = customerUpdate.phone.replace(/[\s\-\(\)\+]/g, '').trim();
      updateData.whatsapp = normalizedPhone; // Mapeando para o campo correto no banco
      console.log(`Telefone normalizado para atualização: "${normalizedPhone}" (original: "${customerUpdate.phone}")`);
    }

    if (customerUpdate.countryCode !== undefined) {
      updateData.country_code = customerUpdate.countryCode; // Adicionando código do país
    }

    console.log('Dados normalizados para atualização:', updateData);

    // Verificar se há algo para atualizar
    if (Object.keys(updateData).length === 0) {
      console.log('Nenhum dado para atualizar');

      // Retornar o cliente atual
      const { data: currentData } = await supabase
        .from('customers')
        .select('*')
        .eq('id', id)
        .single();

      if (currentData) {
        return {
          id: currentData.id,
          storeId: currentData.store_id,
          name: currentData.name,
          email: currentData.email,
          phone: currentData.whatsapp,
          countryCode: currentData.country_code,
          createdAt: new Date(currentData.created_at)
        };
      }

      return undefined;
    }

    const { data, error } = await supabase
      .from('customers')
      .update(updateData)
      .eq('id', id)
      .select('*')
      .single();

    if (error) {
      console.error('Erro ao atualizar cliente:', error);
      return undefined;
    }

    if (!data) {
      console.error('Cliente não encontrado após atualização');
      return undefined;
    }

    console.log('Cliente atualizado com sucesso:', data);

    // Mapear os dados do Supabase para o formato do nosso schema
    return {
      id: data.id,
      storeId: data.store_id,
      name: data.name,
      email: data.email,
      phone: data.whatsapp,
      countryCode: data.country_code,
      createdAt: new Date(data.created_at)
    };
  }

  async getOrder(id: number): Promise<Order | undefined> {
    const { data, error } = await supabase
      .from('orders')
      .select('*')
      .eq('id', id)
      .single();

    if (error || !data) {
      console.error('Erro ao obter pedido:', error);
      return undefined;
    }

    // Buscar recebimentos do pedido
    const recebimentos = await this.getOrderPaymentsByOrderId(id);

    return {
      id: data.id,
      storeId: data.store_id,
      customerId: data.customer_id,
      status: data.status,
      paymentStatus: data.payment_status || 'pendente',
      total: data.total,
      paymentMethod: data.payment_method,
      notes: data.notes,
      receivingMethod: data.receiving_method,
      receivingDate: new Date(data.receiving_date),
      receivingTime: data.receiving_time,
      deliveryAddress: data.delivery_address,
      subtotal: data.subtotal,
      deliveryFee: data.delivery_fee,
      discount: data.discount || 0,
      couponId: data.coupon_id || null,
      couponCode: data.coupon_code || null,
      couponType: data.coupon_type || null,
      recebimentos: recebimentos,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at)
    };
  }

  async getOrdersByStoreId(storeId: number): Promise<Order[]> {
    const { data, error } = await supabase
      .from('orders')
      .select('*')
      .eq('store_id', storeId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Erro ao obter pedidos da loja:', error);
      return [];
    }

    return data.map(order => ({
      id: order.id,
      storeId: order.store_id,
      customerId: order.customer_id,
      status: order.status,
      paymentStatus: order.payment_status || 'pendente',
      total: order.total,
      paymentMethod: order.payment_method,
      notes: order.notes,
      receivingMethod: order.receiving_method,
      receivingDate: new Date(order.receiving_date),
      receivingTime: order.receiving_time,
      deliveryAddress: order.delivery_address,
      subtotal: order.subtotal,
      deliveryFee: order.delivery_fee,
      discount: order.discount || 0,
      couponId: order.coupon_id || null,
      couponCode: order.coupon_code || null,
      couponType: order.coupon_type || null,
      createdAt: new Date(order.created_at),
      updatedAt: new Date(order.updated_at)
    }));
  }

  async getOrdersByCustomerId(customerId: number): Promise<Order[]> {
    const { data, error } = await supabase
      .from('orders')
      .select('*')
      .eq('customer_id', customerId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Erro ao obter pedidos do cliente:', error);
      return [];
    }

    return data.map(order => ({
      id: order.id,
      storeId: order.store_id,
      customerId: order.customer_id,
      status: order.status,
      paymentStatus: order.payment_status || 'pendente',
      total: order.total,
      paymentMethod: order.payment_method,
      notes: order.notes,
      receivingMethod: order.receiving_method,
      receivingDate: new Date(order.receiving_date),
      receivingTime: order.receiving_time,
      deliveryAddress: order.delivery_address,
      subtotal: order.subtotal,
      deliveryFee: order.delivery_fee,
      discount: order.discount || 0,
      couponId: order.coupon_id || null,
      couponCode: order.coupon_code || null,
      couponType: order.coupon_type || null,
      createdAt: new Date(order.created_at),
      updatedAt: new Date(order.updated_at)
    }));
  }

  async createOrder(order: InsertOrder): Promise<Order> {
    const { data, error } = await supabase
      .from('orders')
      .insert({
        store_id: order.storeId,
        customer_id: order.customerId,
        status: order.status,
        payment_status: order.paymentStatus || 'pendente',
        total: order.total,
        payment_method: order.paymentMethod,
        notes: order.notes,
        receiving_method: order.receivingMethod,
        receiving_date: order.receivingDate,
        receiving_time: order.receivingTime,
        delivery_address: order.deliveryAddress,
        subtotal: order.subtotal,
        delivery_fee: order.deliveryFee,
        discount: order.discount || 0,
        coupon_id: order.couponId || null,
        coupon_code: order.couponCode || null,
        coupon_type: order.couponType || null,
        created_at: new Date(),
        updated_at: new Date()
      })
      .select('*')
      .single();

    if (error) {
      console.error('Erro ao criar pedido:', error);
      throw error;
    }

    // Mapear os dados do Supabase para o formato do nosso schema
    return {
      id: data.id,
      storeId: data.store_id,
      customerId: data.customer_id,
      status: data.status,
      paymentStatus: data.payment_status || 'pendente',
      total: data.total,
      paymentMethod: data.payment_method,
      notes: data.notes,
      receivingMethod: data.receiving_method,
      receivingDate: new Date(data.receiving_date),
      receivingTime: data.receiving_time,
      deliveryAddress: data.delivery_address,
      subtotal: data.subtotal,
      deliveryFee: data.delivery_fee,
      discount: data.discount || 0,
      couponId: data.coupon_id || null,
      couponCode: data.coupon_code || null,
      couponType: data.coupon_type || null,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at)
    };
  }

  async getOrdersByDateRange(startDate: Date, endDate: Date): Promise<Order[]> {
    const { data, error } = await supabase
      .from('orders')
      .select('*')
      .gte('created_at', startDate.toISOString())
      .lte('created_at', endDate.toISOString())
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Erro ao obter pedidos por período:', error);
      return [];
    }

    return data.map(order => ({
      id: order.id,
      storeId: order.store_id,
      customerId: order.customer_id,
      status: order.status,
      paymentStatus: order.payment_status || 'pendente',
      total: order.total,
      paymentMethod: order.payment_method,
      notes: order.notes,
      receivingMethod: order.receiving_method,
      receivingDate: new Date(order.receiving_date),
      receivingTime: order.receiving_time,
      deliveryAddress: order.delivery_address,
      subtotal: order.subtotal,
      deliveryFee: order.delivery_fee,
      discount: order.discount || 0,
      couponId: order.coupon_id || null,
      couponCode: order.coupon_code || null,
      couponType: order.coupon_type || null,
      createdAt: new Date(order.created_at),
      updatedAt: new Date(order.updated_at)
    }));
  }

  async updateOrder(id: number, orderData: Partial<Order>): Promise<Order | undefined> {
    const updateData: any = {};

    // Mapear campos para o formato snake_case do Supabase
    if (orderData.status) updateData.status = orderData.status;
    if (orderData.paymentStatus) updateData.payment_status = orderData.paymentStatus;
    if (orderData.total !== undefined) updateData.total = orderData.total;
    if (orderData.paymentMethod) updateData.payment_method = orderData.paymentMethod;
    if (orderData.notes) updateData.notes = orderData.notes;
    if (orderData.receivingMethod) updateData.receiving_method = orderData.receivingMethod;
    if (orderData.receivingDate) updateData.receiving_date = orderData.receivingDate;
    if (orderData.receivingTime) updateData.receiving_time = orderData.receivingTime;
    if (orderData.deliveryAddress) updateData.delivery_address = orderData.deliveryAddress;
    if (orderData.subtotal !== undefined) updateData.subtotal = orderData.subtotal;
    if (orderData.deliveryFee !== undefined) updateData.delivery_fee = orderData.deliveryFee;
    if (orderData.discount !== undefined) updateData.discount = orderData.discount;
    if (orderData.couponId !== undefined) updateData.coupon_id = orderData.couponId;
    if (orderData.couponCode) updateData.coupon_code = orderData.couponCode;
    if (orderData.couponType) updateData.coupon_type = orderData.couponType;

    updateData.updated_at = new Date();

    const { data, error } = await supabase
      .from('orders')
      .update(updateData)
      .eq('id', id)
      .select('*')
      .single();

    if (error || !data) {
      console.error('Erro ao atualizar pedido:', error);
      return undefined;
    }

    return {
      id: data.id,
      storeId: data.store_id,
      customerId: data.customer_id,
      status: data.status,
      paymentStatus: data.payment_status || 'pendente',
      total: data.total,
      paymentMethod: data.payment_method,
      notes: data.notes,
      receivingMethod: data.receiving_method,
      receivingDate: new Date(data.receiving_date),
      receivingTime: data.receiving_time,
      deliveryAddress: data.delivery_address,
      subtotal: data.subtotal,
      deliveryFee: data.delivery_fee,
      discount: data.discount || 0,
      couponId: data.coupon_id || null,
      couponCode: data.coupon_code || null,
      couponType: data.coupon_type || null,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at)
    };
  }

  async updateOrderStatus(id: number, status: string): Promise<Order | undefined> {
    const { data, error } = await supabase
      .from('orders')
      .update({
        status,
        updated_at: new Date()
      })
      .eq('id', id)
      .select('*')
      .single();

    if (error || !data) {
      console.error('Erro ao atualizar status do pedido:', error);
      return undefined;
    }

    return {
      id: data.id,
      storeId: data.store_id,
      customerId: data.customer_id,
      status: data.status,
      paymentStatus: data.payment_status || 'pendente',
      total: data.total,
      paymentMethod: data.payment_method,
      notes: data.notes,
      receivingMethod: data.receiving_method,
      receivingDate: new Date(data.receiving_date),
      receivingTime: data.receiving_time,
      deliveryAddress: data.delivery_address,
      subtotal: data.subtotal,
      deliveryFee: data.delivery_fee,
      discount: data.discount || 0,
      couponId: data.coupon_id || null,
      couponCode: data.coupon_code || null,
      couponType: data.coupon_type || null,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at)
    };
  }

  async getOrderItemsByOrderId(orderId: number): Promise<OrderItem[]> {
    const { data, error } = await supabase
      .from('order_items')
      .select('*')
      .eq('order_id', orderId);

    if (error) {
      console.error('Erro ao obter itens do pedido:', error);
      return [];
    }

    return data.map(item => ({
      id: item.id,
      orderId: item.order_id,
      productId: item.product_id,
      quantity: item.quantity,
      price: item.price,
      selectedOptions: item.selected_options,
      observation: item.observation,
      productName: item.product_name,
      productDescription: item.product_description,
      isCustomProduct: item.is_custom_product,
      name: item.product_name // Adicionando o campo name para compatibilidade com a interface do cliente
    }));
  }

  async createOrderItem(orderItem: InsertOrderItem): Promise<OrderItem> {
    console.log('Criando item de pedido:', orderItem);

    // Verificar se é um produto personalizado
    const isCustomProduct = orderItem.productId === -1 || (orderItem as any).isCustomProduct;

    const insertData: any = {
      order_id: orderItem.orderId,
      product_id: orderItem.productId,
      quantity: orderItem.quantity,
      price: orderItem.price,
      selected_options: orderItem.selectedOptions,
      observation: orderItem.observation
    };

    // Adicionar campos para produtos personalizados
    if (isCustomProduct) {
      insertData.is_custom_product = true;
      insertData.product_name = (orderItem as any).productName || 'Produto Personalizado';
      insertData.product_description = (orderItem as any).productDescription || '';
    }

    console.log('Dados para inserção:', insertData);

    const { data, error } = await supabase
      .from('order_items')
      .insert(insertData)
      .select('*')
      .single();

    if (error) {
      console.error('Erro ao criar item do pedido:', error);
      throw error;
    }

    console.log('Item de pedido criado com sucesso:', data);

    // Mapear os dados do Supabase para o formato do nosso schema
    return {
      id: data.id,
      orderId: data.order_id,
      productId: data.product_id,
      quantity: data.quantity,
      price: data.price,
      selectedOptions: data.selected_options,
      observation: data.observation,
      productName: data.product_name,
      productDescription: data.product_description,
      isCustomProduct: data.is_custom_product
    };
  }

  async recordStoreVisit(storeVisit: InsertStoreVisit): Promise<StoreVisit> {
    console.log(`Successfully recorded store visit for store ID: ${storeVisit.storeId}`);

    const { data, error } = await supabase
      .from('store_visits')
      .insert({
        store_id: storeVisit.storeId,
        visitor_ip: storeVisit.visitorIp || '',
        session_id: storeVisit.sessionId || '',
        user_agent: storeVisit.userAgent || '',
        referrer: storeVisit.referrer || '',
      })
      .select('*')
      .single();

    if (error) {
      console.error('Erro ao registrar visita:', error);
      // Retornamos um objeto mínimo mesmo em caso de erro para não quebrar a aplicação
      return {
        id: 0,
        storeId: storeVisit.storeId,
        visitorIp: storeVisit.visitorIp || '',
        sessionId: storeVisit.sessionId || '',
        userAgent: storeVisit.userAgent || '',
        referrer: storeVisit.referrer || '',
        createdAt: new Date(),
      };
    }

    return {
      id: data.id,
      storeId: data.store_id,
      visitorIp: data.visitor_ip,
      sessionId: data.session_id,
      userAgent: data.user_agent,
      referrer: data.referrer,
      createdAt: new Date(data.created_at),
    };
  }

  async getStoreVisitsByStoreId(storeId: number, startDate?: Date, endDate?: Date): Promise<StoreVisit[]> {
    let query = supabase
      .from('store_visits')
      .select('*')
      .eq('store_id', storeId);

    if (startDate) {
      query = query.gte('created_at', startDate.toISOString());
    }

    if (endDate) {
      query = query.lte('created_at', endDate.toISOString());
    }

    const { data, error } = await query;

    if (error) {
      console.error('Erro ao obter visitas da loja:', error);
      return [];
    }

    return data.map(visit => ({
      id: visit.id,
      storeId: visit.store_id,
      visitorIp: visit.visitor_ip,
      sessionId: visit.session_id,
      userAgent: visit.user_agent,
      referrer: visit.referrer,
      createdAt: new Date(visit.created_at),
    }));
  }

  async getMonthlyVisitCount(storeId: number): Promise<number> {
    // Calcula o primeiro dia do mês atual
    const now = new Date();
    const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    // Busca todas as visitas deste mês
    const { data, error } = await supabase
      .from('store_visits')
      .select('id')
      .eq('store_id', storeId)
      .gte('created_at', firstDayOfMonth.toISOString());

    if (error) {
      console.error('Erro ao contar visitas mensais:', error);
      return 0;
    }

    // Retorna o número de visitas
    return data.length;
  }

  async getMonthlyOrderCount(storeId: number): Promise<number> {
    // Calcula o primeiro dia do mês atual
    const now = new Date();
    const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    // Busca todas os pedidos deste mês
    const { data, error } = await supabase
      .from('orders')
      .select('id')
      .eq('store_id', storeId)
      .gte('created_at', firstDayOfMonth.toISOString());

    if (error) {
      console.error('Erro ao contar pedidos mensais:', error);
      return 0;
    }

    // Retorna o número de pedidos
    return data.length;
  }

  async getCartItemsBySessionId(storeId: number, sessionId: string): Promise<CartItem[]> {
    // Implementação pendente
    return [];
  }

  async getCartItemsByUserId(storeId: number, userId: string): Promise<CartItem[]> {
    // Implementação pendente
    return [];
  }

  async createCartItem(cartItem: InsertCartItem): Promise<CartItem> {
    // Implementação pendente
    return {} as CartItem;
  }

  async updateCartItem(id: number, cartItem: Partial<CartItem>): Promise<CartItem | undefined> {
    // Implementação pendente
    return undefined;
  }

  async deleteCartItem(id: number): Promise<boolean> {
    // Implementação pendente
    return true;
  }

  async clearCartItems(storeId: number, sessionId: string): Promise<boolean> {
    // Implementação pendente
    return true;
  }

  // Order Payment methods
  async createOrderPayment(payment: InsertOrderPayment): Promise<OrderPayment> {
    const { data, error } = await supabase
      .from('order_payments')
      .insert({
        order_id: payment.orderId,
        value: payment.valor,
        date: payment.data,
        method: payment.metodo,
        observation: payment.observacao,
        created_by: payment.createdBy,
        created_at: new Date()
      })
      .select('*')
      .single();

    if (error) {
      console.error('Erro ao criar recebimento:', error);
      throw error;
    }

    // Não atualizar status aqui - será feito na rota para evitar chamadas duplicadas

    return {
      id: data.id,
      orderId: data.order_id,
      valor: data.value,
      data: new Date(data.date),
      metodo: data.method,
      observacao: data.observation,
      createdAt: new Date(data.created_at),
      createdBy: data.created_by
    };
  }

  async getOrderPaymentsByOrderId(orderId: number): Promise<OrderPayment[]> {
    const { data, error } = await supabase
      .from('order_payments')
      .select('*')
      .eq('order_id', orderId)
      .order('date', { ascending: false });

    if (error) {
      console.error('Erro ao obter recebimentos do pedido:', error);
      return [];
    }

    return data.map(payment => ({
      id: payment.id,
      orderId: payment.order_id,
      valor: payment.value,
      data: new Date(payment.date),
      metodo: payment.method,
      observacao: payment.observation,
      createdAt: new Date(payment.created_at),
      createdBy: payment.created_by
    }));
  }

  async getOrderPayment(paymentId: number): Promise<any> {
    try {
      const { data, error } = await supabase
        .from('order_payments')
        .select('*')
        .eq('id', paymentId)
        .single();

      if (error) {
        console.error('Erro ao buscar recebimento:', error);
        return null;
      }

      return {
        id: data.id,
        orderId: data.order_id,
        valor: data.valor,
        data: data.data,
        metodo: data.metodo,
        observacao: data.observacao,
        createdAt: data.created_at,
        createdBy: data.created_by
      };
    } catch (error) {
      console.error('Erro ao buscar recebimento:', error);
      return null;
    }
  }

  async updateOrderPayment(paymentId: number, paymentData: any): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('order_payments')
        .update({
          valor: paymentData.valor,
          data: paymentData.data,
          metodo: paymentData.metodo,
          observacao: paymentData.observacao
        })
        .eq('id', paymentId);

      if (error) {
        console.error('Erro ao atualizar recebimento:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Erro ao atualizar recebimento:', error);
      return false;
    }
  }

  async deleteOrderPayment(paymentId: number): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('order_payments')
        .delete()
        .eq('id', paymentId);

      if (error) {
        console.error('Erro ao excluir recebimento:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Erro ao excluir recebimento:', error);
      return false;
    }
  }

  async updateOrderPaymentStatus(orderId: number, paymentStatus: string): Promise<Order | undefined> {
    const { data, error } = await supabase
      .from('orders')
      .update({
        payment_status: paymentStatus,
        updated_at: new Date()
      })
      .eq('id', orderId)
      .select('*')
      .single();

    if (error || !data) {
      console.error('Erro ao atualizar status de pagamento do pedido:', error);
      return undefined;
    }

    return {
      id: data.id,
      storeId: data.store_id,
      customerId: data.customer_id,
      status: data.status,
      paymentStatus: data.payment_status || 'pendente',
      total: data.total,
      paymentMethod: data.payment_method,
      notes: data.notes,
      receivingMethod: data.receiving_method,
      receivingDate: new Date(data.receiving_date),
      receivingTime: data.receiving_time,
      deliveryAddress: data.delivery_address,
      subtotal: data.subtotal,
      deliveryFee: data.delivery_fee,
      discount: data.discount || 0,
      couponId: data.coupon_id || null,
      couponCode: data.coupon_code || null,
      couponType: data.coupon_type || null,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at)
    };
  }

  // Método para corrigir subtotais de itens de revisão
  // ⚠️ ATENÇÃO: Esta função deve ser chamada apenas quando há mudanças nos itens/descontos
  // NÃO deve ser chamada durante registro de pagamentos
  async corrigirSubtotaisRevisao(revisionId: number, context: string = 'unknown'): Promise<void> {
    console.log(`=== CORRIGINDO SUBTOTAIS DA REVISÃO ${revisionId} ===`);
    console.log(`Contexto da chamada: ${context}`);

    // Verificar se esta chamada é de uma operação de pagamento
    if (context.includes('payment') || context.includes('pagamento')) {
      console.log(`⚠️ OPERAÇÃO DE PAGAMENTO DETECTADA - Pulando correção de subtotais`);
      console.log('Durante operações de pagamento, apenas o status deve ser atualizado');
      return;
    }
    try {
      console.log(`=== CORRIGINDO SUBTOTAIS DA REVISÃO ${revisionId} ===`);

      // Buscar todos os itens da revisão
      const { data: items, error: itemsError } = await supabase
        .from('order_revision_items')
        .select('*')
        .eq('revision_id', revisionId);

      if (itemsError || !items) {
        console.error('Erro ao buscar itens da revisão:', itemsError);
        return;
      }

      let totalSubtotal = 0;

      // Corrigir cada item
      for (const item of items) {
        // Calcular o preço total das variações
        let variationsTotal = 0;
        if (item.selected_variations && Array.isArray(item.selected_variations)) {
          variationsTotal = item.selected_variations.reduce((sum: number, variation: any) => {
            return sum + (variation.price || 0);
          }, 0);
        }

        // Calcular o subtotal correto
        const correctSubtotal = item.quantity * (item.unit_price + variationsTotal);

        console.log(`Item ${item.id} (${item.product_name}):`, {
          quantity: item.quantity,
          unitPrice: item.unit_price,
          variationsTotal: variationsTotal,
          oldSubtotal: item.subtotal,
          correctSubtotal: correctSubtotal
        });

        // Atualizar se necessário
        if (Math.abs(item.subtotal - correctSubtotal) > 0.01) {
          const { error: updateError } = await supabase
            .from('order_revision_items')
            .update({ subtotal: correctSubtotal })
            .eq('id', item.id);

          if (updateError) {
            console.error(`Erro ao atualizar subtotal do item ${item.id}:`, updateError);
          } else {
            console.log(`✓ Subtotal do item ${item.id} corrigido: ${item.subtotal} → ${correctSubtotal}`);
          }
        }

        totalSubtotal += correctSubtotal;
      }

      // Atualizar o subtotal da revisão
      const { error: updateRevisionError } = await supabase
        .from('order_revisions')
        .update({ subtotal: totalSubtotal })
        .eq('id', revisionId);

      if (updateRevisionError) {
        console.error('Erro ao atualizar subtotal da revisão:', updateRevisionError);
      } else {
        console.log(`✓ Subtotal da revisão ${revisionId} atualizado para: ${totalSubtotal}`);
      }

      // Recalcular o total da revisão
      // ⚠️ TEMPORARIAMENTE DESABILITADO PARA EVITAR ALTERAÇÕES DURANTE OPERAÇÕES DE PAGAMENTO
      // await this.recalcularTotalRevisao(revisionId);

    } catch (error) {
      console.error('Erro ao corrigir subtotais da revisão:', error);
    }
  }

  // Método para recalcular o total da revisão
  async recalcularTotalRevisao(revisionId: number): Promise<void> {
    try {
      // Buscar dados da revisão
      const { data: revision, error: revisionError } = await supabase
        .from('order_revisions')
        .select('subtotal, delivery_fee, discount')
        .eq('id', revisionId)
        .single();

      if (revisionError || !revision) {
        console.error('Erro ao buscar revisão:', revisionError);
        return;
      }

      // Calcular o total correto
      const newTotal = revision.subtotal - (revision.discount || 0) + (revision.delivery_fee || 0);

      // Atualizar o total
      const { error: updateError } = await supabase
        .from('order_revisions')
        .update({ total: newTotal })
        .eq('id', revisionId);

      if (updateError) {
        console.error('Erro ao atualizar total da revisão:', updateError);
      } else {
        console.log(`✓ Total da revisão ${revisionId} recalculado para: ${newTotal}`);
      }

    } catch (error) {
      console.error('Erro ao recalcular total da revisão:', error);
    }
  }

  // Método para atualizar automaticamente o status de pagamento baseado nos recebimentos
  async atualizarStatusPagamentoAutomatico(orderId: number): Promise<void> {
    try {
      // Importar a função de cálculo do schema
      const { atualizarStatusPagamento } = await import('@shared/schema');

      // Buscar o pedido para obter o total e status atual
      const { data: orderData, error: orderError } = await supabase
        .from('orders')
        .select('total, payment_status')
        .eq('id', orderId)
        .single();

      if (orderError || !orderData) {
        console.error('Erro ao buscar pedido para atualizar status de pagamento:', orderError);
        return;
      }

      // Verificar se há uma revisão ativa para usar seu total
      const { data: activeRevisionData, error: revisionError } = await supabase
        .from('order_revisions')
        .select('id, total, payment_status')
        .eq('order_id', orderId)
        .eq('is_current', true)
        .single();

      // ⚠️ IMPORTANTE: Durante operações de pagamento, NUNCA alterar dados do pedido/revisão
      // Apenas o campo payment_status deve ser atualizado
      // Subtotais, descontos e totais devem permanecer inalterados

      // Usar o total da revisão ativa se existir, senão usar o total do pedido original
      const totalToUse = activeRevisionData?.total || orderData.total;
      console.log(`=== CALCULANDO STATUS DE PAGAMENTO PARA PEDIDO ${orderId} ===`);
      console.log('⚠️ IMPORTANTE: Apenas atualizando status de pagamento - dados do pedido/revisão NÃO serão alterados');
      console.log('Dados do pedido original:', {
        total: orderData.total
      });
      console.log('Dados da revisão ativa:', {
        exists: !!activeRevisionData,
        total: activeRevisionData?.total,
        paymentStatus: activeRevisionData?.payment_status
      });
      console.log('Total que será usado para cálculos:', totalToUse);
      console.log('Diferença entre totais:', {
        orderTotal: orderData.total,
        revisionTotal: activeRevisionData?.total,
        difference: activeRevisionData?.total ? (activeRevisionData.total - orderData.total) : 0
      });

      // Buscar todos os recebimentos do pedido
      const recebimentos = await this.getOrderPaymentsByOrderId(orderId);
      console.log('Recebimentos encontrados:', {
        count: recebimentos.length,
        totalReceived: recebimentos.reduce((sum, r) => sum + r.valor, 0),
        payments: recebimentos.map(r => ({ valor: r.valor, metodo: r.metodo, data: r.data }))
      });

      // Calcular o novo status
      const totalRecebido = recebimentos.reduce((sum, r) => sum + r.valor, 0);
      const novoStatus = atualizarStatusPagamento(totalToUse, recebimentos);
      console.log('Status calculado:', {
        oldStatus: orderData.payment_status || 'pendente',
        newStatus: novoStatus,
        totalUsed: totalToUse,
        totalReceived: totalRecebido,
        pendingAmount: totalToUse - totalRecebido,
        tolerance: 0.01,
        isFullyPaid: totalRecebido >= (totalToUse - 0.01),
        difference: totalRecebido - totalToUse
      });

      // Atualizar o status no pedido original
      console.log(`Tentando atualizar status do pedido ${orderId} de "${orderData.payment_status || 'pendente'}" para "${novoStatus}"`);
      const { data: updateResult, error: updateOrderError } = await supabase
        .from('orders')
        .update({ payment_status: novoStatus })
        .eq('id', orderId)
        .select('payment_status');

      if (updateOrderError) {
        console.error('Erro ao atualizar status de pagamento do pedido:', updateOrderError);
      } else {
        console.log(`Status de pagamento do pedido ${orderId} atualizado com sucesso para: ${novoStatus}`);
        console.log('Resultado da atualização:', updateResult);
      }

      // Se há uma revisão ativa, também atualizar seu status
      if (activeRevisionData) {
        const { error: updateRevisionError } = await supabase
          .from('order_revisions')
          .update({ payment_status: novoStatus })
          .eq('order_id', orderId)
          .eq('is_current', true);

        if (updateRevisionError) {
          console.error('Erro ao atualizar status de pagamento da revisão ativa:', updateRevisionError);
        } else {
          console.log(`Status de pagamento da revisão ativa do pedido ${orderId} atualizado para: ${novoStatus}`);
        }
      }
    } catch (error) {
      console.error('Erro ao atualizar status de pagamento automaticamente:', error);
    }
  }

  // Order Revision methods
  async getOrderRevision(id: number): Promise<OrderRevision | undefined> {
    const { data, error } = await supabase
      .from('order_revisions')
      .select('*')
      .eq('id', id)
      .single();

    if (error || !data) return undefined;

    console.log('Dados da revisão obtidos do banco (raw):', data);
    console.log('Dados da revisão obtidos do banco (campos específicos):', {
      id: data.id,
      discount_type: data.discount_type,
      original_percentage: data.original_percentage,
      discount: data.discount
    });

    const revision = {
      id: data.id,
      orderId: data.order_id,
      revisionNumber: data.revision_number,
      status: data.status,
      paymentStatus: data.payment_status || 'pendente',
      receivingMethod: data.receiving_method,
      receivingDate: data.receiving_date ? new Date(data.receiving_date) : null,
      receivingTime: data.receiving_time,
      deliveryAddress: data.delivery_address,
      paymentMethod: data.payment_method,
      subtotal: data.subtotal,
      deliveryFee: data.delivery_fee,
      discount: data.discount || 0,
      discount_type: data.discount_type || 'fixed',
      original_percentage: data.original_percentage || null,
      couponId: data.coupon_id || null,
      couponCode: data.coupon_code || null,
      couponType: data.coupon_type || null,
      total: data.total,
      notes: data.notes,
      createdBy: data.created_by,
      isCurrent: data.is_current,
      createdAt: new Date(data.created_at)
    };

    console.log('Objeto de revisão mapeado:', {
      discount_type: revision.discount_type,
      original_percentage: revision.original_percentage,
      discount: revision.discount
    });

    return revision;
  }

  async getOrderRevisionsByOrderId(orderId: number): Promise<OrderRevision[]> {
    const { data, error } = await supabase
      .from('order_revisions')
      .select('*')
      .eq('order_id', orderId)
      .order('revision_number', { ascending: false });

    if (error || !data) return [];

    // Log para depuração
    if (data.length > 0) {
      console.log('Exemplo de dados de revisão obtidos do banco (raw):', data[0]);
      console.log('Exemplo de dados de revisão obtidos do banco (campos específicos):', {
        id: data[0].id,
        discount_type: data[0].discount_type,
        original_percentage: data[0].original_percentage,
        discount: data[0].discount
      });
    }

    const revisions = data.map(item => ({
      id: item.id,
      orderId: item.order_id,
      revisionNumber: item.revision_number,
      status: item.status,
      paymentStatus: item.payment_status || 'pendente',
      receivingMethod: item.receiving_method,
      receivingDate: item.receiving_date ? new Date(item.receiving_date) : null,
      receivingTime: item.receiving_time,
      deliveryAddress: item.delivery_address,
      paymentMethod: item.payment_method,
      subtotal: item.subtotal,
      deliveryFee: item.delivery_fee,
      discount: item.discount || 0,
      discount_type: item.discount_type || 'fixed',
      original_percentage: item.original_percentage || null,
      couponId: item.coupon_id || null,
      couponCode: item.coupon_code || null,
      couponType: item.coupon_type || null,
      total: item.total,
      notes: item.notes,
      createdBy: item.created_by,
      isCurrent: item.is_current,
      createdAt: new Date(item.created_at)
    }));

    if (revisions.length > 0) {
      console.log('Primeiro objeto de revisão mapeado:', {
        discount_type: revisions[0].discount_type,
        original_percentage: revisions[0].original_percentage,
        discount: revisions[0].discount
      });
    }

    return revisions;
  }

  async getOrderRevisionItems(revisionId: number): Promise<OrderRevisionItem[]> {
    const { data, error } = await supabase
      .from('order_revision_items')
      .select('*')
      .eq('revision_id', revisionId);

    if (error || !data) return [];

    return data.map(item => ({
      id: item.id,
      revisionId: item.revision_id,
      productId: item.product_id,
      productName: item.product_name,
      productDescription: item.product_description,
      productImage: item.product_image,
      quantity: item.quantity,
      unitPrice: item.unit_price,
      subtotal: item.subtotal,
      selectedVariations: item.selected_variations,
      observation: item.observation
    }));
  }

  async createOrderRevision(insertRevision: InsertOrderRevision): Promise<OrderRevision> {
    const { data, error } = await supabase
      .from('order_revisions')
      .insert({
        order_id: insertRevision.orderId,
        revision_number: insertRevision.revisionNumber,
        status: insertRevision.status,
        payment_status: insertRevision.paymentStatus || 'pendente',
        receiving_method: insertRevision.receivingMethod,
        receiving_date: insertRevision.receivingDate,
        receiving_time: insertRevision.receivingTime,
        delivery_address: insertRevision.deliveryAddress,
        payment_method: insertRevision.paymentMethod,
        subtotal: insertRevision.subtotal,
        delivery_fee: insertRevision.deliveryFee,
        discount: insertRevision.discount || 0,
        coupon_id: insertRevision.couponId || null,
        coupon_code: insertRevision.couponCode || null,
        coupon_type: insertRevision.couponType || null,
        total: insertRevision.total,
        notes: insertRevision.notes,
        created_by: insertRevision.createdBy,
        is_current: insertRevision.isCurrent,
        created_at: new Date()
      })
      .select('*')
      .single();

    if (error) {
      console.error('Erro ao criar revisão de pedido:', error);
      throw error;
    }

    return {
      id: data.id,
      orderId: data.order_id,
      revisionNumber: data.revision_number,
      status: data.status,
      paymentStatus: data.payment_status || 'pendente',
      receivingMethod: data.receiving_method,
      receivingDate: data.receiving_date ? new Date(data.receiving_date) : null,
      receivingTime: data.receiving_time,
      deliveryAddress: data.delivery_address,
      paymentMethod: data.payment_method,
      subtotal: data.subtotal,
      deliveryFee: data.delivery_fee,
      discount: data.discount || 0,
      discountType: data.discount_type || 'fixed',
      originalPercentage: data.original_percentage || null,
      couponId: data.coupon_id || null,
      couponCode: data.coupon_code || null,
      couponType: data.coupon_type || null,
      total: data.total,
      notes: data.notes,
      createdBy: data.created_by,
      isCurrent: data.is_current,
      createdAt: new Date(data.created_at)
    };
  }

  async createOrderRevisionItem(insertItem: InsertOrderRevisionItem): Promise<OrderRevisionItem> {
    const { data, error } = await supabase
      .from('order_revision_items')
      .insert({
        revision_id: insertItem.revisionId,
        product_id: insertItem.productId,
        product_name: insertItem.productName,
        product_description: insertItem.productDescription,
        product_image: insertItem.productImage,
        quantity: insertItem.quantity,
        unit_price: insertItem.unitPrice,
        subtotal: insertItem.subtotal,
        selected_variations: insertItem.selectedVariations,
        observation: insertItem.observation
      })
      .select('*')
      .single();

    if (error) {
      console.error('Erro ao criar item de revisão de pedido:', error);
      throw error;
    }

    return {
      id: data.id,
      revisionId: data.revision_id,
      productId: data.product_id,
      productName: data.product_name,
      productDescription: data.product_description,
      productImage: data.product_image,
      quantity: data.quantity,
      unitPrice: data.unit_price,
      subtotal: data.subtotal,
      selectedVariations: data.selected_variations,
      observation: data.observation
    };
  }

  async getLatestRevisionNumber(orderId: number): Promise<number> {
    const { data, error } = await supabase
      .from('order_revisions')
      .select('revision_number')
      .eq('order_id', orderId)
      .order('revision_number', { ascending: false })
      .limit(1)
      .single();

    if (error) {
      console.log('Nenhuma revisão encontrada para o pedido:', orderId);
      return 0; // Se não houver revisões anteriores, começa do 0
    }

    return data.revision_number || 0;
  }

  async updateOrderRevisionStatus(id: number, status: string): Promise<OrderRevision | undefined> {
    const { data, error } = await supabase
      .from('order_revisions')
      .update({ status })
      .eq('id', id)
      .select('*')
      .single();

    if (error || !data) {
      console.error('Erro ao atualizar status da revisão:', error);
      return undefined;
    }

    return {
      id: data.id,
      orderId: data.order_id,
      revisionNumber: data.revision_number,
      status: data.status,
      paymentStatus: data.payment_status || 'pendente',
      receivingMethod: data.receiving_method,
      receivingDate: data.receiving_date ? new Date(data.receiving_date) : null,
      receivingTime: data.receiving_time,
      deliveryAddress: data.delivery_address,
      paymentMethod: data.payment_method,
      subtotal: data.subtotal,
      deliveryFee: data.delivery_fee,
      discount: data.discount || 0,
      discountType: data.discount_type || 'fixed',
      originalPercentage: data.original_percentage || null,
      couponId: data.coupon_id || null,
      couponCode: data.coupon_code || null,
      couponType: data.coupon_type || null,
      total: data.total,
      notes: data.notes,
      createdBy: data.created_by,
      isCurrent: data.is_current,
      createdAt: new Date(data.created_at)
    };
  }

  async updateOrderRevision(id: number, revisionData: Partial<OrderRevision>): Promise<OrderRevision | undefined> {
    console.log('Atualizando revisão de pedido com dados:', revisionData);

    // Converter de camelCase para snake_case para o formato do Supabase
    const updateData: Record<string, any> = {};

    if ('status' in revisionData) updateData.status = revisionData.status;
    if ('paymentStatus' in revisionData) updateData.payment_status = revisionData.paymentStatus;
    if ('receivingMethod' in revisionData) updateData.receiving_method = revisionData.receivingMethod;
    if ('receivingDate' in revisionData) updateData.receiving_date = revisionData.receivingDate;
    if ('receivingTime' in revisionData) updateData.receiving_time = revisionData.receivingTime;
    if ('deliveryAddress' in revisionData) updateData.delivery_address = revisionData.deliveryAddress;
    if ('paymentMethod' in revisionData) updateData.payment_method = revisionData.paymentMethod;
    if ('subtotal' in revisionData) updateData.subtotal = revisionData.subtotal;
    if ('deliveryFee' in revisionData) updateData.delivery_fee = revisionData.deliveryFee;
    if ('discount' in revisionData) updateData.discount = revisionData.discount;
    if ('discountType' in revisionData) updateData.discount_type = revisionData.discountType;
    if ('originalPercentage' in revisionData) updateData.original_percentage = revisionData.originalPercentage;
    if ('total' in revisionData) updateData.total = revisionData.total;
    if ('notes' in revisionData) updateData.notes = revisionData.notes;
    if ('isCurrent' in revisionData) updateData.is_current = revisionData.isCurrent;

    console.log('Dados formatados para atualização:', updateData);

    const { data, error } = await supabase
      .from('order_revisions')
      .update(updateData)
      .eq('id', id)
      .select('*')
      .single();

    if (error || !data) {
      console.error('Erro ao atualizar revisão de pedido:', error);
      return undefined;
    }

    console.log('Revisão atualizada com sucesso:', data);

    return {
      id: data.id,
      orderId: data.order_id,
      revisionNumber: data.revision_number,
      status: data.status,
      paymentStatus: data.payment_status || 'pendente',
      receivingMethod: data.receiving_method,
      receivingDate: data.receiving_date ? new Date(data.receiving_date) : null,
      receivingTime: data.receiving_time,
      deliveryAddress: data.delivery_address,
      paymentMethod: data.payment_method,
      subtotal: data.subtotal,
      deliveryFee: data.delivery_fee,
      discount: data.discount || 0,
      discountType: data.discount_type || 'fixed',
      originalPercentage: data.original_percentage || null,
      couponId: data.coupon_id || null,
      couponCode: data.coupon_code || null,
      couponType: data.coupon_type || null,
      total: data.total,
      notes: data.notes,
      createdBy: data.created_by,
      isCurrent: data.is_current,
      createdAt: new Date(data.created_at)
    };
  }

  async setCurrentRevision(revisionId: number, orderId: number): Promise<void> {
    // Primeiro, remove o flag de todas as revisões deste pedido
    await supabase
      .from('order_revisions')
      .update({ is_current: false })
      .eq('order_id', orderId);

    // Em seguida, define a revisão especificada como atual
    await supabase
      .from('order_revisions')
      .update({ is_current: true })
      .eq('id', revisionId);
  }

  async clearCurrentRevisions(orderId: number): Promise<void> {
    // Define todas as revisões deste pedido como não atuais
    await supabase
      .from('order_revisions')
      .update({ is_current: false })
      .eq('order_id', orderId);
  }

  async deleteOrderRevisionItem(id: number): Promise<boolean> {
    try {
      console.log('Excluindo item de revisão com ID:', id);
      const { error } = await supabase
        .from('order_revision_items')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Erro ao excluir item de revisão:', error);
        return false;
      }

      console.log('Item de revisão excluído com sucesso');
      return true;
    } catch (error) {
      console.error('Erro ao excluir item de revisão:', error);
      return false;
    }
  }

  async deleteOrderRevision(id: number): Promise<boolean> {
    try {
      // Primeiro, exclui os itens da revisão
      const { error: itemsError } = await supabase
        .from('order_revision_items')
        .delete()
        .eq('revision_id', id);

      if (itemsError) {
        console.error('Erro ao excluir itens da revisão:', itemsError);
        return false;
      }

      // Em seguida, exclui a revisão
      const { error } = await supabase
        .from('order_revisions')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Erro ao excluir revisão:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Erro ao excluir revisão:', error);
      return false;
    }
  }

  // Coupon methods
  async getCoupon(id: number): Promise<Coupon | undefined> {
    const { data, error } = await supabase
      .from('coupons')
      .select('*')
      .eq('id', id)
      .single();

    if (error || !data) return undefined;
    return this.mapCouponFromSupabase(data);
  }

  async getCouponsByStoreId(storeId: number): Promise<Coupon[]> {
    const { data, error } = await supabase
      .from('coupons')
      .select('*')
      .eq('store_id', storeId)
      .order('created_at', { ascending: false });

    if (error || !data) return [];
    return data.map(this.mapCouponFromSupabase);
  }

  async getCouponByCode(storeId: number, code: string): Promise<Coupon | undefined> {
    console.log(`Buscando cupom com código "${code}" para loja ${storeId} (case insensitive)`);

    try {
      // Usar o operador ilike do PostgreSQL para fazer a comparação case insensitive
      const { data, error } = await supabase
        .from('coupons')
        .select('*')
        .eq('store_id', storeId)
        .ilike('code', code) // ilike faz a comparação case insensitive
        .single();

      if (error) {
        console.error('Erro ao buscar cupom por código:', error);

        // Fallback: Buscar todos os cupons da loja e filtrar manualmente
        console.log('Tentando método alternativo com filtro manual...');
        const { data: allCoupons, error: allCouponsError } = await supabase
          .from('coupons')
          .select('*')
          .eq('store_id', storeId);

        if (allCouponsError) {
          console.error('Erro ao buscar todos os cupons da loja:', allCouponsError);
          return undefined;
        }

        // Filtrar manualmente por código (case insensitive)
        const matchingCoupon = allCoupons?.find(
          coupon => coupon.code.toLowerCase() === code.toLowerCase()
        );

        if (!matchingCoupon) {
          console.log(`Nenhum cupom encontrado com código "${code}" (case insensitive) para loja ${storeId}`);
          return undefined;
        }

        console.log(`Cupom encontrado (método manual):`, matchingCoupon);
        return this.mapCouponFromSupabase(matchingCoupon);
      }

      if (!data) {
        console.log(`Nenhum cupom encontrado com código "${code}" (case insensitive) para loja ${storeId}`);
        return undefined;
      }

      console.log(`Cupom encontrado:`, data);
      return this.mapCouponFromSupabase(data);
    } catch (error) {
      console.error('Erro inesperado ao buscar cupom por código:', error);
      return undefined;
    }
  }

  async createCoupon(insertCoupon: InsertCoupon): Promise<Coupon> {
    console.log('Criando cupom com dados:', insertCoupon);

    // Mapear o tipo de cupom para o formato aceito pelo banco de dados
    const mappedType = insertCoupon.tipo === 'valor_fixo' ? 'R$' : '%';
    console.log(`Mapeando tipo de cupom: ${insertCoupon.tipo} -> ${mappedType}`);

    const { data, error } = await supabase
      .from('coupons')
      .insert({
        store_id: insertCoupon.storeId,
        code: insertCoupon.code,
        type: mappedType, // Usar o valor mapeado
        value: insertCoupon.valor,
        min_purchase: insertCoupon.minimoCompra,
        expiration_date: insertCoupon.dataValidade,
        single_use: insertCoupon.usoUnico,
        active: insertCoupon.ativo,
        created_at: new Date()
      })
      .select('*')
      .single();

    if (error) {
      console.error('Erro ao criar cupom:', error);
      throw error;
    }

    console.log('Cupom criado com sucesso:', data);
    return this.mapCouponFromSupabase(data);
  }

  async updateCoupon(id: number, couponUpdate: Partial<Coupon>): Promise<Coupon | undefined> {
    console.log('Atualizando cupom:', id, couponUpdate);

    const updateData: Record<string, any> = {};

    if (couponUpdate.code !== undefined) updateData.code = couponUpdate.code;

    // Mapear o tipo de cupom para o formato aceito pelo banco de dados
    if (couponUpdate.tipo !== undefined) {
      const mappedType = couponUpdate.tipo === 'valor_fixo' ? 'R$' : '%';
      console.log(`Mapeando tipo de cupom: ${couponUpdate.tipo} -> ${mappedType}`);
      updateData.type = mappedType;
    }

    if (couponUpdate.valor !== undefined) updateData.value = couponUpdate.valor;
    if (couponUpdate.minimoCompra !== undefined) updateData.min_purchase = couponUpdate.minimoCompra;
    if (couponUpdate.dataValidade !== undefined) updateData.expiration_date = couponUpdate.dataValidade;
    if (couponUpdate.usoUnico !== undefined) updateData.single_use = couponUpdate.usoUnico;
    if (couponUpdate.ativo !== undefined) updateData.active = couponUpdate.ativo;

    console.log('Dados formatados para atualização:', updateData);

    const { data, error } = await supabase
      .from('coupons')
      .update(updateData)
      .eq('id', id)
      .select('*')
      .single();

    if (error || !data) {
      console.error('Erro ao atualizar cupom:', error);
      return undefined;
    }

    console.log('Cupom atualizado com sucesso:', data);
    return this.mapCouponFromSupabase(data);
  }

  async updateCouponStatus(id: number, active: boolean): Promise<Coupon | undefined> {
    console.log(`Atualizando status do cupom ${id} para ${active ? 'ativo' : 'inativo'}`);

    const { data, error } = await supabase
      .from('coupons')
      .update({ active })
      .eq('id', id)
      .select('*')
      .single();

    if (error || !data) {
      console.error('Erro ao atualizar status do cupom:', error);
      return undefined;
    }

    console.log('Status do cupom atualizado com sucesso:', data);
    return this.mapCouponFromSupabase(data);
  }

  // Coupon Usage methods
  async createCouponUsage(insertUsage: InsertCouponUsage): Promise<CouponUsage> {
    console.log('Registrando uso de cupom:', insertUsage);

    try {
      const { data, error } = await supabase
        .from('coupon_usage')
        .insert({
          coupon_id: insertUsage.couponId,
          customer_id: insertUsage.customerId,
          order_id: insertUsage.orderId,
          used_at: new Date()
        })
        .select('*')
        .single();

      if (error) {
        console.error('Erro ao registrar uso de cupom:', error);
        console.error('Detalhes do erro:', error.message, error.details, error.hint);
        throw error;
      }

      console.log('Uso de cupom registrado com sucesso:', data);
      return {
        id: data.id,
        couponId: data.coupon_id,
        customerId: data.customer_id,
        orderId: data.order_id,
        usedAt: new Date(data.used_at)
      };
    } catch (error) {
      console.error('Exceção ao registrar uso de cupom:', error);
      // Verificar se é um erro de violação de chave única
      if (error.code === '23505') {
        console.log('Este cupom já foi registrado para este pedido. Ignorando duplicação.');
        // Retornar um objeto simulado para não interromper o fluxo
        return {
          id: -1, // ID temporário
          couponId: insertUsage.couponId,
          customerId: insertUsage.customerId,
          orderId: insertUsage.orderId,
          usedAt: new Date()
        };
      }
      throw error;
    }
  }

  async getCouponUsagesByCouponId(couponId: number): Promise<CouponUsage[]> {
    const { data, error } = await supabase
      .from('coupon_usage')
      .select('*')
      .eq('coupon_id', couponId)
      .order('used_at', { ascending: false });

    if (error || !data) return [];

    return data.map(usage => ({
      id: usage.id,
      couponId: usage.coupon_id,
      customerId: usage.customer_id,
      orderId: usage.order_id,
      usedAt: new Date(usage.used_at)
    }));
  }

  async getCouponUsagesByCustomerId(customerId: number): Promise<CouponUsage[]> {
    const { data, error } = await supabase
      .from('coupon_usage')
      .select('*')
      .eq('customer_id', customerId)
      .order('used_at', { ascending: false });

    if (error || !data) return [];

    return data.map(usage => ({
      id: usage.id,
      couponId: usage.coupon_id,
      customerId: usage.customer_id,
      orderId: usage.order_id,
      usedAt: new Date(usage.used_at)
    }));
  }

  // Helper para mapear dados do formato do Supabase para o nosso schema
  private mapCouponFromSupabase(data: any): Coupon {
    // Mapear o tipo de cupom do formato do banco de dados para o formato esperado pelo frontend
    const mappedType = data.type === 'R$' ? 'valor_fixo' : 'percentual';
    console.log(`Mapeando tipo de cupom (do banco para frontend): ${data.type} -> ${mappedType}`);

    return {
      id: data.id,
      storeId: data.store_id,
      code: data.code,
      tipo: mappedType,
      valor: data.value,
      minimoCompra: data.min_purchase,
      dataValidade: data.expiration_date,
      usoUnico: data.single_use,
      ativo: data.active,
      createdAt: new Date(data.created_at)
    };
  }

  // Subscription methods
  async getSubscription(id: number): Promise<Subscription | undefined> {
    const { data, error } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('id', id)
      .single();

    if (error || !data) return undefined;
    return this.mapSubscriptionFromSupabase(data);
  }

  async getSubscriptionsByStoreId(storeId: number): Promise<Subscription[]> {
    const { data, error } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('store_id', storeId)
      .order('created_at', { ascending: false });

    if (error || !data) return [];
    return data.map(this.mapSubscriptionFromSupabase.bind(this));
  }

  async getSubscriptionByStripeId(stripeSubscriptionId: string): Promise<Subscription | undefined> {
    const { data, error } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('stripe_subscription_id', stripeSubscriptionId)
      .single();

    if (error || !data) return undefined;
    return this.mapSubscriptionFromSupabase(data);
  }

  async getActiveSubscription(storeId: number): Promise<Subscription | undefined> {
    const { data, error } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('store_id', storeId)
      .eq('status', 'active')
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (error || !data) return undefined;
    return this.mapSubscriptionFromSupabase(data);
  }

  async getAllSubscriptions(): Promise<Subscription[]> {
    const { data, error } = await supabase
      .from('subscriptions')
      .select('*')
      .order('created_at', { ascending: false });

    if (error || !data) {
      console.error('Erro ao buscar todas as assinaturas:', error);
      return [];
    }

    return data.map(this.mapSubscriptionFromSupabase.bind(this));
  }

  async createSubscription(insertSubscription: InsertSubscription): Promise<Subscription> {
    const { data, error } = await supabase
      .from('subscriptions')
      .insert({
        store_id: insertSubscription.storeId,
        stripe_customer_id: insertSubscription.stripeCustomerId,
        stripe_subscription_id: insertSubscription.stripeSubscriptionId,
        plan_type: insertSubscription.planType,
        status: insertSubscription.status,
        current_period_start: insertSubscription.currentPeriodStart,
        current_period_end: insertSubscription.currentPeriodEnd,
        trial_end: insertSubscription.trialEnd,
        cancel_at_period_end: insertSubscription.cancelAtPeriodEnd,
        created_at: new Date(),
        updated_at: new Date()
      })
      .select('*')
      .single();

    if (error) {
      console.error('Erro ao criar assinatura:', error);
      throw error;
    }

    return this.mapSubscriptionFromSupabase(data);
  }

  async updateSubscription(id: number, subscriptionUpdate: Partial<Subscription>): Promise<Subscription | undefined> {
    const updateData: any = {};

    // Mapear campos para o formato snake_case do Supabase
    if (subscriptionUpdate.stripeCustomerId !== undefined) updateData.stripe_customer_id = subscriptionUpdate.stripeCustomerId;
    if (subscriptionUpdate.stripeSubscriptionId !== undefined) updateData.stripe_subscription_id = subscriptionUpdate.stripeSubscriptionId;
    if (subscriptionUpdate.planType !== undefined) updateData.plan_type = subscriptionUpdate.planType;
    if (subscriptionUpdate.status !== undefined) updateData.status = subscriptionUpdate.status;
    if (subscriptionUpdate.currentPeriodStart !== undefined) updateData.current_period_start = subscriptionUpdate.currentPeriodStart;
    if (subscriptionUpdate.currentPeriodEnd !== undefined) updateData.current_period_end = subscriptionUpdate.currentPeriodEnd;
    if (subscriptionUpdate.trialEnd !== undefined) updateData.trial_end = subscriptionUpdate.trialEnd;
    if (subscriptionUpdate.cancelAtPeriodEnd !== undefined) updateData.cancel_at_period_end = subscriptionUpdate.cancelAtPeriodEnd;

    updateData.updated_at = new Date();

    const { data, error } = await supabase
      .from('subscriptions')
      .update(updateData)
      .eq('id', id)
      .select('*')
      .single();

    if (error || !data) {
      console.error('Erro ao atualizar assinatura:', error);
      return undefined;
    }

    return this.mapSubscriptionFromSupabase(data);
  }

  async deleteSubscription(id: number): Promise<boolean> {
    const { error } = await supabase
      .from('subscriptions')
      .delete()
      .eq('id', id);

    return !error;
  }

  private mapSubscriptionFromSupabase(data: any): Subscription {
    return {
      id: data.id,
      storeId: data.store_id,
      stripeCustomerId: data.stripe_customer_id,
      stripeSubscriptionId: data.stripe_subscription_id,
      planType: data.plan_type,
      status: data.status,
      currentPeriodStart: data.current_period_start ? new Date(data.current_period_start) : null,
      currentPeriodEnd: data.current_period_end ? new Date(data.current_period_end) : null,
      trialEnd: data.trial_end ? new Date(data.trial_end) : null,
      cancelAtPeriodEnd: data.cancel_at_period_end,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at)
    };
  }

  // Global Settings methods
  async getGlobalSetting(key: string): Promise<GlobalSetting | undefined> {
    const { data, error } = await supabase
      .from('global_settings')
      .select('*')
      .eq('key', key)
      .single();

    if (error || !data) return undefined;
    return this.mapGlobalSettingFromSupabase(data);
  }

  async getAllGlobalSettings(): Promise<GlobalSetting[]> {
    const { data, error } = await supabase
      .from('global_settings')
      .select('*')
      .order('key', { ascending: true });

    if (error || !data) {
      console.error('Erro ao buscar configurações globais:', error);
      return [];
    }

    return data.map(this.mapGlobalSettingFromSupabase.bind(this));
  }

  async updateGlobalSetting(key: string, value: GlobalSettingsValue, updatedBy?: number): Promise<GlobalSetting | undefined> {
    const { data, error } = await supabase
      .from('global_settings')
      .update({
        value: value,
        updated_at: new Date(),
        updated_by: updatedBy
      })
      .eq('key', key)
      .select('*')
      .single();

    if (error || !data) {
      console.error('Erro ao atualizar configuração global:', error);
      return undefined;
    }

    return this.mapGlobalSettingFromSupabase(data);
  }

  async createGlobalSetting(key: string, value: GlobalSettingsValue, description?: string, updatedBy?: number): Promise<GlobalSetting> {
    const { data, error } = await supabase
      .from('global_settings')
      .insert({
        key: key,
        value: value,
        description: description,
        updated_at: new Date(),
        updated_by: updatedBy
      })
      .select('*')
      .single();

    if (error) {
      console.error('Erro ao criar configuração global:', error);
      throw error;
    }

    return this.mapGlobalSettingFromSupabase(data);
  }

  private mapGlobalSettingFromSupabase(data: any): GlobalSetting {
    return {
      id: data.id,
      key: data.key,
      value: data.value,
      description: data.description,
      updatedAt: new Date(data.updated_at),
      updatedBy: data.updated_by
    };
  }
}
