import Stripe from 'stripe';
import dotenv from 'dotenv';

// Carregar variáveis de ambiente
dotenv.config();

// Verificar se as chaves do Stripe estão configuradas
const stripeSecretKey = process.env.STRIPE_SECRET_KEY;
const stripeWebhookSecret = process.env.STRIPE_WEBHOOK_SECRET;

if (!stripeSecretKey) {
  console.warn('⚠️ STRIPE_SECRET_KEY não configurada. Funcionalidades de assinatura não estarão disponíveis.');
}

// Inicializar Stripe
export const stripe = stripeSecretKey ? new Stripe(stripeSecretKey, {
  apiVersion: '2024-12-18.acacia',
  typescript: true,
}) : null;

export const STRIPE_CONFIG = {
  webhookSecret: stripeWebhookSecret,
  premiumMonthlyPriceId: process.env.STRIPE_PREMIUM_MONTHLY_PRICE_ID,
  premiumYearlyPriceId: process.env.STRIPE_PREMIUM_YEARLY_PRICE_ID,
  successUrl: process.env.STRIPE_SUCCESS_URL || 'http://localhost:3000/admin/settings?subscription=success',
  cancelUrl: process.env.STRIPE_CANCEL_URL || 'http://localhost:3000/admin/settings?subscription=canceled',
  customerPortalUrl: process.env.STRIPE_CUSTOMER_PORTAL_URL,
};

// Função para verificar se o Stripe está configurado
export function isStripeConfigured(): boolean {
  return !!stripe && !!stripeSecretKey;
}

// Função para criar um cliente no Stripe
export async function createStripeCustomer(email: string, name: string, storeId: number): Promise<Stripe.Customer | null> {
  if (!stripe) {
    console.error('Stripe não configurado');
    return null;
  }

  try {
    const customer = await stripe.customers.create({
      email,
      name,
      metadata: {
        storeId: storeId.toString(),
      },
    });

    return customer;
  } catch (error) {
    console.error('Erro ao criar cliente no Stripe:', error);
    return null;
  }
}

// Função para criar uma sessão de checkout
export async function createCheckoutSession(
  customerId: string,
  priceId: string,
  storeId: number,
  interval: 'month' | 'year' = 'month',
  trialDays: number = 7
): Promise<Stripe.Checkout.Session | null> {
  if (!stripe) {
    console.error('Stripe não configurado');
    return null;
  }

  try {
    const session = await stripe.checkout.sessions.create({
      customer: customerId,
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      mode: 'subscription',
      success_url: `${STRIPE_CONFIG.successUrl}&store_id=${storeId}&interval=${interval}`,
      cancel_url: `${STRIPE_CONFIG.cancelUrl}&store_id=${storeId}&interval=${interval}`,
      subscription_data: {
        // Trial apenas para planos mensais
        ...(interval === 'month' && trialDays > 0 ? { trial_period_days: trialDays } : {}),
        metadata: {
          storeId: storeId.toString(),
          interval,
        },
      },
      metadata: {
        storeId: storeId.toString(),
        interval,
      },
    });

    return session;
  } catch (error) {
    console.error('Erro ao criar sessão de checkout:', error);
    return null;
  }
}

// Função para criar link do Customer Portal
export async function createCustomerPortalSession(customerId: string, returnUrl: string): Promise<string | null> {
  if (!stripe) {
    console.error('Stripe não configurado');
    return null;
  }

  try {
    const session = await stripe.billingPortal.sessions.create({
      customer: customerId,
      return_url: returnUrl,
    });

    return session.url;
  } catch (error) {
    console.error('Erro ao criar sessão do Customer Portal:', error);
    return null;
  }
}

// Função para cancelar assinatura
export async function cancelSubscription(subscriptionId: string, cancelAtPeriodEnd: boolean = true): Promise<Stripe.Subscription | null> {
  if (!stripe) {
    console.error('Stripe não configurado');
    return null;
  }

  try {
    const subscription = await stripe.subscriptions.update(subscriptionId, {
      cancel_at_period_end: cancelAtPeriodEnd,
    });

    return subscription;
  } catch (error) {
    console.error('Erro ao cancelar assinatura:', error);
    return null;
  }
}

// Função para reativar assinatura
export async function reactivateSubscription(subscriptionId: string): Promise<Stripe.Subscription | null> {
  if (!stripe) {
    console.error('Stripe não configurado');
    return null;
  }

  try {
    const subscription = await stripe.subscriptions.update(subscriptionId, {
      cancel_at_period_end: false,
    });

    return subscription;
  } catch (error) {
    console.error('Erro ao reativar assinatura:', error);
    return null;
  }
}

// Função para obter informações de uma assinatura
export async function getSubscription(subscriptionId: string): Promise<Stripe.Subscription | null> {
  if (!stripe) {
    console.error('Stripe não configurado');
    return null;
  }

  try {
    const subscription = await stripe.subscriptions.retrieve(subscriptionId);
    return subscription;
  } catch (error) {
    console.error('Erro ao obter assinatura:', error);
    return null;
  }
}

// Função para verificar webhook signature
export function verifyWebhookSignature(payload: string | Buffer, signature: string): Stripe.Event | null {
  if (!stripe || !STRIPE_CONFIG.webhookSecret) {
    console.error('Stripe ou webhook secret não configurado');
    return null;
  }

  try {
    const event = stripe.webhooks.constructEvent(payload, signature, STRIPE_CONFIG.webhookSecret);
    return event;
  } catch (error) {
    console.error('Erro ao verificar assinatura do webhook:', error);
    return null;
  }
}

export default stripe;
