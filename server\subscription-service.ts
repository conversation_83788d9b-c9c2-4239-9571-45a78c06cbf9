import { storage } from './storage';
import { 
  type Subscription, 
  type InsertSubscription, 
  type PlanType, 
  type SubscriptionStatus,
  PLAN_CONFIGS,
  getFeatureLimit,
  isFeatureAvailable,
  isLimitExceeded
} from '@shared/schema';
import { 
  stripe, 
  createStripeCustomer, 
  createCheckoutSession, 
  createCustomerPortalSession,
  cancelSubscription,
  reactivateSubscription,
  getSubscription,
  isStripeConfigured,
  STRIPE_CONFIG
} from './stripe-config';

export class SubscriptionService {
  /**
   * Verifica se um trial expirou (validação local independente do Stripe)
   * @param subscription - Assinatura a ser verificada
   * @returns true se o trial expirou, false caso contrário
   */
  private isTrialExpired(subscription: Subscription): boolean {
    if (!subscription.trialEnd) {
      return false; // Sem trial, não pode estar expirado
    }

    const now = new Date();
    const trialEndDate = new Date(subscription.trialEnd);

    // Usar UTC para evitar problemas de timezone
    const nowUTC = new Date(now.getTime() + (now.getTimezoneOffset() * 60000));
    const trialEndUTC = new Date(trialEndDate.getTime() + (trialEndDate.getTimezoneOffset() * 60000));

    return trialEndUTC <= nowUTC;
  }

  /**
   * Verifica se uma assinatura é válida (ativa e trial não expirado)
   * @param subscription - Assinatura a ser verificada
   * @returns true se a assinatura é válida, false caso contrário
   */
  private isSubscriptionValid(subscription: Subscription): boolean {
    // Verificar se status é ativo
    if (subscription.status !== 'active') {
      return false;
    }

    // Para planos premium, verificar se trial não expirou
    if (subscription.planType === 'premium' && this.isTrialExpired(subscription)) {
      console.log(`Trial expirado para assinatura ${subscription.id}, trialEnd: ${subscription.trialEnd}`);
      return false;
    }

    return true;
  }

  /**
   * Obter assinatura ativa e válida de uma loja (com validação local de trial)
   * @param storeId - ID da loja
   * @returns Assinatura válida ou null
   */
  async getActiveSubscription(storeId: number): Promise<Subscription | null> {
    try {
      const subscriptions = await storage.getSubscriptionsByStoreId(storeId);
      const activeSubscription = subscriptions.find(sub => sub.status === 'active');

      if (!activeSubscription) {
        return null;
      }

      // Validação local independente do Stripe
      if (!this.isSubscriptionValid(activeSubscription)) {
        // Trial expirado - marcar como inativo localmente
        await this.handleExpiredTrial(activeSubscription);
        return null;
      }

      return activeSubscription;
    } catch (error) {
      console.error('Erro ao obter assinatura ativa:', error);
      return null;
    }
  }

  /**
   * Lidar com trial expirado (fallback local quando Stripe não está disponível)
   * @param subscription - Assinatura com trial expirado
   */
  private async handleExpiredTrial(subscription: Subscription): Promise<void> {
    try {
      console.log(`🔄 Processando trial expirado para assinatura ${subscription.id}`);

      // Tentar sincronizar com Stripe primeiro (se disponível)
      if (isStripeConfigured() && subscription.stripeSubscriptionId) {
        try {
          await this.syncSubscriptionWithStripe(subscription.stripeSubscriptionId);
          console.log(`✅ Sincronização com Stripe concluída para assinatura ${subscription.id}`);
          return;
        } catch (stripeError) {
          console.warn(`⚠️ Falha na sincronização com Stripe, usando fallback local:`, stripeError);
        }
      }

      // Fallback local: atualizar status para canceled
      await storage.updateSubscription(subscription.id, {
        status: 'canceled',
        cancelAtPeriodEnd: true,
      });

      console.log(`✅ Trial expirado processado localmente para assinatura ${subscription.id}`);
    } catch (error) {
      console.error(`❌ Erro ao processar trial expirado para assinatura ${subscription.id}:`, error);
    }
  }

  // Criar assinatura gratuita padrão para nova loja
  async createFreeSubscription(storeId: number): Promise<Subscription | null> {
    try {
      const subscriptionData: InsertSubscription = {
        storeId,
        planType: 'free',
        status: 'active',
      };

      const subscription = await storage.createSubscription(subscriptionData);
      return subscription;
    } catch (error) {
      console.error('Erro ao criar assinatura gratuita:', error);
      return null;
    }
  }

  // Iniciar processo de upgrade para plano premium
  async createPremiumCheckoutSession(
    storeId: number,
    userEmail: string,
    userName: string,
    interval: 'month' | 'year' = 'month'
  ): Promise<string | null> {
    if (!isStripeConfigured()) {
      throw new Error('Stripe não configurado');
    }

    try {
      // Obter ou criar assinatura atual
      let subscription = await this.getActiveSubscription(storeId);

      if (!subscription) {
        subscription = await this.createFreeSubscription(storeId);
        if (!subscription) {
          throw new Error('Erro ao criar assinatura');
        }
      }

      // Criar ou obter cliente no Stripe
      let stripeCustomerId = subscription.stripeCustomerId;

      if (!stripeCustomerId) {
        const stripeCustomer = await createStripeCustomer(userEmail, userName, storeId);
        if (!stripeCustomer) {
          throw new Error('Erro ao criar cliente no Stripe');
        }

        stripeCustomerId = stripeCustomer.id;

        // Atualizar assinatura com ID do cliente
        await storage.updateSubscription(subscription.id, {
          stripeCustomerId,
        });
      }

      // Selecionar price ID baseado no intervalo
      const premiumPriceId = interval === 'year'
        ? STRIPE_CONFIG.premiumYearlyPriceId
        : STRIPE_CONFIG.premiumMonthlyPriceId;

      if (!premiumPriceId) {
        throw new Error(`Price ID do plano premium ${interval === 'year' ? 'anual' : 'mensal'} não configurado`);
      }

      const checkoutSession = await createCheckoutSession(
        stripeCustomerId,
        premiumPriceId,
        storeId,
        interval,
        interval === 'month' ? 7 : 0 // Trial apenas para planos mensais
      );

      if (!checkoutSession) {
        throw new Error('Erro ao criar sessão de checkout');
      }

      return checkoutSession.url || null;
    } catch (error) {
      console.error('Erro ao criar sessão de checkout premium:', error);
      throw error;
    }
  }

  // Obter link do Customer Portal
  async getCustomerPortalUrl(storeId: number): Promise<string | null> {
    if (!isStripeConfigured()) {
      throw new Error('Stripe não configurado');
    }

    try {
      const subscription = await this.getActiveSubscription(storeId);
      
      if (!subscription?.stripeCustomerId) {
        throw new Error('Cliente Stripe não encontrado');
      }

      const returnUrl = `${process.env.BASE_URL || 'http://localhost:5000'}/admin/settings`;
      const portalUrl = await createCustomerPortalSession(subscription.stripeCustomerId, returnUrl);
      
      return portalUrl;
    } catch (error) {
      console.error('Erro ao obter URL do Customer Portal:', error);
      throw error;
    }
  }

  // Verificar se uma funcionalidade está disponível
  async isFeatureAvailableForStore(storeId: number, feature: keyof typeof PLAN_CONFIGS.free.limits): Promise<boolean> {
    try {
      const subscription = await this.getActiveSubscription(storeId);
      const planType = subscription?.planType || 'free';

      // Buscar configurações globais
      const globalSettings = await this.getGlobalPlanLimits();

      return isFeatureAvailable(planType as PlanType, feature, globalSettings);
    } catch (error) {
      console.error('Erro ao verificar disponibilidade de funcionalidade:', error);
      return false; // Em caso de erro, negar acesso
    }
  }

  // Verificar se um limite foi excedido
  async isLimitExceededForStore(
    storeId: number,
    feature: 'maxProducts' | 'maxOrdersPerMonth',
    currentCount: number
  ): Promise<boolean> {
    try {
      const subscription = await this.getActiveSubscription(storeId);
      const planType = subscription?.planType || 'free';

      // Buscar configurações globais
      const globalSettings = await this.getGlobalPlanLimits();

      return isLimitExceeded(planType as PlanType, feature, currentCount, globalSettings);
    } catch (error) {
      console.error('Erro ao verificar limite:', error);
      return true; // Em caso de erro, considerar limite excedido
    }
  }

  // Buscar configurações globais de limites de planos
  private async getGlobalPlanLimits() {
    try {
      const setting = await storage.getGlobalSetting('plan_limits');
      return setting?.value || undefined;
    } catch (error) {
      console.error('Erro ao buscar configurações globais:', error);
      return undefined;
    }
  }

  // Obter contagem atual de produtos
  async getCurrentProductCount(storeId: number): Promise<number> {
    try {
      const products = await storage.getProductsByStoreId(storeId);
      return products.length;
    } catch (error) {
      console.error('Erro ao obter contagem de produtos:', error);
      return 0;
    }
  }

  // Obter contagem de pedidos do mês atual
  async getCurrentMonthOrderCount(storeId: number): Promise<number> {
    try {
      const now = new Date();
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
      
      const orders = await storage.getOrdersByStoreId(storeId);
      const monthOrders = orders.filter(order => {
        const orderDate = new Date(order.createdAt);
        return orderDate >= startOfMonth && orderDate <= endOfMonth;
      });
      
      return monthOrders.length;
    } catch (error) {
      console.error('Erro ao obter contagem de pedidos do mês:', error);
      return 0;
    }
  }

  // Sincronizar assinatura com dados do Stripe
  async syncSubscriptionWithStripe(subscriptionId: string): Promise<void> {
    if (!isStripeConfigured()) {
      console.warn('Stripe não configurado, não é possível sincronizar');
      return;
    }

    try {
      console.log(`🔄 Iniciando sincronização da assinatura: ${subscriptionId}`);

      const stripeSubscription = await getSubscription(subscriptionId);
      if (!stripeSubscription) {
        console.error(`❌ Assinatura não encontrada no Stripe: ${subscriptionId}`);
        return;
      }

      console.log(`✅ Assinatura encontrada no Stripe:`, {
        id: stripeSubscription.id,
        status: stripeSubscription.status,
        trial_end: stripeSubscription.trial_end,
        current_period_start: stripeSubscription.current_period_start,
        current_period_end: stripeSubscription.current_period_end,
        metadata: stripeSubscription.metadata
      });

      // Encontrar assinatura local
      let localSubscription = await storage.getSubscriptionByStripeId(subscriptionId);

      // Se não encontrou pela ID do Stripe, tentar encontrar pela metadata da assinatura
      if (!localSubscription && stripeSubscription.metadata?.storeId) {
        const storeId = parseInt(stripeSubscription.metadata.storeId);
        console.log(`Tentando encontrar assinatura pela loja ${storeId}...`);

        // Buscar assinatura ativa da loja que ainda não tem stripeSubscriptionId
        const storeSubscriptions = await storage.getSubscriptionsByStoreId(storeId);
        localSubscription = storeSubscriptions.find(sub =>
          sub.status === 'active' &&
          !sub.stripeSubscriptionId &&
          sub.stripeCustomerId === stripeSubscription.customer
        );

        if (localSubscription) {
          console.log(`Assinatura local encontrada pela loja ${storeId}, atualizando com Stripe ID...`);
          // Atualizar a assinatura local com o ID do Stripe
          await storage.updateSubscription(localSubscription.id, {
            stripeSubscriptionId: subscriptionId,
          });
          // Recarregar a assinatura atualizada
          localSubscription = await storage.getSubscription(localSubscription.id);
        }
      }

      if (!localSubscription) {
        console.error('Assinatura local não encontrada:', subscriptionId);
        return;
      }

      // Determinar tipo de plano baseado no price ID
      let planType: PlanType = 'free';
      if (stripeSubscription.items.data.length > 0) {
        const priceId = stripeSubscription.items.data[0].price.id;
        if (priceId === STRIPE_CONFIG.premiumMonthlyPriceId || priceId === STRIPE_CONFIG.premiumYearlyPriceId) {
          planType = 'premium';
        }
      }

      // Mapear status do Stripe para nosso sistema
      const statusMap: Record<string, SubscriptionStatus> = {
        'active': 'active',
        'past_due': 'past_due',
        'canceled': 'canceled',
        'unpaid': 'unpaid',
        'incomplete': 'incomplete',
        'incomplete_expired': 'canceled',
        'trialing': 'active',
      };

      const status = statusMap[stripeSubscription.status] || 'canceled';

      // Preparar dados para atualização
      const updateData = {
        planType,
        status,
        currentPeriodStart: new Date(stripeSubscription.current_period_start * 1000),
        currentPeriodEnd: new Date(stripeSubscription.current_period_end * 1000),
        trialEnd: stripeSubscription.trial_end ? new Date(stripeSubscription.trial_end * 1000) : null,
        cancelAtPeriodEnd: stripeSubscription.cancel_at_period_end,
      };

      console.log(`📝 Atualizando assinatura local com dados:`, {
        subscriptionId: localSubscription.id,
        planType: updateData.planType,
        status: updateData.status,
        trialEnd: updateData.trialEnd,
        currentPeriodStart: updateData.currentPeriodStart,
        currentPeriodEnd: updateData.currentPeriodEnd
      });

      // Atualizar assinatura local
      await storage.updateSubscription(localSubscription.id, updateData);

      console.log(`✅ Assinatura ${subscriptionId} sincronizada com sucesso`);
    } catch (error) {
      console.error('Erro ao sincronizar assinatura:', error);
    }
  }

  // Obter informações de uso e limites para uma loja
  async getUsageInfo(storeId: number) {
    try {
      const subscription = await this.getActiveSubscription(storeId);
      const planType = subscription?.planType || 'free';
      const planConfig = PLAN_CONFIGS[planType as PlanType];

      // Buscar configurações globais
      const globalSettings = await this.getGlobalPlanLimits();

      const currentProductCount = await this.getCurrentProductCount(storeId);
      const currentOrderCount = await this.getCurrentMonthOrderCount(storeId);

      // Usar configurações globais se disponíveis, senão usar configurações estáticas
      const maxProducts = getFeatureLimit(planType as PlanType, 'maxProducts', globalSettings);
      const maxOrdersPerMonth = getFeatureLimit(planType as PlanType, 'maxOrdersPerMonth', globalSettings);

      // Criar objeto de funcionalidades com configurações globais aplicadas
      const features = {
        maxProducts: maxProducts,
        maxOrdersPerMonth: maxOrdersPerMonth,
        allowPdfGeneration: isFeatureAvailable(planType as PlanType, 'allowPdfGeneration', globalSettings),
        allowAnalytics: isFeatureAvailable(planType as PlanType, 'allowAnalytics', globalSettings),
        allowWhatsappIntegration: isFeatureAvailable(planType as PlanType, 'allowWhatsappIntegration', globalSettings),
        allowCoupons: isFeatureAvailable(planType as PlanType, 'allowCoupons', globalSettings),
        allowCustomization: isFeatureAvailable(planType as PlanType, 'allowCustomization', globalSettings),
      };

      return {
        subscription,
        planConfig,
        usage: {
          products: {
            current: currentProductCount,
            limit: maxProducts,
            isLimitExceeded: isLimitExceeded(planType as PlanType, 'maxProducts', currentProductCount, globalSettings),
          },
          orders: {
            current: currentOrderCount,
            limit: maxOrdersPerMonth,
            isLimitExceeded: maxOrdersPerMonth !== -1 &&
                             isLimitExceeded(planType as PlanType, 'maxOrdersPerMonth', currentOrderCount, globalSettings),
          },
        },
        features: features,
      };
    } catch (error) {
      console.error('Erro ao obter informações de uso:', error);
      throw error;
    }
  }

  /**
   * Processar todos os trials expirados no sistema (job de limpeza)
   * @returns Número de trials processados
   */
  async processExpiredTrials(): Promise<number> {
    try {
      console.log('🔄 Iniciando processamento de trials expirados...');

      // Buscar todas as assinaturas ativas com trial
      const allStores = await storage.getAllStores();
      let processedCount = 0;

      for (const store of allStores) {
        try {
          const subscriptions = await storage.getSubscriptionsByStoreId(store.id);
          const activeSubscription = subscriptions.find(sub => sub.status === 'active');

          if (activeSubscription && activeSubscription.planType === 'premium' && this.isTrialExpired(activeSubscription)) {
            console.log(`🔄 Processando trial expirado para loja ${store.id}`);
            await this.handleExpiredTrial(activeSubscription);
            processedCount++;
          }
        } catch (storeError) {
          console.error(`Erro ao processar loja ${store.id}:`, storeError);
        }
      }

      console.log(`✅ Processamento concluído: ${processedCount} trials expirados processados`);
      return processedCount;
    } catch (error) {
      console.error('Erro no processamento de trials expirados:', error);
      return 0;
    }
  }

  /**
   * Verificar saúde da sincronização com Stripe
   * @returns Status da sincronização
   */
  async checkStripeSyncHealth(): Promise<{
    isHealthy: boolean;
    stripeAvailable: boolean;
    inconsistencies: number;
    lastSyncCheck: Date;
  }> {
    const result = {
      isHealthy: true,
      stripeAvailable: isStripeConfigured(),
      inconsistencies: 0,
      lastSyncCheck: new Date()
    };

    if (!result.stripeAvailable) {
      console.warn('⚠️ Stripe não configurado - funcionando apenas com validação local');
      return result;
    }

    try {
      // Verificar algumas assinaturas para detectar inconsistências
      const allStores = await storage.getAllStores();
      const sampleStores = allStores.slice(0, 10); // Verificar apenas uma amostra

      for (const store of sampleStores) {
        try {
          const subscriptions = await storage.getSubscriptionsByStoreId(store.id);
          const premiumSub = subscriptions.find(sub => sub.planType === 'premium' && sub.stripeSubscriptionId);

          if (premiumSub?.stripeSubscriptionId) {
            // Tentar buscar no Stripe
            const stripeData = await this.getStripeSubscriptionDetails(premiumSub.stripeSubscriptionId);

            // Verificar inconsistências básicas
            if (stripeData.status !== premiumSub.status) {
              result.inconsistencies++;
              console.warn(`Inconsistência detectada: Loja ${store.id}, Local: ${premiumSub.status}, Stripe: ${stripeData.status}`);
            }
          }
        } catch (storeError) {
          // Erro específico da loja não afeta a saúde geral
          console.warn(`Erro ao verificar loja ${store.id}:`, storeError);
        }
      }

      result.isHealthy = result.inconsistencies === 0;
    } catch (error) {
      console.error('Erro na verificação de saúde do Stripe:', error);
      result.isHealthy = false;
    }

    return result;
  }

  // Buscar detalhes de uma assinatura no Stripe (para admin global)
  async getStripeSubscriptionDetails(stripeSubscriptionId: string): Promise<any> {
    if (!isStripeConfigured() || !stripe) {
      throw new Error('Stripe não configurado');
    }

    try {
      const subscription = await stripe.subscriptions.retrieve(stripeSubscriptionId, {
        expand: ['latest_invoice', 'customer', 'items.data.price']
      });

      return {
        id: subscription.id,
        status: subscription.status,
        currentPeriodStart: new Date(subscription.current_period_start * 1000),
        currentPeriodEnd: new Date(subscription.current_period_end * 1000),
        cancelAtPeriodEnd: subscription.cancel_at_period_end,
        canceledAt: subscription.canceled_at ? new Date(subscription.canceled_at * 1000) : null,
        trialStart: subscription.trial_start ? new Date(subscription.trial_start * 1000) : null,
        trialEnd: subscription.trial_end ? new Date(subscription.trial_end * 1000) : null,
        customer: subscription.customer,
        latestInvoice: subscription.latest_invoice,
        items: subscription.items.data.map(item => ({
          id: item.id,
          priceId: item.price.id,
          productId: item.price.product,
          quantity: item.quantity,
          amount: item.price.unit_amount,
          currency: item.price.currency,
          interval: item.price.recurring?.interval,
          intervalCount: item.price.recurring?.interval_count
        }))
      };
    } catch (error) {
      console.error('Erro ao buscar detalhes da assinatura no Stripe:', error);
      throw error;
    }
  }
}

export const subscriptionService = new SubscriptionService();
