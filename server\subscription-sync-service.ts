import { storage } from './storage';
import { 
  type Subscription, 
  type InsertSubscription, 
  type PlanType, 
  type SubscriptionStatus,
} from '@shared/schema';
import { 
  stripe, 
  getSubscription,
  isStripeConfigured,
  STRIPE_CONFIG
} from './stripe-config';

// Interface para resultados de sincronização
export interface SyncResult {
  success: boolean;
  subscriptionId?: string;
  localId?: number;
  error?: string;
  changes?: string[];
  timestamp: Date;
}

// Interface para resultados de sincronização em lote
export interface BulkSyncResult {
  totalProcessed: number;
  successful: number;
  failed: number;
  results: SyncResult[];
  duration: number;
}

// Interface para validação de dados
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// Interface para histórico de sincronização
export interface SyncHistory {
  id: string;
  subscriptionId: string;
  localId: number;
  operation: 'manual_sync' | 'webhook_sync' | 'bulk_sync' | 'validation_fix';
  result: 'success' | 'error' | 'warning';
  details: string;
  timestamp: Date;
}

export class SubscriptionSyncService {
  private syncHistory: SyncHistory[] = [];
  private readonly maxHistorySize = 1000;

  // Adicionar entrada ao histórico
  private addToHistory(entry: Omit<SyncHistory, 'id' | 'timestamp'>): void {
    const historyEntry: SyncHistory = {
      ...entry,
      id: `sync_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date()
    };

    this.syncHistory.unshift(historyEntry);
    
    // Manter apenas os últimos registros
    if (this.syncHistory.length > this.maxHistorySize) {
      this.syncHistory = this.syncHistory.slice(0, this.maxHistorySize);
    }
  }

  // Obter histórico de sincronização
  getSyncHistory(limit: number = 50): SyncHistory[] {
    return this.syncHistory.slice(0, limit);
  }

  // Validar dados de assinatura
  async validateSubscriptionData(subscription: Subscription): Promise<ValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Validações obrigatórias
    if (!subscription.storeId) {
      errors.push('Store ID é obrigatório');
    }

    if (!subscription.planType || !['free', 'premium'].includes(subscription.planType)) {
      errors.push('Tipo de plano inválido');
    }

    if (!subscription.status || !['active', 'past_due', 'canceled', 'unpaid', 'incomplete'].includes(subscription.status)) {
      errors.push('Status da assinatura inválido');
    }

    // Validações de datas para planos premium
    if (subscription.planType === 'premium') {
      if (!subscription.currentPeriodStart) {
        warnings.push('Data de início do período não definida para plano premium');
      }

      if (!subscription.currentPeriodEnd) {
        warnings.push('Data de fim do período não definida para plano premium');
      }

      if (subscription.currentPeriodStart && subscription.currentPeriodEnd) {
        if (subscription.currentPeriodStart >= subscription.currentPeriodEnd) {
          errors.push('Data de início deve ser anterior à data de fim do período');
        }
      }

      // Validar trial_end se existir
      if (subscription.trialEnd) {
        const now = new Date();
        if (subscription.trialEnd < now && subscription.status === 'active') {
          warnings.push('Trial expirado mas assinatura ainda ativa');
        }
      }
    }

    // Validações do Stripe
    if (subscription.planType === 'premium' && !subscription.stripeSubscriptionId) {
      warnings.push('Plano premium sem ID de assinatura do Stripe');
    }

    if (subscription.stripeSubscriptionId && !subscription.stripeCustomerId) {
      warnings.push('Assinatura do Stripe sem ID de cliente');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  // Sincronização manual de uma assinatura específica
  async manualSyncSubscription(subscriptionId: string, forceUpdate: boolean = false): Promise<SyncResult> {
    const startTime = Date.now();
    
    try {
      console.log(`🔄 Iniciando sincronização manual: ${subscriptionId}`);

      if (!isStripeConfigured()) {
        const error = 'Stripe não configurado';
        this.addToHistory({
          subscriptionId,
          localId: 0,
          operation: 'manual_sync',
          result: 'error',
          details: error
        });
        return { success: false, error, timestamp: new Date() };
      }

      // Buscar assinatura no Stripe
      const stripeSubscription = await getSubscription(subscriptionId);
      if (!stripeSubscription) {
        const error = `Assinatura não encontrada no Stripe: ${subscriptionId}`;
        this.addToHistory({
          subscriptionId,
          localId: 0,
          operation: 'manual_sync',
          result: 'error',
          details: error
        });
        return { success: false, error, timestamp: new Date() };
      }

      console.log(`✅ Assinatura encontrada no Stripe:`, {
        id: stripeSubscription.id,
        status: stripeSubscription.status,
        trial_end: stripeSubscription.trial_end,
        metadata: stripeSubscription.metadata
      });

      // Encontrar assinatura local
      let localSubscription = await storage.getSubscriptionByStripeId(subscriptionId);
      
      // Se não encontrou pela ID do Stripe, tentar encontrar pela metadata
      if (!localSubscription && stripeSubscription.metadata?.storeId) {
        const storeId = parseInt(stripeSubscription.metadata.storeId);
        console.log(`🔍 Buscando assinatura local pela loja ${storeId}...`);
        
        const storeSubscriptions = await storage.getSubscriptionsByStoreId(storeId);
        localSubscription = storeSubscriptions.find(sub =>
          sub.status === 'active' &&
          !sub.stripeSubscriptionId &&
          sub.stripeCustomerId === stripeSubscription.customer
        );

        if (localSubscription) {
          console.log(`✅ Assinatura local encontrada pela loja ${storeId}`);
          // Atualizar com Stripe ID
          await storage.updateSubscription(localSubscription.id, {
            stripeSubscriptionId: subscriptionId,
          });
          localSubscription = await storage.getSubscription(localSubscription.id);
        }
      }

      if (!localSubscription) {
        const error = `Assinatura local não encontrada para Stripe ID: ${subscriptionId}`;
        this.addToHistory({
          subscriptionId,
          localId: 0,
          operation: 'manual_sync',
          result: 'error',
          details: error
        });
        return { success: false, error, timestamp: new Date() };
      }

      // Verificar se precisa atualizar
      const changes: string[] = [];
      const currentData = localSubscription;

      // Determinar tipo de plano
      let planType: PlanType = 'free';
      if (stripeSubscription.items.data.length > 0) {
        const priceId = stripeSubscription.items.data[0].price.id;
        if (priceId === STRIPE_CONFIG.premiumMonthlyPriceId || priceId === STRIPE_CONFIG.premiumYearlyPriceId) {
          planType = 'premium';
        }
      }

      // Mapear status
      const statusMap: Record<string, SubscriptionStatus> = {
        'active': 'active',
        'past_due': 'past_due',
        'canceled': 'canceled',
        'unpaid': 'unpaid',
        'incomplete': 'incomplete',
        'incomplete_expired': 'canceled',
        'trialing': 'active',
      };
      const status = statusMap[stripeSubscription.status] || 'canceled';

      // Preparar dados de atualização
      const updateData = {
        planType,
        status,
        currentPeriodStart: new Date(stripeSubscription.current_period_start * 1000),
        currentPeriodEnd: new Date(stripeSubscription.current_period_end * 1000),
        trialEnd: stripeSubscription.trial_end ? new Date(stripeSubscription.trial_end * 1000) : null,
        cancelAtPeriodEnd: stripeSubscription.cancel_at_period_end,
      };

      // Detectar mudanças
      if (currentData.planType !== updateData.planType) {
        changes.push(`planType: ${currentData.planType} → ${updateData.planType}`);
      }
      if (currentData.status !== updateData.status) {
        changes.push(`status: ${currentData.status} → ${updateData.status}`);
      }
      if (currentData.trialEnd?.getTime() !== updateData.trialEnd?.getTime()) {
        changes.push(`trialEnd: ${currentData.trialEnd} → ${updateData.trialEnd}`);
      }

      // Atualizar apenas se houver mudanças ou forçar atualização
      if (changes.length > 0 || forceUpdate) {
        console.log(`📝 Atualizando assinatura local:`, updateData);
        await storage.updateSubscription(localSubscription.id, updateData);

        this.addToHistory({
          subscriptionId,
          localId: localSubscription.id,
          operation: 'manual_sync',
          result: 'success',
          details: `Sincronização bem-sucedida. Mudanças: ${changes.join(', ') || 'Nenhuma mudança, atualização forçada'}`
        });

        console.log(`✅ Sincronização manual concluída: ${subscriptionId}`);
        return {
          success: true,
          subscriptionId,
          localId: localSubscription.id,
          changes,
          timestamp: new Date()
        };
      } else {
        this.addToHistory({
          subscriptionId,
          localId: localSubscription.id,
          operation: 'manual_sync',
          result: 'success',
          details: 'Nenhuma mudança detectada'
        });

        console.log(`ℹ️ Nenhuma mudança detectada para: ${subscriptionId}`);
        return {
          success: true,
          subscriptionId,
          localId: localSubscription.id,
          changes: [],
          timestamp: new Date()
        };
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
      console.error(`❌ Erro na sincronização manual:`, error);
      
      this.addToHistory({
        subscriptionId,
        localId: 0,
        operation: 'manual_sync',
        result: 'error',
        details: errorMessage
      });

      return {
        success: false,
        error: errorMessage,
        timestamp: new Date()
      };
    }
  }

  // Sincronização em lote para uma loja específica
  async bulkSyncStore(storeId: number, options: { forceUpdate?: boolean, maxConcurrent?: number } = {}): Promise<BulkSyncResult> {
    const startTime = Date.now();
    const { forceUpdate = false, maxConcurrent = 3 } = options;

    console.log(`🔄 Iniciando sincronização em lote para loja ${storeId}`);

    try {
      // Buscar todas as assinaturas da loja
      const localSubscriptions = await storage.getSubscriptionsByStoreId(storeId);
      const stripeSubscriptions = localSubscriptions.filter(sub => sub.stripeSubscriptionId);

      if (stripeSubscriptions.length === 0) {
        console.log(`ℹ️ Nenhuma assinatura do Stripe encontrada para loja ${storeId}`);
        return {
          totalProcessed: 0,
          successful: 0,
          failed: 0,
          results: [],
          duration: Date.now() - startTime
        };
      }

      console.log(`📊 Encontradas ${stripeSubscriptions.length} assinaturas para sincronizar`);

      // Processar em lotes para evitar sobrecarga
      const results: SyncResult[] = [];
      const batches = [];

      for (let i = 0; i < stripeSubscriptions.length; i += maxConcurrent) {
        batches.push(stripeSubscriptions.slice(i, i + maxConcurrent));
      }

      for (const batch of batches) {
        const batchPromises = batch.map(sub =>
          this.manualSyncSubscription(sub.stripeSubscriptionId!, forceUpdate)
        );

        const batchResults = await Promise.allSettled(batchPromises);

        for (const result of batchResults) {
          if (result.status === 'fulfilled') {
            results.push(result.value);
          } else {
            results.push({
              success: false,
              error: result.reason?.message || 'Erro desconhecido',
              timestamp: new Date()
            });
          }
        }
      }

      const successful = results.filter(r => r.success).length;
      const failed = results.filter(r => !r.success).length;

      this.addToHistory({
        subscriptionId: `bulk_store_${storeId}`,
        localId: storeId,
        operation: 'bulk_sync',
        result: failed === 0 ? 'success' : (successful > 0 ? 'warning' : 'error'),
        details: `Processadas: ${results.length}, Sucesso: ${successful}, Falhas: ${failed}`
      });

      console.log(`✅ Sincronização em lote concluída para loja ${storeId}: ${successful}/${results.length} sucessos`);

      return {
        totalProcessed: results.length,
        successful,
        failed,
        results,
        duration: Date.now() - startTime
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
      console.error(`❌ Erro na sincronização em lote:`, error);

      this.addToHistory({
        subscriptionId: `bulk_store_${storeId}`,
        localId: storeId,
        operation: 'bulk_sync',
        result: 'error',
        details: errorMessage
      });

      return {
        totalProcessed: 0,
        successful: 0,
        failed: 1,
        results: [{
          success: false,
          error: errorMessage,
          timestamp: new Date()
        }],
        duration: Date.now() - startTime
      };
    }
  }

  // Validar e corrigir dados de assinatura
  async validateAndFixSubscription(subscriptionId: number): Promise<SyncResult> {
    try {
      console.log(`🔍 Validando assinatura local ID: ${subscriptionId}`);

      const subscription = await storage.getSubscription(subscriptionId);
      if (!subscription) {
        return {
          success: false,
          error: 'Assinatura não encontrada',
          timestamp: new Date()
        };
      }

      const validation = await this.validateSubscriptionData(subscription);
      const changes: string[] = [];

      if (!validation.isValid) {
        console.log(`❌ Validação falhou:`, validation.errors);

        // Se tem Stripe ID, tentar sincronizar com Stripe
        if (subscription.stripeSubscriptionId) {
          console.log(`🔄 Tentando corrigir via sincronização com Stripe...`);
          return await this.manualSyncSubscription(subscription.stripeSubscriptionId, true);
        } else {
          // Aplicar correções básicas para assinaturas gratuitas
          const fixes: Partial<Subscription> = {};

          if (!subscription.planType || !['free', 'premium'].includes(subscription.planType)) {
            fixes.planType = 'free';
            changes.push('planType corrigido para "free"');
          }

          if (!subscription.status || !['active', 'past_due', 'canceled', 'unpaid', 'incomplete'].includes(subscription.status)) {
            fixes.status = 'active';
            changes.push('status corrigido para "active"');
          }

          if (Object.keys(fixes).length > 0) {
            await storage.updateSubscription(subscriptionId, fixes);
            changes.push('Correções aplicadas');
          }
        }
      }

      this.addToHistory({
        subscriptionId: subscription.stripeSubscriptionId || `local_${subscriptionId}`,
        localId: subscriptionId,
        operation: 'validation_fix',
        result: validation.isValid ? 'success' : 'warning',
        details: validation.isValid ? 'Validação passou' : `Correções: ${changes.join(', ')}`
      });

      return {
        success: true,
        localId: subscriptionId,
        changes,
        timestamp: new Date()
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
      console.error(`❌ Erro na validação:`, error);

      return {
        success: false,
        error: errorMessage,
        timestamp: new Date()
      };
    }
  }

  // Obter estatísticas de sincronização
  getSyncStats(): {
    totalSyncs: number;
    successfulSyncs: number;
    failedSyncs: number;
    lastSync?: Date;
    recentErrors: string[];
  } {
    const recentHistory = this.syncHistory.slice(0, 100);
    const totalSyncs = recentHistory.length;
    const successfulSyncs = recentHistory.filter(h => h.result === 'success').length;
    const failedSyncs = recentHistory.filter(h => h.result === 'error').length;
    const lastSync = recentHistory[0]?.timestamp;
    const recentErrors = recentHistory
      .filter(h => h.result === 'error')
      .slice(0, 5)
      .map(h => h.details);

    return {
      totalSyncs,
      successfulSyncs,
      failedSyncs,
      lastSync,
      recentErrors
    };
  }
}

export const subscriptionSyncService = new SubscriptionSyncService();
