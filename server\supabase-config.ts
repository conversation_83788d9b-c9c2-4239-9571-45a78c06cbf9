import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';

// Carrega variáveis de ambiente do .env
config();

// Obtém as variáveis do Supabase
const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Erro: SUPABASE_URL ou SUPABASE_SERVICE_ROLE_KEY não definidos.');
  console.error('Por favor, defina essas variáveis no arquivo .env');
  process.exit(1);
}

// Cria o cliente Supabase
export const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

export const getSupabaseClient = () => {
  console.log(`Usando Supabase URL: ${supabaseUrl}`);
  return supabase;
};
