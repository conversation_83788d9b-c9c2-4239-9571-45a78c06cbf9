import { createClient } from '@supabase/supabase-js';
import { PostgrestResponse } from '@supabase/postgrest-js';
import 'dotenv/config';

// Obtém as variáveis do Supabase do arquivo .env
const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('ERRO: Supabase URL ou API Key não definidos nas variáveis de ambiente.');
  console.error('Por favor, defina SUPABASE_URL e SUPABASE_SERVICE_ROLE_KEY no arquivo .env');
  process.exit(1);
}

// Inicializar cliente Supabase
console.log(`Conectando ao Supabase: ${supabaseUrl}`);
export const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Função auxiliar para realizar consultas SQL no Supabase
export async function executeSql<T = any>(query: string): Promise<PostgrestResponse<T>> {
  try {
    return await supabase.from('pg_execute_sql').select('*').eq('query', query);
  } catch (error) {
    console.error('Erro ao executar SQL:', error);
    throw error;
  }
}

// Verificar conexão com o banco
export async function testConnection(): Promise<boolean> {
  try {
    console.log('Testando conexão com o Supabase...');
    const { data, error } = await supabase.from('users').select('count').limit(1);
    
    if (error) {
      console.error('Erro na conexão com o Supabase:', error);
      return false;
    }
    
    console.log('Conexão com Supabase estabelecida com sucesso!');
    return true;
  } catch (error) {
    console.error('Erro ao testar conexão com o Supabase:', error);
    return false;
  }
}
