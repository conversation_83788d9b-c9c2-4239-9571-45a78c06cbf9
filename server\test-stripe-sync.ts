import { stripe, isStripeConfigured, getSubscription, STRIPE_CONFIG } from './stripe-config';
import { subscriptionSyncService } from './subscription-sync-service';
import { storage } from './storage';

interface TestResult {
  test: string;
  success: boolean;
  data?: any;
  error?: string;
  duration: number;
}

class StripeDataRetrievalTest {
  private results: TestResult[] = [];

  private async runTest(testName: string, testFunction: () => Promise<any>): Promise<TestResult> {
    const startTime = Date.now();
    console.log(`\n🧪 Executando teste: ${testName}`);
    
    try {
      const data = await testFunction();
      const duration = Date.now() - startTime;
      
      console.log(`✅ ${testName} - Sucesso (${duration}ms)`);
      
      const result: TestResult = {
        test: testName,
        success: true,
        data,
        duration
      };
      
      this.results.push(result);
      return result;
      
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
      
      console.error(`❌ ${testName} - Falha (${duration}ms):`, errorMessage);
      
      const result: TestResult = {
        test: testName,
        success: false,
        error: errorMessage,
        duration
      };
      
      this.results.push(result);
      return result;
    }
  }

  // Teste 1: Verificar configuração do Stripe
  private async testStripeConfiguration(): Promise<any> {
    if (!isStripeConfigured()) {
      throw new Error('Stripe não está configurado corretamente');
    }

    const config = {
      hasSecretKey: !!process.env.STRIPE_SECRET_KEY,
      hasWebhookSecret: !!STRIPE_CONFIG.webhookSecret,
      hasPremiumMonthlyPrice: !!STRIPE_CONFIG.premiumMonthlyPriceId,
      hasPremiumYearlyPrice: !!STRIPE_CONFIG.premiumYearlyPriceId,
      successUrl: STRIPE_CONFIG.successUrl,
      cancelUrl: STRIPE_CONFIG.cancelUrl
    };

    console.log('📋 Configuração do Stripe:', config);
    return config;
  }

  // Teste 2: Testar conexão com Stripe API
  private async testStripeConnection(): Promise<any> {
    if (!stripe) {
      throw new Error('Cliente Stripe não inicializado');
    }

    // Testar listagem de produtos para verificar conectividade
    const products = await stripe.products.list({ limit: 1 });
    
    console.log('🔗 Conexão com Stripe estabelecida');
    console.log(`📦 Produtos encontrados: ${products.data.length}`);
    
    return {
      connected: true,
      productsCount: products.data.length,
      apiVersion: stripe.getApiField('version')
    };
  }

  // Teste 3: Listar assinaturas do Stripe
  private async testListStripeSubscriptions(): Promise<any> {
    if (!stripe) {
      throw new Error('Cliente Stripe não inicializado');
    }

    const subscriptions = await stripe.subscriptions.list({
      limit: 10,
      expand: ['data.customer', 'data.items.data.price']
    });

    console.log(`📋 Assinaturas encontradas no Stripe: ${subscriptions.data.length}`);
    
    const subscriptionDetails = subscriptions.data.map(sub => ({
      id: sub.id,
      status: sub.status,
      customerId: sub.customer,
      trialEnd: sub.trial_end ? new Date(sub.trial_end * 1000) : null,
      currentPeriodStart: new Date(sub.current_period_start * 1000),
      currentPeriodEnd: new Date(sub.current_period_end * 1000),
      planId: sub.items.data[0]?.price?.id,
      metadata: sub.metadata
    }));

    subscriptionDetails.forEach((sub, index) => {
      console.log(`  ${index + 1}. ${sub.id} - Status: ${sub.status} - Trial: ${sub.trialEnd || 'Não'}`);
    });

    return {
      totalSubscriptions: subscriptions.data.length,
      subscriptions: subscriptionDetails
    };
  }

  // Teste 4: Testar recuperação de assinatura específica
  private async testGetSpecificSubscription(subscriptionId?: string): Promise<any> {
    // Se não foi fornecido um ID, pegar o primeiro da lista
    if (!subscriptionId) {
      if (!stripe) throw new Error('Cliente Stripe não inicializado');
      
      const subscriptions = await stripe.subscriptions.list({ limit: 1 });
      if (subscriptions.data.length === 0) {
        throw new Error('Nenhuma assinatura encontrada no Stripe para testar');
      }
      subscriptionId = subscriptions.data[0].id;
    }

    console.log(`🔍 Testando recuperação da assinatura: ${subscriptionId}`);
    
    const subscription = await getSubscription(subscriptionId);
    if (!subscription) {
      throw new Error(`Assinatura ${subscriptionId} não encontrada`);
    }

    const details = {
      id: subscription.id,
      status: subscription.status,
      customerId: subscription.customer,
      trialStart: subscription.trial_start ? new Date(subscription.trial_start * 1000) : null,
      trialEnd: subscription.trial_end ? new Date(subscription.trial_end * 1000) : null,
      currentPeriodStart: new Date(subscription.current_period_start * 1000),
      currentPeriodEnd: new Date(subscription.current_period_end * 1000),
      cancelAtPeriodEnd: subscription.cancel_at_period_end,
      metadata: subscription.metadata,
      items: subscription.items.data.map(item => ({
        priceId: item.price.id,
        productId: item.price.product,
        quantity: item.quantity
      }))
    };

    console.log('📄 Detalhes da assinatura:', JSON.stringify(details, null, 2));
    return details;
  }

  // Teste 5: Testar sincronização manual
  private async testManualSync(subscriptionId?: string): Promise<any> {
    // Se não foi fornecido um ID, pegar o primeiro da lista
    if (!subscriptionId) {
      if (!stripe) throw new Error('Cliente Stripe não inicializado');
      
      const subscriptions = await stripe.subscriptions.list({ limit: 1 });
      if (subscriptions.data.length === 0) {
        throw new Error('Nenhuma assinatura encontrada no Stripe para sincronizar');
      }
      subscriptionId = subscriptions.data[0].id;
    }

    console.log(`🔄 Testando sincronização manual da assinatura: ${subscriptionId}`);
    
    const syncResult = await subscriptionSyncService.manualSyncSubscription(subscriptionId, true);
    
    console.log('📊 Resultado da sincronização:', JSON.stringify(syncResult, null, 2));
    return syncResult;
  }

  // Teste 6: Verificar consistência do banco de dados
  private async testDatabaseConsistency(): Promise<any> {
    console.log('🗄️ Verificando consistência do banco de dados...');
    
    // Buscar todas as assinaturas locais que têm Stripe ID
    const allStores = await storage.getAllStores();
    let totalSubscriptions = 0;
    let validSubscriptions = 0;
    let invalidSubscriptions = 0;
    const issues: string[] = [];

    for (const store of allStores) {
      const subscriptions = await storage.getSubscriptionsByStoreId(store.id);
      
      for (const subscription of subscriptions) {
        totalSubscriptions++;
        
        const validation = await subscriptionSyncService.validateSubscriptionData(subscription);
        
        if (validation.isValid) {
          validSubscriptions++;
        } else {
          invalidSubscriptions++;
          issues.push(`Loja ${store.id} - Assinatura ${subscription.id}: ${validation.errors.join(', ')}`);
        }
      }
    }

    console.log(`📊 Total de assinaturas: ${totalSubscriptions}`);
    console.log(`✅ Válidas: ${validSubscriptions}`);
    console.log(`❌ Inválidas: ${invalidSubscriptions}`);
    
    if (issues.length > 0) {
      console.log('🚨 Problemas encontrados:');
      issues.forEach((issue, index) => {
        console.log(`  ${index + 1}. ${issue}`);
      });
    }

    return {
      totalSubscriptions,
      validSubscriptions,
      invalidSubscriptions,
      issues
    };
  }

  // Executar todos os testes
  async runAllTests(specificSubscriptionId?: string): Promise<TestResult[]> {
    console.log('🚀 Iniciando testes de recuperação de dados do Stripe...\n');
    
    await this.runTest('Configuração do Stripe', () => this.testStripeConfiguration());
    await this.runTest('Conexão com Stripe', () => this.testStripeConnection());
    await this.runTest('Listar Assinaturas', () => this.testListStripeSubscriptions());
    await this.runTest('Recuperar Assinatura Específica', () => this.testGetSpecificSubscription(specificSubscriptionId));
    await this.runTest('Sincronização Manual', () => this.testManualSync(specificSubscriptionId));
    await this.runTest('Consistência do Banco', () => this.testDatabaseConsistency());

    this.printSummary();
    return this.results;
  }

  // Imprimir resumo dos testes
  private printSummary(): void {
    console.log('\n📋 RESUMO DOS TESTES');
    console.log('='.repeat(50));
    
    const totalTests = this.results.length;
    const passedTests = this.results.filter(r => r.success).length;
    const failedTests = totalTests - passedTests;
    
    console.log(`Total de testes: ${totalTests}`);
    console.log(`✅ Passou: ${passedTests}`);
    console.log(`❌ Falhou: ${failedTests}`);
    console.log(`📊 Taxa de sucesso: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    
    if (failedTests > 0) {
      console.log('\n🚨 TESTES QUE FALHARAM:');
      this.results
        .filter(r => !r.success)
        .forEach(result => {
          console.log(`  ❌ ${result.test}: ${result.error}`);
        });
    }
    
    console.log('\n⏱️ TEMPOS DE EXECUÇÃO:');
    this.results.forEach(result => {
      const status = result.success ? '✅' : '❌';
      console.log(`  ${status} ${result.test}: ${result.duration}ms`);
    });
  }

  // Obter resultados
  getResults(): TestResult[] {
    return this.results;
  }
}

// Função para executar os testes
export async function runStripeDataRetrievalTest(subscriptionId?: string): Promise<TestResult[]> {
  const tester = new StripeDataRetrievalTest();
  return await tester.runAllTests(subscriptionId);
}

// Executar se chamado diretamente
// Compatível com CommonJS (cjs) format
if (require.main === module) {
  runStripeDataRetrievalTest()
    .then(results => {
      console.log('\n🎉 Testes concluídos!');
      process.exit(results.every(r => r.success) ? 0 : 1);
    })
    .catch(error => {
      console.error('💥 Erro fatal nos testes:', error);
      process.exit(1);
    });
}
