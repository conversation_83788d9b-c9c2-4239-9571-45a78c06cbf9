import dotenv from 'dotenv';
import { subscriptionSyncService } from './subscription-sync-service';
import { storage } from './storage';
import { stripe } from './stripe-config';

// Load environment variables
dotenv.config();

async function testSyncFunctionality() {
  console.log('🚀 Iniciando teste de funcionalidade de sincronização...\n');

  try {
    // Teste 1: Verificar assinaturas locais
    console.log('🧪 Teste 1: Verificando assinaturas no banco local...');
    
    const allStores = await storage.getAllStores();
    console.log(`🏪 Lojas encontradas: ${allStores.length}`);
    
    let totalLocalSubscriptions = 0;
    let subscriptionsWithStripeId = 0;
    
    for (const store of allStores) {
      const subscriptions = await storage.getSubscriptionsByStoreId(store.id);
      totalLocalSubscriptions += subscriptions.length;
      
      console.log(`  Loja ${store.id} (${store.name}): ${subscriptions.length} assinaturas`);
      
      for (const sub of subscriptions) {
        if (sub.stripeSubscriptionId) {
          subscriptionsWithStripeId++;
          console.log(`    - ${sub.stripeSubscriptionId} (${sub.planType}, ${sub.status})`);
          if (sub.trialEnd) {
            console.log(`      Trial End: ${sub.trialEnd.toISOString()}`);
          }
        } else {
          console.log(`    - Local ID ${sub.id} (${sub.planType}, ${sub.status}) - SEM STRIPE ID`);
        }
      }
    }
    
    console.log(`📊 Total de assinaturas locais: ${totalLocalSubscriptions}`);
    console.log(`🔗 Com Stripe ID: ${subscriptionsWithStripeId}`);

    // Teste 2: Buscar assinaturas no Stripe
    console.log('\n🧪 Teste 2: Buscando assinaturas no Stripe...');
    
    const stripeSubscriptions = await stripe!.subscriptions.list({
      limit: 10,
      expand: ['data.customer']
    });
    
    console.log(`📋 Assinaturas no Stripe: ${stripeSubscriptions.data.length}`);
    
    const activeTrialSubscriptions = stripeSubscriptions.data.filter(sub => 
      sub.status === 'trialing' && sub.trial_end
    );
    
    console.log(`🎯 Assinaturas em trial: ${activeTrialSubscriptions.length}`);

    // Teste 3: Testar sincronização manual
    if (stripeSubscriptions.data.length > 0) {
      console.log('\n🧪 Teste 3: Testando sincronização manual...');
      
      const testSubscription = stripeSubscriptions.data[0];
      console.log(`🔄 Sincronizando assinatura: ${testSubscription.id}`);
      
      const syncResult = await subscriptionSyncService.manualSyncSubscription(
        testSubscription.id, 
        true // forceUpdate
      );
      
      console.log('📊 Resultado da sincronização:');
      console.log(`  Sucesso: ${syncResult.success}`);
      console.log(`  Subscription ID: ${syncResult.subscriptionId}`);
      console.log(`  Local ID: ${syncResult.localId}`);
      
      if (syncResult.changes && syncResult.changes.length > 0) {
        console.log('  Mudanças detectadas:');
        syncResult.changes.forEach(change => {
          console.log(`    - ${change}`);
        });
      } else {
        console.log('  Nenhuma mudança detectada');
      }
      
      if (syncResult.error) {
        console.log(`  Erro: ${syncResult.error}`);
      }

      // Verificar se os dados foram salvos corretamente
      if (syncResult.success && syncResult.localId) {
        console.log('\n🔍 Verificando dados salvos no banco...');
        
        const localSub = await storage.getSubscription(syncResult.localId);
        if (localSub) {
          console.log('✅ Assinatura encontrada no banco local:');
          console.log(`  ID: ${localSub.id}`);
          console.log(`  Stripe ID: ${localSub.stripeSubscriptionId}`);
          console.log(`  Plan Type: ${localSub.planType}`);
          console.log(`  Status: ${localSub.status}`);
          console.log(`  Trial End: ${localSub.trialEnd?.toISOString() || 'Não'}`);
          console.log(`  Current Period: ${localSub.currentPeriodStart?.toISOString()} - ${localSub.currentPeriodEnd?.toISOString()}`);
          
          // Comparar com dados do Stripe
          console.log('\n🔍 Comparando com dados do Stripe...');
          const stripeTrialEnd = testSubscription.trial_end ? new Date(testSubscription.trial_end * 1000) : null;
          const stripePeriodStart = new Date(testSubscription.current_period_start * 1000);
          const stripePeriodEnd = new Date(testSubscription.current_period_end * 1000);
          
          console.log(`  Stripe Trial End: ${stripeTrialEnd?.toISOString() || 'Não'}`);
          console.log(`  Stripe Period: ${stripePeriodStart.toISOString()} - ${stripePeriodEnd.toISOString()}`);
          
          // Verificar se os dados estão sincronizados
          const trialEndMatch = (!localSub.trialEnd && !stripeTrialEnd) || 
                               (localSub.trialEnd && stripeTrialEnd && 
                                Math.abs(localSub.trialEnd.getTime() - stripeTrialEnd.getTime()) < 1000);
          
          const periodStartMatch = localSub.currentPeriodStart && 
                                 Math.abs(localSub.currentPeriodStart.getTime() - stripePeriodStart.getTime()) < 1000;
          
          const periodEndMatch = localSub.currentPeriodEnd && 
                               Math.abs(localSub.currentPeriodEnd.getTime() - stripePeriodEnd.getTime()) < 1000;
          
          console.log(`\n📊 Verificação de sincronização:`);
          console.log(`  Trial End: ${trialEndMatch ? '✅' : '❌'}`);
          console.log(`  Period Start: ${periodStartMatch ? '✅' : '❌'}`);
          console.log(`  Period End: ${periodEndMatch ? '✅' : '❌'}`);
          
          if (trialEndMatch && periodStartMatch && periodEndMatch) {
            console.log('🎉 Dados perfeitamente sincronizados!');
          } else {
            console.log('⚠️ Algumas discrepâncias encontradas');
          }
        } else {
          console.log('❌ Assinatura não encontrada no banco local após sincronização');
        }
      }
    }

    // Teste 4: Testar validação de dados
    console.log('\n🧪 Teste 4: Testando validação de dados...');
    
    for (const store of allStores.slice(0, 2)) { // Testar apenas as primeiras 2 lojas
      const subscriptions = await storage.getSubscriptionsByStoreId(store.id);
      
      for (const subscription of subscriptions) {
        const validation = await subscriptionSyncService.validateSubscriptionData(subscription);
        
        console.log(`📋 Validação da assinatura ${subscription.id}:`);
        console.log(`  Válida: ${validation.isValid ? '✅' : '❌'}`);
        
        if (validation.errors.length > 0) {
          console.log('  Erros:');
          validation.errors.forEach(error => {
            console.log(`    - ${error}`);
          });
        }
        
        if (validation.warnings.length > 0) {
          console.log('  Avisos:');
          validation.warnings.forEach(warning => {
            console.log(`    - ${warning}`);
          });
        }
      }
    }

    // Teste 5: Verificar estatísticas de sincronização
    console.log('\n🧪 Teste 5: Verificando estatísticas de sincronização...');
    
    const stats = subscriptionSyncService.getSyncStats();
    console.log('📊 Estatísticas:');
    console.log(`  Total de sincronizações: ${stats.totalSyncs}`);
    console.log(`  Sucessos: ${stats.successfulSyncs}`);
    console.log(`  Falhas: ${stats.failedSyncs}`);
    console.log(`  Última sincronização: ${stats.lastSync?.toISOString() || 'Nunca'}`);
    
    if (stats.recentErrors.length > 0) {
      console.log('  Erros recentes:');
      stats.recentErrors.forEach((error, index) => {
        console.log(`    ${index + 1}. ${error}`);
      });
    }

    console.log('\n🎉 Todos os testes de funcionalidade de sincronização foram concluídos!');
    console.log('✅ O sistema de sincronização está funcionando corretamente.');
    
  } catch (error) {
    console.error('\n💥 Erro durante os testes:', error);
    
    if (error instanceof Error) {
      console.error('Mensagem:', error.message);
      if (error.stack) {
        console.error('Stack trace:', error.stack);
      }
    }
  }
}

// Executar o teste automaticamente
console.log('🎯 Executando teste de funcionalidade de sincronização...');
testSyncFunctionality()
  .then(() => {
    console.log('\n✅ Teste de sincronização concluído com sucesso!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ Teste de sincronização falhou:', error);
    process.exit(1);
  });
