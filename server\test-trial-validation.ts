/**
 * Script de teste para validação de trial expirado
 * 
 * Este script testa a nova implementação de validação de trial local
 * independente do Stripe, incluindo fallbacks e processamento automático.
 */

import { subscriptionService } from './subscription-service';
import { storage } from './storage';

async function testTrialValidation() {
  console.log('🧪 Iniciando testes de validação de trial...\n');

  try {
    // 1. Testar verificação de saúde do Stripe
    console.log('1️⃣ Testando verificação de saúde do Stripe...');
    const healthStatus = await subscriptionService.checkStripeSyncHealth();
    console.log('   Status:', healthStatus);
    console.log('   ✅ Verificação de saúde concluída\n');

    // 2. Testar processamento de trials expirados
    console.log('2️⃣ Testando processamento de trials expirados...');
    const processedCount = await subscriptionService.processExpiredTrials();
    console.log(`   Trials processados: ${processedCount}`);
    console.log('   ✅ Processamento concluído\n');

    // 3. Testar validação de assinatura ativa
    console.log('3️⃣ Testando validação de assinatura ativa...');
    
    // Buscar algumas lojas para teste
    const stores = await storage.getAllStores();
    const testStores = stores.slice(0, 3); // Testar apenas 3 lojas
    
    for (const store of testStores) {
      console.log(`   Testando loja ${store.id} (${store.name})...`);
      
      try {
        const subscription = await subscriptionService.getActiveSubscription(store.id);
        
        if (subscription) {
          console.log(`     ✅ Assinatura válida encontrada:`);
          console.log(`        - Plano: ${subscription.planType}`);
          console.log(`        - Status: ${subscription.status}`);
          console.log(`        - Trial End: ${subscription.trialEnd || 'N/A'}`);
        } else {
          console.log(`     ⚠️ Nenhuma assinatura válida encontrada`);
        }
      } catch (storeError) {
        console.log(`     ❌ Erro ao testar loja ${store.id}:`, storeError.message);
      }
    }
    console.log('   ✅ Teste de validação concluído\n');

    // 4. Testar informações de uso
    console.log('4️⃣ Testando informações de uso...');
    
    if (testStores.length > 0) {
      const testStore = testStores[0];
      console.log(`   Testando loja ${testStore.id}...`);
      
      try {
        const usageInfo = await subscriptionService.getUsageInfo(testStore.id);
        console.log('     ✅ Informações de uso obtidas:');
        console.log(`        - Produtos: ${usageInfo.usage.products.current}/${usageInfo.usage.products.limit}`);
        console.log(`        - Pedidos: ${usageInfo.usage.orders.current}/${usageInfo.usage.orders.limit}`);
        console.log(`        - Plano: ${usageInfo.subscription?.planType || 'N/A'}`);
      } catch (usageError) {
        console.log(`     ❌ Erro ao obter informações de uso:`, usageError.message);
      }
    }
    console.log('   ✅ Teste de informações de uso concluído\n');

    console.log('🎉 Todos os testes concluídos com sucesso!');
    
  } catch (error) {
    console.error('❌ Erro durante os testes:', error);
  }
}

// Função para simular um trial expirado (apenas para teste)
async function simulateExpiredTrial(storeId: number) {
  console.log(`🔧 Simulando trial expirado para loja ${storeId}...`);
  
  try {
    const subscriptions = await storage.getSubscriptionsByStoreId(storeId);
    const activeSubscription = subscriptions.find(sub => sub.status === 'active');
    
    if (activeSubscription && activeSubscription.planType === 'premium') {
      // Definir trialEnd para ontem
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      
      await storage.updateSubscription(activeSubscription.id, {
        trialEnd: yesterday
      });
      
      console.log(`   ✅ Trial simulado como expirado para loja ${storeId}`);
      console.log(`   Trial End definido para: ${yesterday.toISOString()}`);
    } else {
      console.log(`   ⚠️ Loja ${storeId} não possui assinatura premium ativa para simular`);
    }
  } catch (error) {
    console.error(`   ❌ Erro ao simular trial expirado:`, error);
  }
}

// Função para restaurar trial (desfazer simulação)
async function restoreTrial(storeId: number) {
  console.log(`🔧 Restaurando trial para loja ${storeId}...`);
  
  try {
    const subscriptions = await storage.getSubscriptionsByStoreId(storeId);
    const subscription = subscriptions.find(sub => sub.planType === 'premium');
    
    if (subscription) {
      // Definir trialEnd para amanhã
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      
      await storage.updateSubscription(subscription.id, {
        trialEnd: tomorrow,
        status: 'active'
      });
      
      console.log(`   ✅ Trial restaurado para loja ${storeId}`);
      console.log(`   Trial End definido para: ${tomorrow.toISOString()}`);
    } else {
      console.log(`   ⚠️ Loja ${storeId} não possui assinatura premium para restaurar`);
    }
  } catch (error) {
    console.error(`   ❌ Erro ao restaurar trial:`, error);
  }
}

// Executar testes se chamado diretamente
if (require.main === module) {
  testTrialValidation()
    .then(() => {
      console.log('\n✅ Script de teste concluído');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Erro no script de teste:', error);
      process.exit(1);
    });
}

export {
  testTrialValidation,
  simulateExpiredTrial,
  restoreTrial
};
