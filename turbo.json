{"$schema": "https://turbo.build/schema.json", "ui": "tui", "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["client/src/**", "server/**", "shared/**", "vite.config.ts", "tsconfig.json", "tailwind.config.ts", "postcss.config.js", "package.json"], "outputs": ["dist/**"], "env": ["NODE_ENV", "VITE_SUPABASE_URL", "VITE_SUPABASE_ANON_KEY", "VITE_FIREBASE_API_KEY", "VITE_FIREBASE_AUTH_DOMAIN", "VITE_FIREBASE_PROJECT_ID", "VITE_STRIPE_PUBLISHABLE_KEY"]}, "dev": {"cache": false, "persistent": true, "inputs": ["client/src/**", "server/**", "shared/**", "vite.config.ts", "tsconfig.json"], "env": ["NODE_ENV", "VITE_SUPABASE_URL", "VITE_SUPABASE_ANON_KEY", "VITE_FIREBASE_API_KEY", "VITE_FIREBASE_AUTH_DOMAIN", "VITE_FIREBASE_PROJECT_ID", "VITE_STRIPE_PUBLISHABLE_KEY", "SUPABASE_SERVICE_ROLE_KEY", "STRIPE_SECRET_KEY"]}, "check": {"dependsOn": [], "inputs": ["client/src/**", "server/**", "shared/**", "tsconfig.json"], "outputs": []}, "db:push": {"cache": false, "inputs": ["shared/schema.ts", "drizzle.config.ts"], "env": ["VITE_SUPABASE_URL", "SUPABASE_SERVICE_ROLE_KEY"]}, "start": {"dependsOn": ["build"], "cache": false, "inputs": ["dist/**"], "env": ["NODE_ENV", "VITE_SUPABASE_URL", "SUPABASE_SERVICE_ROLE_KEY", "STRIPE_SECRET_KEY"]}}, "globalEnv": ["NODE_ENV"], "globalDependencies": ["package.json", "tsconfig.json"]}