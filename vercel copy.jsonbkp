{"version": 2, "builds": [{"src": "dist/index.js", "use": "@vercel/node"}, {"src": "dist/public/**/*", "use": "@vercel/static"}], "routes": [{"src": "/api/(.*)", "dest": "/dist/index.js"}, {"src": "/(.*)", "dest": "/dist/public/$1"}, {"src": "/(.*)", "dest": "/dist/public/index.html"}], "buildCommand": "npm run build", "outputDirectory": "dist", "rewrites": [{"source": "/(.*)", "destination": "/index.html"}]}