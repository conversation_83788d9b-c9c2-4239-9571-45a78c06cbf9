{"version": 2, "builds": [{"src": "dist/index.js", "use": "@vercel/node"}, {"src": "dist/public/**/*", "use": "@vercel/static"}], "routes": [{"src": "/api/(.*)", "dest": "/dist/index.js", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"], "headers": {"Access-Control-Allow-Origin": "*", "Access-Control-Allow-Methods": "GET, POST, PUT, PATCH, DELETE, OPTIONS", "Access-Control-Allow-Headers": "Content-Type, Authorization"}}, {"handle": "filesystem"}, {"src": "/(.*)", "dest": "/dist/public/index.html"}], "buildCommand": "npm run build", "outputDirectory": "dist", "functions": {"dist/index.js": {"maxDuration": 30}}, "headers": [{"source": "/dist/public/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/dist/public/index.html", "headers": [{"key": "Cache-Control", "value": "public, max-age=0, must-revalidate"}]}], "env": {"NODE_ENV": "production"}}