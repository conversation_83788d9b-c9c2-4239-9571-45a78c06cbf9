import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";

// Usar process.cwd() para garantir o diretório correto
const projectRoot = process.cwd();

export default defineConfig(async () => {
  const plugins = [
    react(),
  ];

  // Adiciona plugins do Replit apenas em desenvolvimento e se disponíveis
  if (process.env.NODE_ENV !== "production" && process.env.REPL_ID !== undefined) {
    try {
      const runtimeErrorOverlay = await import("@replit/vite-plugin-runtime-error-modal");
      plugins.push(runtimeErrorOverlay.default() as any);

      const cartographerModule = await import("@replit/vite-plugin-cartographer");
      plugins.push(cartographerModule.cartographer() as any);
    } catch (error) {
      console.warn("Replit plugins not available:", error.message);
    }
  }

  return {
    plugins,
    define: {
      // Define process.env para o cliente se necessário
      'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV),
    },
    resolve: {
      alias: {
        "@": path.resolve(projectRoot, "client", "src"),
        "@shared": path.resolve(projectRoot, "shared"),
        "@assets": path.resolve(projectRoot, "attached_assets"),
      },
    },
    root: path.resolve(projectRoot, "client"),
    build: {
      outDir: path.resolve(projectRoot, "dist/public"),
      emptyOutDir: true,
      chunkSizeWarningLimit: 1000, // Aumenta o limite para 1MB
      rollupOptions: {
        output: {
          manualChunks: {
            // Vendor chunks - bibliotecas grandes separadas
            'react-vendor': ['react', 'react-dom'],
            'ui-vendor': [
              '@radix-ui/react-dialog',
              '@radix-ui/react-dropdown-menu',
              '@radix-ui/react-select',
              '@radix-ui/react-tabs',
              '@radix-ui/react-toast',
              '@radix-ui/react-popover',
              '@radix-ui/react-accordion',
              '@radix-ui/react-alert-dialog',
              '@radix-ui/react-checkbox',
              '@radix-ui/react-label',
              '@radix-ui/react-switch',
              '@radix-ui/react-slider',
              '@radix-ui/react-progress',
              '@radix-ui/react-avatar',
              '@radix-ui/react-separator',
              '@radix-ui/react-scroll-area'
            ],
            'charts-vendor': ['recharts', 'd3-scale', 'd3-shape'],
            'form-vendor': ['react-hook-form', '@hookform/resolvers', 'zod'],
            'firebase-vendor': ['firebase/app', 'firebase/auth', 'firebase/firestore'],
            'query-vendor': ['@tanstack/react-query'],
            'icons-vendor': ['lucide-react', 'react-icons'],
            'utils-vendor': ['date-fns', 'clsx', 'tailwind-merge'],
            'pdf-vendor': ['jspdf', 'html2canvas'],
            'motion-vendor': ['framer-motion'],
            'i18n-vendor': ['i18next', 'react-i18next']
          }
        }
      }
    }
  };
});
